apiVersion: apps/v1
kind: Deployment
metadata:
  name: fhem
spec:
  strategy:
    type: Recreate
  replicas: 1
  selector:
    matchLabels:
      app: fhem
  template:
    metadata:
      labels:
        app: fhem
    spec:
      nodeSelector:
        location: home
      initContainers:
        - name: init-config
          image: busybox
          command:
            [
              "sh",
              "-c",
              "cp /tmp/post-init.sh /docker/ && chmod +x /docker/post-init.sh",
            ]
          volumeMounts:
            - name: fhem-config
              mountPath: /tmp/post-init.sh
              subPath: post-init.sh
            - name: docker
              mountPath: /docker
      containers:
        - name: fhem
          # image: ghcr.io/fhem/fhem-docker:3-bullseye
          image: fhem/fhem:3-bullseye
          env:
            - name: LANG
              value: "en_US.UTF-8"
            - name: TZ
              value: "Europe/Prague"
          volumeMounts:
            - name: fhem-config
              mountPath: /tmp/fhem.cfg
              subPath: fhem.cfg
            - name: log
              mountPath: /opt/log
            - name: fhem-temp
              mountPath: /opt/fhem
            - name: docker
              mountPath: /docker
      volumes:
        - name: fhem-config
          configMap:
            name: fhem-config
        - name: log
          emptyDir: {}
        - name: fhem-temp
          emptyDir: {}
        - name: docker
          emptyDir: {}
