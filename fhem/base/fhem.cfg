define my_mqtt MQTT2_CLIENT 172.16.100.80:1883
setuuid my_mqtt 65746656-f33f-d220-0f28-0d7a08c63512c6c7
attr my_mqtt autocreate no
attr my_mqtt clientId fhemclient
#setuuid my_mqtt 5d2b5477-f33f-d220-ad23-6eb27d2e145ccdf0
#attr my_mqtt username fhemclient
#attr my_mqtt keepaliveTimeout 30;
#attr my_mqtt mqttVersion 3.1.1
#attr my_mqtt verbose 5

define mqttlog FileLog log/mqttlog-%Y-%W.log my_mqtt
setuuid mqttlog 5d2e17a8-f33f-d220-3574-7b9188e11e317413

define mqttGeneric MQTT_GENERIC_BRIDGE
setuuid mqttGeneric 5d2b5478-f33f-d220-cfdd-a93f51f0a0ebfe0b
attr mqttGeneric IODev my_mqtt
attr mqttGeneric globalDefaults base={"fhem/$device"}

define CUBE MAXLAN 172.16.100.196
setuuid CUBE 5d2a4402-f33f-d220-02f1-5a141f0351fcb326

define MAX_130bf6 MAX HeatingThermostat 130bf6
setuuid MAX_130bf6 5d2a4403-f33f-d220-89ad-0ace4d8bdb3e849d
attr MAX_130bf6 IODev CUBE
attr MAX_130bf6 alias Topeni pokoj vlevo stresni okno
attr MAX_130bf6 icon hc_wht_regler
attr MAX_130bf6 model HeatingThermostat
attr MAX_130bf6 room MAX
define FileLog_MAX_130bf6 FileLog ./log/MAX_130bf6-%Y.log MAX_130bf6
setuuid FileLog_MAX_130bf6 5d2a4403-f33f-d220-10d5-dca57620616d8bdf
attr FileLog_MAX_130bf6 logtype text
attr FileLog_MAX_130bf6 room MAX

define MAX_1a387c MAX HeatingThermostat 1a387c
setuuid MAX_1a387c 5d2a4404-f33f-d220-e59d-25c00e1c74c24037
attr MAX_1a387c IODev CUBE
attr MAX_1a387c alias Chodba
attr MAX_1a387c icon hc_wht_regler
attr MAX_1a387c model HeatingThermostat
attr MAX_1a387c mqttPublish *:topic={"$base/$reading"}
attr MAX_1a387c mqttSubscribe desiredTemperature:stopic={"fhem/MAX_1a387c/desiredTemperature/set"}
attr MAX_1a387c room MAX
define FileLog_MAX_1a387c FileLog ./log/MAX_1a387c-%Y.log MAX_1a387c
setuuid FileLog_MAX_1a387c 5d2a4404-f33f-d220-cead-6613194a12a81ec3
attr FileLog_MAX_1a387c logtype text
attr FileLog_MAX_1a387c room MAX

define MAX_1a388e MAX HeatingThermostat 1a388e
setuuid MAX_1a388e 5d2a4404-f33f-d220-1f58-ba14588239f84288
attr MAX_1a388e IODev CUBE
attr MAX_1a388e alias Pokoj vpravo
attr MAX_1a388e icon hc_wht_regler
attr MAX_1a388e model HeatingThermostat
attr MAX_1a388e room MAX
define FileLog_MAX_1a388e FileLog ./log/MAX_1a388e-%Y.log MAX_1a388e
setuuid FileLog_MAX_1a388e 5d2a4404-f33f-d220-0050-32c59d6def55f92d
attr FileLog_MAX_1a388e logtype text
attr FileLog_MAX_1a388e room MAX

define MAX_168181 MAX WallMountedThermostat 168181
setuuid MAX_168181 5d2a4404-f33f-d220-509f-c5461a346fa4bf4d
attr MAX_168181 IODev CUBE
attr MAX_168181 alias Pokoj dole
attr MAX_168181 icon max_wandthermostat
attr MAX_168181 keepAuto 1
attr MAX_168181 model WallMountedThermostat
attr MAX_168181 mqttPublish *:topic={"$base/$reading"}
attr MAX_168181 mqttSubscribe desiredTemperature:stopic={"fhem/MAX_168181/desiredTemperature/set"}
attr MAX_168181 room MAX
define FileLog_MAX_168181 FileLog ./log/MAX_168181-%Y.log MAX_168181
setuuid FileLog_MAX_168181 5d2a4404-f33f-d220-3080-091a3494fa20d81a
attr FileLog_MAX_168181 logtype text
attr FileLog_MAX_168181 room MAX

define MAX_13eacb MAX HeatingThermostat 13eacb
setuuid MAX_13eacb 5d2a4404-f33f-d220-ac4b-11c54e9741123f4b
attr MAX_13eacb IODev CUBE
attr MAX_13eacb alias Topeni loznice stresni okno
attr MAX_13eacb icon hc_wht_regler
attr MAX_13eacb model HeatingThermostat
attr MAX_13eacb room MAX
define FileLog_MAX_13eacb FileLog ./log/MAX_13eacb-%Y.log MAX_13eacb
setuuid FileLog_MAX_13eacb 5d2a4404-f33f-d220-6c16-712b54834761c468
attr FileLog_MAX_13eacb logtype text
attr FileLog_MAX_13eacb room MAX

define MAX_131799 MAX HeatingThermostat 131799
setuuid MAX_131799 5d2a4404-f33f-d220-d45e-47d4b3284b024a5e
attr MAX_131799 IODev CUBE
attr MAX_131799 alias Topeni pokoj dole sever
attr MAX_131799 icon hc_wht_regler
attr MAX_131799 model HeatingThermostat
attr MAX_131799 room MAX
define FileLog_MAX_131799 FileLog ./log/MAX_131799-%Y.log MAX_131799
setuuid FileLog_MAX_131799 5d2a4404-f33f-d220-9b4a-cb0bad13879c1197
attr FileLog_MAX_131799 logtype text
attr FileLog_MAX_131799 room MAX

define MAX_1304ef MAX HeatingThermostat 1304ef
setuuid MAX_1304ef 5d2a4404-f33f-d220-a3cf-37b966692acf068b
attr MAX_1304ef IODev CUBE
attr MAX_1304ef alias Topeni pokoj dole ulice
attr MAX_1304ef icon hc_wht_regler
attr MAX_1304ef model HeatingThermostat
attr MAX_1304ef room MAX
define FileLog_MAX_1304ef FileLog ./log/MAX_1304ef-%Y.log MAX_1304ef
setuuid FileLog_MAX_1304ef 5d2a4404-f33f-d220-054e-2ef15a1809ead131
attr FileLog_MAX_1304ef logtype text
attr FileLog_MAX_1304ef room MAX

define MAX_131457 MAX HeatingThermostat 131457
setuuid MAX_131457 5d2a4404-f33f-d220-b38f-90ce29d4611dc6dc
attr MAX_131457 IODev CUBE
attr MAX_131457 alias Topeni kuchyne
attr MAX_131457 icon hc_wht_regler
attr MAX_131457 model HeatingThermostat
attr MAX_131457 room MAX
define FileLog_MAX_131457 FileLog ./log/MAX_131457-%Y.log MAX_131457
setuuid FileLog_MAX_131457 5d2a4404-f33f-d220-3340-5220321c8085eb39
attr FileLog_MAX_131457 logtype text
attr FileLog_MAX_131457 room MAX

define MAX_130dab MAX HeatingThermostat 130dab
setuuid MAX_130dab 5d2a4404-f33f-d220-2453-48a1af68e754b2ed
attr MAX_130dab IODev CUBE
attr MAX_130dab alias Topeni loznice okno server
attr MAX_130dab icon hc_wht_regler
attr MAX_130dab model HeatingThermostat
attr MAX_130dab room MAX
define FileLog_MAX_130dab FileLog ./log/MAX_130dab-%Y.log MAX_130dab
setuuid FileLog_MAX_130dab 5d2a4404-f33f-d220-72d2-285445ee1f4f6117
attr FileLog_MAX_130dab logtype text
attr FileLog_MAX_130dab room MAX

define MAX_14e1dd MAX WallMountedThermostat 14e1dd
setuuid MAX_14e1dd 5d2a4404-f33f-d220-dc0a-78cfd5c64c333244
attr MAX_14e1dd IODev CUBE
attr MAX_14e1dd alias Loznice
attr MAX_14e1dd icon max_wandthermostat
attr MAX_14e1dd keepAuto 1
attr MAX_14e1dd model WallMountedThermostat
attr MAX_14e1dd mqttPublish *:topic={"$base/$reading"}
attr MAX_14e1dd mqttSubscribe desiredTemperature:stopic={"fhem/MAX_14e1dd/desiredTemperature/set"}
attr MAX_14e1dd room MAX
define FileLog_MAX_14e1dd FileLog ./log/MAX_14e1dd-%Y.log MAX_14e1dd
setuuid FileLog_MAX_14e1dd 5d2a4404-f33f-d220-6af7-67dc8b8abcd1cee3
attr FileLog_MAX_14e1dd logtype text
attr FileLog_MAX_14e1dd room MAX

define MAX_14e20e MAX WallMountedThermostat 14e20e
setuuid MAX_14e20e 5d2a4404-f33f-d220-f7ab-e0233de3218e839a
attr MAX_14e20e IODev CUBE
attr MAX_14e20e alias Obyvaci pokoj
attr MAX_14e20e icon max_wandthermostat
attr MAX_14e20e keepAuto 1
attr MAX_14e20e model WallMountedThermostat
attr MAX_14e20e mqttPublish *:topic={"$base/$reading"}
attr MAX_14e20e mqttSubscribe desiredTemperature:stopic={"fhem/MAX_14e20e/desiredTemperature/set"}
attr MAX_14e20e room MAX
define FileLog_MAX_14e20e FileLog ./log/MAX_14e20e-%Y.log MAX_14e20e
setuuid FileLog_MAX_14e20e 5d2a4404-f33f-d220-9df5-cc3531cea97be846
attr FileLog_MAX_14e20e logtype text
attr FileLog_MAX_14e20e room MAX

define MAX_161d33 MAX HeatingThermostat 161d33
setuuid MAX_161d33 5d2a4405-f33f-d220-44b7-d33046d3b7b5070b
attr MAX_161d33 IODev CUBE
attr MAX_161d33 alias Koupelna nahore
attr MAX_161d33 icon hc_wht_regler
attr MAX_161d33 keepAuto 1
attr MAX_161d33 model HeatingThermostat
attr MAX_161d33 mqttPublish *:topic={"$base/$reading"}
attr MAX_161d33 mqttSubscribe desiredTemperature:stopic={"fhem/MAX_161d33/desiredTemperature/set"}
attr MAX_161d33 room MAX
define FileLog_MAX_161d33 FileLog ./log/MAX_161d33-%Y.log MAX_161d33
setuuid FileLog_MAX_161d33 5d2a4405-f33f-d220-c08b-c0bdf02d1d350953
attr FileLog_MAX_161d33 logtype text
attr FileLog_MAX_161d33 room MAX

define MAX_1304f2 MAX HeatingThermostat 1304f2
setuuid MAX_1304f2 5d2a4405-f33f-d220-109f-4f70b086d88f3db2
attr MAX_1304f2 IODev CUBE
attr MAX_1304f2 alias Topení obyvak okno server
attr MAX_1304f2 icon hc_wht_regler
attr MAX_1304f2 model HeatingThermostat
attr MAX_1304f2 room MAX
define FileLog_MAX_1304f2 FileLog ./log/MAX_1304f2-%Y.log MAX_1304f2
setuuid FileLog_MAX_1304f2 5d2a4405-f33f-d220-5218-380358a49f2f66e2
attr FileLog_MAX_1304f2 logtype text
attr FileLog_MAX_1304f2 room MAX

define MAX_130dac MAX HeatingThermostat 130dac
setuuid MAX_130dac 5d2a4405-f33f-d220-2039-c4872dd812f5ff9e
attr MAX_130dac IODev CUBE
attr MAX_130dac alias Topeni pokoj vlevo okno server
attr MAX_130dac icon hc_wht_regler
attr MAX_130dac model HeatingThermostat
attr MAX_130dac room MAX
define FileLog_MAX_130dac FileLog ./log/MAX_130dac-%Y.log MAX_130dac
setuuid FileLog_MAX_130dac 5d2a4405-f33f-d220-a4dd-0d6099369d871060
attr FileLog_MAX_130dac logtype text
attr FileLog_MAX_130dac room MAX

define MAX_131f32 MAX HeatingThermostat 131f32
setuuid MAX_131f32 5d2a4405-f33f-d220-c939-de4c64e8c193f12e
attr MAX_131f32 IODev CUBE
attr MAX_131f32 alias Topeni obyvak terasa
attr MAX_131f32 icon hc_wht_regler
attr MAX_131f32 model HeatingThermostat
attr MAX_131f32 room MAX
define FileLog_MAX_131f32 FileLog ./log/MAX_131f32-%Y.log MAX_131f32
setuuid FileLog_MAX_131f32 5d2a4405-f33f-d220-f1cf-58fc55fe542fb937
attr FileLog_MAX_131f32 logtype text
attr FileLog_MAX_131f32 room MAX

define MAX_185949 MAX WallMountedThermostat 185949
setuuid MAX_185949 5d2a4405-f33f-d220-7a4c-e58db3bfa5b42270
attr MAX_185949 IODev CUBE
attr MAX_185949 alias Pokoj vpravo
attr MAX_185949 icon max_wandthermostat
attr MAX_185949 keepAuto 1
attr MAX_185949 model WallMountedThermostat
attr MAX_185949 mqttPublish *:topic={"$base/$reading"}
attr MAX_185949 mqttSubscribe desiredTemperature:stopic={"fhem/MAX_185949/desiredTemperature/set"}
attr MAX_185949 room MAX
define FileLog_MAX_185949 FileLog ./log/MAX_185949-%Y.log MAX_185949
setuuid FileLog_MAX_185949 5d2a4405-f33f-d220-796a-8a733194285d7e85
attr FileLog_MAX_185949 logtype text
attr FileLog_MAX_185949 room MAX

define MAX_14ec88 MAX WallMountedThermostat 14ec88
setuuid MAX_14ec88 5d2a4405-f33f-d220-8d05-400173f88d6c92a5
attr MAX_14ec88 IODev CUBE
attr MAX_14ec88 alias Pokoj vlevo
attr MAX_14ec88 icon max_wandthermostat
attr MAX_14ec88 keepAuto 1
attr MAX_14ec88 model WallMountedThermostat
attr MAX_14ec88 mqttPublish *:topic={"$base/$reading"}
attr MAX_14ec88 mqttSubscribe desiredTemperature:stopic={"fhem/MAX_14ec88/desiredTemperature/set"}
attr MAX_14ec88 room MAX
define FileLog_MAX_14ec88 FileLog ./log/MAX_14ec88-%Y.log MAX_14ec88
setuuid FileLog_MAX_14ec88 5d2a4405-f33f-d220-191e-4b334264cc3ec605
attr FileLog_MAX_14ec88 logtype text
attr FileLog_MAX_14ec88 room MAX

define MAX_17c50f MAX HeatingThermostatPlus 17c50f
setuuid MAX_17c50f 5d2e17ad-f33f-d220-0779-ee4854366899fce6
attr MAX_17c50f IODev CUBE
attr MAX_17c50f alias Topeni koupelna dole
attr MAX_17c50f icon hc_wht_regler
attr MAX_17c50f keepAuto 1
attr MAX_17c50f model HeatingThermostatPlus
attr MAX_17c50f mqttPublish *:topic={"$base/$reading"}
attr MAX_17c50f mqttSubscribe desiredTemperature:stopic={"fhem/MAX_17c50f/desiredTemperature/set"}
attr MAX_17c50f room MAX
define FileLog_MAX_17c50f FileLog ./log/MAX_17c50f-%Y.log MAX_17c50f
setuuid FileLog_MAX_17c50f 5d2e17ad-f33f-d220-bcf9-c1cd326100c7dc4e
attr FileLog_MAX_17c50f logtype text
attr FileLog_MAX_17c50f room MAX
define MAX_1a124c MAX HeatingThermostat 1a124c
setuuid MAX_1a124c 6574665a-f33f-d220-2ecf-059e7b79c3b1b9f5
attr MAX_1a124c IODev CUBE
attr MAX_1a124c model HeatingThermostat
attr MAX_1a124c room MAX
define FileLog_MAX_1a124c FileLog ./log/MAX_1a124c-%Y.log MAX_1a124c
setuuid FileLog_MAX_1a124c 6574665a-f33f-d220-0a62-c315241ddf5d97e5
attr FileLog_MAX_1a124c logtype text
attr FileLog_MAX_1a124c room MAX

