apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: fhem-ingress
  annotations:
    cert-manager.io/cluster-issuer: letsencrypt-cloudflare-sklenarovi
spec:
  rules:
    - host: fhem.k8s.sklenarovi.cz
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: fhem
                port:
                  number: 8083
