#!/bin/sh

# Source and destination files
SOURCE_FILE="/tmp/fhem.cfg"
DESTINATION_FILE="/opt/fhem/fhem.cfg"

echo "Copying content from $SOURCE_FILE to $DESTINATION_FILE"

# Check if the source file exists
if [ -f "$SOURCE_FILE" ]; then
    # Append content from source file to destination file
    cat "$SOURCE_FILE" >> "$DESTINATION_FILE"
    echo "Content appended successfully."
else
    echo "Source file not found."
fi
