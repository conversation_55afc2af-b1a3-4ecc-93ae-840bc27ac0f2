#!/bin/bash
# Auto-fix YAML formatting issues in the GitOps repository

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo "🔧 Running YAML formatting fixes..."

# Check for required tools
check_tool() {
    if ! command -v "$1" &> /dev/null; then
        echo -e "${RED}❌ $1 is not installed${NC}"
        echo "   Install with: $2"
        return 1
    else
        echo -e "${GREEN}✓ $1 is available${NC}"
        return 0
    fi
}

echo -e "\n${YELLOW}Checking required tools...${NC}"
MISSING_TOOLS=0

check_tool "yamllint" "brew install yamllint" || MISSING_TOOLS=1
check_tool "yq" "brew install yq" || MISSING_TOOLS=1

# Optional prettier for more advanced formatting
if command -v prettier &> /dev/null; then
    echo -e "${GREEN}✓ prettier is available (will use for better formatting)${NC}"
    HAS_PRETTIER=1
else
    echo -e "${YELLOW}⚠️  prettier not found (optional for better formatting)${NC}"
    echo "   Install with: npm install -g prettier"
    HAS_PRETTIER=0
fi

if [ $MISSING_TOOLS -eq 1 ]; then
    echo -e "\n${RED}Some required tools are missing. Please install them first.${NC}"
    exit 1
fi

# Create yamllint config if not exists
YAMLLINT_CONFIG=".yamllint.yml"
if [ ! -f "$YAMLLINT_CONFIG" ]; then
    echo -e "\n${YELLOW}Creating default yamllint config...${NC}"
    cat > "$YAMLLINT_CONFIG" << 'EOF'
extends: default
rules:
  line-length:
    max: 150
    level: warning
  comments:
    min-spaces-from-content: 1
  document-start: disable
  brackets:
    min-spaces-inside: 0
    max-spaces-inside: 1
  braces:
    min-spaces-inside: 0
    max-spaces-inside: 1
  colons:
    max-spaces-after: 1
  commas:
    max-spaces-after: 1
  empty-lines:
    max: 2
  indentation:
    spaces: 2
    indent-sequences: true
  truthy:
    level: warning
EOF
fi

# Function to fix YAML file
fix_yaml_file() {
    local file="$1"
    local temp_file="${file}.tmp"
    local backup_file="${file}.bak"
    
    echo -n "  Fixing $file... "
    
    # Create backup
    cp "$file" "$backup_file"
    
    # First, check if file is valid YAML
    if ! yq eval '.' "$file" > /dev/null 2>&1; then
        echo -e "${RED}Failed (invalid YAML syntax)${NC}"
        rm "$backup_file"
        return 1
    fi
    
    # Use yq to fix indentation and formatting while preserving content
    # The -P flag ensures proper YAML output format
    # The '.' expression preserves all content
    if yq eval '.' -P -I 2 "$file" > "$temp_file" 2>/dev/null; then
        # Verify the output has content
        if [ -s "$temp_file" ]; then
            # Double-check that we didn't lose content by comparing line counts
            original_lines=$(grep -v '^[[:space:]]*#' "$file" | grep -v '^[[:space:]]*$' | wc -l)
            new_lines=$(grep -v '^[[:space:]]*#' "$temp_file" | grep -v '^[[:space:]]*$' | wc -l)
            
            # Allow for small differences due to formatting changes
            if [ $((original_lines - new_lines)) -gt 5 ] || [ $((new_lines - original_lines)) -gt 5 ]; then
                echo -e "${RED}Failed (content loss detected)${NC}"
                rm -f "$temp_file" "$backup_file"
                return 1
            fi
            
            # If prettier is available and file is not a Kubernetes manifest, use it for final formatting
            if [ $HAS_PRETTIER -eq 1 ] && [[ ! "$file" =~ (deployment|service|ingress|statefulset|daemonset|configmap|secret|pvc|networkpolicy)\.yaml$ ]]; then
                if prettier --write "$temp_file" --parser yaml --print-width 120 2>/dev/null; then
                    mv "$temp_file" "$file"
                else
                    # Fallback to yq output if prettier fails
                    mv "$temp_file" "$file"
                fi
            else
                mv "$temp_file" "$file"
            fi
            
            # Check if file actually changed
            if diff -q "$backup_file" "$file" > /dev/null; then
                rm "$backup_file"
                echo -e "${BLUE}No changes needed${NC}"
            else
                rm "$backup_file"
                echo -e "${GREEN}Fixed${NC}"
                return 0
            fi
        else
            echo -e "${RED}Failed (empty output)${NC}"
            rm -f "$temp_file" "$backup_file"
            return 1
        fi
    else
        echo -e "${RED}Failed (yq error)${NC}"
        rm -f "$temp_file" "$backup_file"
        return 1
    fi
}

# Find and fix all YAML files
echo -e "\n${YELLOW}Fixing YAML files...${NC}"

# Use process substitution to avoid subshell issues with counters
FIXED_COUNT=0
FAILED_COUNT=0
TOTAL_COUNT=0

# Create temporary file for tracking counts
COUNT_FILE=$(mktemp)
echo "0 0 0" > "$COUNT_FILE"

# Get all YAML files excluding certain directories
while IFS= read -r yaml_file; do
    TOTAL_COUNT=$((TOTAL_COUNT + 1))
    
    # Skip files that yamllint reports as having errors
    if yamllint -c "$YAMLLINT_CONFIG" "$yaml_file" > /dev/null 2>&1; then
        # File is already valid, skip
        continue
    fi
    
    if fix_yaml_file "$yaml_file"; then
        # Verify the fix
        if yamllint -c "$YAMLLINT_CONFIG" "$yaml_file" > /dev/null 2>&1; then
            FIXED_COUNT=$((FIXED_COUNT + 1))
        else
            FAILED_COUNT=$((FAILED_COUNT + 1))
            echo -e "    ${YELLOW}⚠️  Still has issues after fix attempt${NC}"
        fi
    else
        FAILED_COUNT=$((FAILED_COUNT + 1))
    fi
    
    # Update count file
    echo "$FIXED_COUNT $FAILED_COUNT $TOTAL_COUNT" > "$COUNT_FILE"
done < <(find . \( -name '*.yaml' -o -name '*.yml' \) \
    -not -path "./.git/*" \
    -not -path "./node_modules/*" \
    -not -path "./.github/*" \
    -not -path "*/charts/*/templates/*" \
    -type f)

# Read final counts
read -r FIXED_COUNT FAILED_COUNT TOTAL_COUNT < "$COUNT_FILE"
rm -f "$COUNT_FILE"

# Note: Kubernetes manifests are handled by the general YAML fixing above
# No special processing needed as yq preserves the structure properly

# Run validation to show results
echo -e "\n${YELLOW}Running validation to verify fixes...${NC}"
if [ -f "$YAMLLINT_CONFIG" ]; then
    yamllint -c "$YAMLLINT_CONFIG" . || true
else
    yamllint . || true
fi

# Summary
echo -e "\n${YELLOW}================================${NC}"
echo -e "${YELLOW}Formatting Summary${NC}"
echo -e "${YELLOW}================================${NC}"

echo -e "Total YAML files found: $TOTAL_COUNT"
echo -e "Successfully fixed: ${GREEN}$FIXED_COUNT${NC}"
echo -e "Failed to fix: ${RED}$FAILED_COUNT${NC}"

if [ "$FAILED_COUNT" -eq 0 ] && [ "$FIXED_COUNT" -gt 0 ]; then
    echo -e "\n${GREEN}✨ All YAML files formatted successfully!${NC}"
elif [ "$FAILED_COUNT" -gt 0 ]; then
    echo -e "\n${YELLOW}⚠️  Some files could not be automatically fixed${NC}"
    echo -e "   Run 'yamllint -c $YAMLLINT_CONFIG .' to see remaining issues"
fi

echo -e "\n${BLUE}Tips:${NC}"
echo "  - Review changes with: git diff"
echo "  - Run validation with: ./scripts/validate-local.sh"
echo "  - For manual fixes, use: yq eval '.' -P <file> > <file>.fixed"

# Create prettier config if not exists and prettier is available
if [ $HAS_PRETTIER -eq 1 ] && [ ! -f ".prettierrc.yaml" ]; then
    echo -e "\n${YELLOW}Creating prettier config for future use...${NC}"
    cat > ".prettierrc.yaml" << 'EOF'
# Prettier configuration for YAML files
singleQuote: false
bracketSpacing: true
printWidth: 120
tabWidth: 2
useTabs: false
overrides:
  - files: "*.{yml,yaml}"
    options:
      singleQuote: false
EOF
fi