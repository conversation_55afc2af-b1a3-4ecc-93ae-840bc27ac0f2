#!/bin/bash
# Local validation script for GitOps repository

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

echo "🔍 Running local GitOps validation..."

# Check for required tools
check_tool() {
    if ! command -v "$1" &> /dev/null; then
        echo -e "${RED}❌ $1 is not installed${NC}"
        echo "   Install with: $2"
        return 1
    else
        echo -e "${GREEN}✓ $1 is available${NC}"
        return 0
    fi
}

echo -e "\n${YELLOW}Checking required tools...${NC}"
MISSING_TOOLS=0

check_tool "yamllint" "brew install yamllint" || MISSING_TOOLS=1
check_tool "kustomize" "brew install kustomize" || MISSING_TOOLS=1
check_tool "kubeconform" "brew install yannh/tap/kubeconform" || MISSING_TOOLS=1
check_tool "yq" "brew install yq" || MISSING_TOOLS=1

if [ $MISSING_TOOLS -eq 1 ]; then
    echo -e "\n${RED}Some tools are missing. Please install them to run all validations.${NC}"
    exit 1
fi

# YAML Validation
echo -e "\n${YELLOW}Running YAML validation...${NC}"
if [ -f ".yamllint.yml" ]; then
    if yamllint -c .yamllint.yml .; then
        echo -e "${GREEN}✓ YAML validation passed${NC}"
    else
        echo -e "${RED}❌ YAML validation failed${NC}"
    fi
else
    if yamllint -c <(echo 'extends: default
rules:
  line-length:
    max: 150
  comments:
    min-spaces-from-content: 1
  document-start: disable') .; then
        echo -e "${GREEN}✓ YAML validation passed${NC}"
    else
        echo -e "${RED}❌ YAML validation failed${NC}"
    fi
fi

# Kubernetes manifest validation
echo -e "\n${YELLOW}Running Kubernetes manifest validation...${NC}"
find . -name '*.yaml' -o -name '*.yml' | \
    grep -E '(deployment|service|ingress|statefulset|daemonset|configmap|secret|pvc|networkpolicy)\.yaml$' | \
    grep -v ".github" | \
    grep -v "/chart/templates/" | \
    grep -v "/charts/" | \
    xargs kubeconform -summary -kubernetes-version 1.29.0 || true

# ArgoCD application validation
echo -e "\n${YELLOW}Validating ArgoCD applications...${NC}"
ARGO_ERRORS=0
for app in apps/application-*.yaml; do
    if [ -f "$app" ]; then
        echo -n "  Checking $(basename "$app")... "
        
        # Check API version
        if ! yq eval '.apiVersion' "$app" | grep -q "argoproj.io/v1alpha1"; then
            echo -e "${RED}Invalid API version${NC}"
            ARGO_ERRORS=$((ARGO_ERRORS + 1))
            continue
        fi
        
        # Check source path exists
        SOURCE_PATH=$(yq eval '.spec.source.path // ""' "$app" 2>/dev/null)
        if [ -n "$SOURCE_PATH" ] && [ "$SOURCE_PATH" != "null" ] && [ "$SOURCE_PATH" != "" ]; then
            if [ ! -d "$SOURCE_PATH" ]; then
                echo -e "${RED}Source path $SOURCE_PATH does not exist${NC}"
                ARGO_ERRORS=$((ARGO_ERRORS + 1))
                continue
            fi
        fi
        
        echo -e "${GREEN}OK${NC}"
    fi
done

if [ $ARGO_ERRORS -gt 0 ]; then
    echo -e "${RED}❌ Found $ARGO_ERRORS ArgoCD application errors${NC}"
else
    echo -e "${GREEN}✓ All ArgoCD applications valid${NC}"
fi

# Kustomize validation
echo -e "\n${YELLOW}Running Kustomize build validation...${NC}"
KUSTOMIZE_ERRORS=0
find . -name "kustomization.yaml" -type f | while read -r kustomization; do
    dir=$(dirname "$kustomization")
    echo -n "  Building $dir... "
    if kustomize build "$dir" > /dev/null 2>&1; then
        echo -e "${GREEN}OK${NC}"
    else
        echo -e "${RED}FAILED${NC}"
        KUSTOMIZE_ERRORS=$((KUSTOMIZE_ERRORS + 1))
    fi
done

# Network policy check
echo -e "\n${YELLOW}Checking network policies...${NC}"
MISSING_POLICIES=0
for app in apps/application-*.yaml; do
    if [ -f "$app" ]; then
        app_name=$(basename "$app" | sed 's/application-//' | sed 's/.yaml//')
        
        # Skip certain apps
        case "$app_name" in
            prepare-apps|network-policies|metallb)
                continue
                ;;
        esac
        
        # Extract namespace
        namespace=$(grep -A5 "destination:" "$app" 2>/dev/null | grep "namespace:" | awk '{print $2}' | tr -d '"' || echo "")
        
        if [ -n "$namespace" ] && [ "$namespace" != "null" ] && [ "$namespace" != "" ]; then
            policy_file="network-policies/ns_${namespace}.yaml"
            if [ ! -f "$policy_file" ]; then
                echo -e "${YELLOW}  ⚠️  No network policy for namespace: $namespace${NC}"
                MISSING_POLICIES=$((MISSING_POLICIES + 1))
            fi
        fi
    fi
done

if [ $MISSING_POLICIES -gt 0 ]; then
    echo -e "${YELLOW}⚠️  Missing $MISSING_POLICIES network policies${NC}"
else
    echo -e "${GREEN}✓ All namespaces have network policies${NC}"
fi

# Summary
echo -e "\n${YELLOW}================================${NC}"
echo -e "${YELLOW}Validation Summary${NC}"
echo -e "${YELLOW}================================${NC}"

# Check for Go code
if [ -d "mutating-webhook" ]; then
    echo -e "${YELLOW}Note: Run 'cd mutating-webhook && go test ./...' to test Go code${NC}"
fi

echo -e "\n${GREEN}✨ Local validation complete!${NC}"
echo -e "\nFor more thorough validation, consider:"
echo "  - Running security scans with Trivy: brew install aquasecurity/trivy/trivy"