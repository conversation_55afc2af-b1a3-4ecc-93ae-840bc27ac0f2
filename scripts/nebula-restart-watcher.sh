#!/usr/bin/env bash

# Set initial PID value to empty string
nebula_pid=""

# Parse named arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        -s|--sleep)
            sleep_time="$2"
            shift
            shift
            ;;
        -r|--restart-service)
            service_name_to_restart="$2"
            shift
            shift
            ;;
        -w|--watch-service)
            service_to_watch="$2"
            shift
            shift
            ;;
        *)  # Unknown option
            echo "Unknown option: $1" >&2
            exit 1
            ;;
    esac
done

# Set default values if not specified
sleep_time="${sleep_time:-60}"
service_name_to_restart="${service_name_to_restart:-k3s-agent}"
service_to_watch="${service_to_watch:-nebula-start}"

# Log input parameters
echo "$(date +"%Y-%m-%d %H:%M:%S") - Starting service watcher script with the following input parameters:"
echo "    Sleep time: $sleep_time"
echo "    Service to restart: $service_name_to_restart"
echo "    Service to watch: $service_to_watch"

# Continuously check PID change every specified interval
while true
do
    current_pid="$(systemctl show -p MainPID --value "$service_to_watch")"
    current_state="$(systemctl show -p ActiveState --value "$service_to_watch")"

    if [[ "$nebula_pid" != "" && "$nebula_pid" != "$current_pid" && "$current_state" == "active" ]]
    then
        echo "$(date +"%Y-%m-%d %H:%M:%S") - $service_to_watch PID has changed. Restarting $service_name_to_restart service..."
        systemctl restart "$service_name_to_restart"
        nebula_pid="$current_pid"
    fi

    if [[ "$nebula_pid" == "" ]]
    then
        echo "$(date +"%Y-%m-%d %H:%M:%S") - Initializing $service_to_watch watcher..."
        nebula_pid="$current_pid"
    fi

    echo "$(date +"%Y-%m-%d %H:%M:%S") - Current PID for $service_to_watch: $current_pid"
    echo "$(date +"%Y-%m-%d %H:%M:%S") - Current state for $service_to_watch: $current_state"

    sleep "$sleep_time"
done
