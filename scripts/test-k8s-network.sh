#!/bin/bash

# Colors for better output
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[0;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to get all nodes in the cluster (compatible with basic shells)
get_nodes() {
    # Reset NODES array
    NODES=()

    # Get all node names using basic commands
    for node in $(kubectl get nodes -o jsonpath='{.items[*].metadata.name}'); do
        NODES+=("$node")
    done

    # Check if we got any nodes
    if [ ${#NODES[@]} -eq 0 ]; then
        echo -e "${RED}Error: No nodes found in the cluster.${NC}"
        exit 1
    fi

    echo -e "${GREEN}Found ${#NODES[@]} nodes in the cluster:${NC}"
    for node in "${NODES[@]}"; do
        echo "  - $node"
    done
}

# Function to print section headers
print_header() {
    echo -e "\n${BLUE}==== $1 ====${NC}\n"
}

# Create namespace
create_namespace() {
    print_header "Creating namespace test-network"

    if kube<PERSON>l get namespace test-network &> /dev/null; then
        echo -e "${YELLOW}Namespace test-network already exists.${NC}"
    else
        kubectl create namespace test-network
        echo -e "${GREEN}Namespace test-network created.${NC}"
    fi
}

# Deploy pods on all nodes
deploy_pods() {
    print_header "Deploying pods across nodes"

    # Deploy nginx pods on each node
    for node in "${NODES[@]}"; do
        echo "Deploying nginx pod on $node..."
        cat <<EOF | kubectl apply -f -
apiVersion: v1
kind: Pod
metadata:
  name: nginx-${node}
  namespace: test-network
  labels:
    app: nginx
    node: ${node}
spec:
  containers:
  - name: nginx
    image: nginx
    ports:
    - containerPort: 80
  nodeSelector:
    kubernetes.io/hostname: ${node}
EOF
    done

    # Deploy netshoot pods on each node
    for node in "${NODES[@]}"; do
        echo "Deploying netshoot pod on $node..."
        cat <<EOF | kubectl apply -f -
apiVersion: v1
kind: Pod
metadata:
  name: netshoot-${node}
  namespace: test-network
  labels:
    app: netshoot
    node: ${node}
spec:
  containers:
  - name: netshoot
    image: nicolaka/netshoot
    command:
    - sleep
    - "3600"
  nodeSelector:
    kubernetes.io/hostname: ${node}
EOF
    done

    # Deploy a test service for internal service discovery testing
    echo "Deploying test service..."
    cat <<EOF | kubectl apply -f -
apiVersion: v1
kind: Service
metadata:
  name: nginx-service
  namespace: test-network
spec:
  selector:
    app: nginx
  ports:
  - port: 80
    targetPort: 80
EOF
}

# Wait for pods to be ready
wait_for_pods() {
    print_header "Waiting for pods to be ready"

    echo "Waiting for nginx pods..."
    kubectl wait --for=condition=Ready pod -l app=nginx -n test-network --timeout=60s || {
        echo -e "${RED}Not all nginx pods are ready. Some tests may fail.${NC}"
    }

    echo "Waiting for netshoot pods..."
    kubectl wait --for=condition=Ready pod -l app=netshoot -n test-network --timeout=60s || {
        echo -e "${RED}Not all netshoot pods are ready. Some tests may fail.${NC}"
    }
}

# Test connectivity between pods
test_pod_connectivity() {
    print_header "Testing pod-to-pod connectivity"

    # Get all the nginx pod IPs - using compatible approach
    for node in "${NODES[@]}"; do
        if kubectl get pod nginx-${node} -n test-network &> /dev/null; then
            ip=$(kubectl get pod nginx-${node} -n test-network -o jsonpath='{.status.podIP}')
            echo "nginx-${node} IP: ${ip}"

            # Store IP in a way compatible with older bash
            eval "nginx_ip_${node}=\"$ip\""
        else
            echo -e "${YELLOW}Warning: nginx-${node} pod not found.${NC}"
        fi
    done

    # Test HTTP connectivity
    print_header "Testing HTTP connectivity between pods"

    printf "%-50s %-10s\n" "CONNECTION" "STATUS"
    printf "%-50s %-10s\n" "-------------------------------------------------" "----------"

    for src_node in "${NODES[@]}"; do
        if ! kubectl get pod netshoot-${src_node} -n test-network &> /dev/null; then
            continue
        fi

        for dst_node in "${NODES[@]}"; do
            # Get destination IP using variable name substitution
            eval "dst_ip=\$nginx_ip_${dst_node}"

            if [ -z "$dst_ip" ]; then
                continue
            fi

            connection="netshoot-${src_node} → nginx-${dst_node} (${dst_ip})"

            # Use curl to test HTTP connectivity
            result=$(kubectl exec -n test-network netshoot-${src_node} -- curl -s -o /dev/null -w "%{http_code}" --connect-timeout 3 http://${dst_ip} 2>/dev/null || echo "Failed")

            if [ "$result" == "200" ]; then
                printf "%-50s ${GREEN}%-10s${NC}\n" "$connection" "SUCCESS"
            else
                printf "%-50s ${RED}%-10s${NC}\n" "$connection" "FAILED"
            fi
        done
    done
}

# Test ICMP connectivity
test_icmp_connectivity() {
    print_header "Testing ICMP connectivity between pods"

    printf "%-50s %-10s\n" "CONNECTION" "STATUS"
    printf "%-50s %-10s\n" "-------------------------------------------------" "----------"

    for src_node in "${NODES[@]}"; do
        if ! kubectl get pod netshoot-${src_node} -n test-network &> /dev/null; then
            continue
        fi

        for dst_node in "${NODES[@]}"; do
            # Get destination IP using variable name substitution
            eval "dst_ip=\$nginx_ip_${dst_node}"

            if [ -z "$dst_ip" ]; then
                continue
            fi

            connection="netshoot-${src_node} → nginx-${dst_node} (${dst_ip})"

            # Use ping to test ICMP connectivity
            if kubectl exec -n test-network netshoot-${src_node} -- ping -c 2 -W 2 ${dst_ip} &> /dev/null; then
                printf "%-50s ${GREEN}%-10s${NC}\n" "$connection" "SUCCESS"
            else
                printf "%-50s ${RED}%-10s${NC}\n" "$connection" "FAILED"
            fi
        done
    done
}

# Test external connectivity
test_external_connectivity() {
    print_header "Testing external connectivity"

    printf "%-50s %-10s\n" "CONNECTION" "STATUS"
    printf "%-50s %-10s\n" "-------------------------------------------------" "----------"

    # Define external endpoints to test
    external_targets="Google DNS (*******):ping:******* Google HTTP:http:http://www.google.com Google HTTPS:https:https://www.google.com Cloudflare DNS (*******):ping:******* GitHub HTTPS:https:https://github.com"

    for src_node in "${NODES[@]}"; do
        if ! kubectl get pod netshoot-${src_node} -n test-network &> /dev/null; then
            continue
        fi

        for target in $external_targets; do
            IFS=':' read -r name type url <<< "$target"
            connection="netshoot-${src_node} → ${name}"

            case $type in
                ping)
                    # Test ICMP connectivity
                    if kubectl exec -n test-network netshoot-${src_node} -- ping -c 2 -W 2 ${url} &> /dev/null; then
                        printf "%-50s ${GREEN}%-10s${NC}\n" "$connection" "SUCCESS"
                    else
                        printf "%-50s ${RED}%-10s${NC}\n" "$connection" "FAILED"
                    fi
                    ;;
                http|https)
                    # Test HTTP/HTTPS connectivity
                    result=$(kubectl exec -n test-network netshoot-${src_node} -- curl -s -o /dev/null -w "%{http_code}" --connect-timeout 5 ${url} 2>/dev/null || echo "Failed")

                    if [[ "$result" == "200" || "$result" == "301" || "$result" == "302" ]]; then
                        printf "%-50s ${GREEN}%-10s${NC}\n" "$connection" "SUCCESS"
                    else
                        printf "%-50s ${RED}%-10s${NC}\n" "$connection" "FAILED ($result)"
                    fi
                    ;;
            esac
        done
    done
}

# Test Kubernetes API server connectivity
test_k8s_api_connectivity() {
    print_header "Testing Kubernetes API server connectivity"

    printf "%-50s %-20s\n" "POD" "STATUS"
    printf "%-50s %-20s\n" "-------------------------------------------------" "--------------------"

    for src_node in "${NODES[@]}"; do
        if ! kubectl get pod netshoot-${src_node} -n test-network &> /dev/null; then
            continue
        fi

        connection="netshoot-${src_node} → K8s API Server"

        # Create a script to test API server connectivity
        kubectl exec -n test-network netshoot-${src_node} -- bash -c "cat > /tmp/test_api.sh << 'EOF'
#!/bin/bash
APISERVER=https://kubernetes.default.svc
SERVICEACCOUNT=/var/run/secrets/kubernetes.io/serviceaccount
TOKEN=\$(cat \${SERVICEACCOUNT}/token)
CACERT=\${SERVICEACCOUNT}/ca.crt

# Test connection to API server
curl --cacert \${CACERT} --header \"Authorization: Bearer \${TOKEN}\" -s \${APISERVER}/api/v1/namespaces/default/pods 2>/dev/null 1>/dev/null
exit \$?
EOF
chmod +x /tmp/test_api.sh
" &> /dev/null

        # Execute the test
        if kubectl exec -n test-network netshoot-${src_node} -- /tmp/test_api.sh &> /dev/null; then
            printf "%-50s ${GREEN}%-20s${NC}\n" "$connection" "ACCESSIBLE"
        else
            printf "%-50s ${RED}%-20s${NC}\n" "$connection" "NOT ACCESSIBLE"
        fi
    done
}

# Test DNS resolution
test_dns_resolution() {
    print_header "Testing DNS resolution"

    printf "%-50s %-20s\n" "DNS QUERY" "STATUS"
    printf "%-50s %-20s\n" "-------------------------------------------------" "--------------------"

    # Test internal and external DNS resolution
    domains="kubernetes.default.svc.cluster.local kube-dns.kube-system.svc.cluster.local nginx-service.test-network.svc.cluster.local google.com github.com"

    for src_node in "${NODES[@]}"; do
        if ! kubectl get pod netshoot-${src_node} -n test-network &> /dev/null; then
            continue
        fi

        for domain in $domains; do
            connection="netshoot-${src_node} → ${domain}"

            # Test DNS resolution
            if kubectl exec -n test-network netshoot-${src_node} -- nslookup ${domain} &> /dev/null; then
                printf "%-50s ${GREEN}%-20s${NC}\n" "$connection" "RESOLVES"
            else
                printf "%-50s ${RED}%-20s${NC}\n" "$connection" "FAILS TO RESOLVE"
            fi
        done
    done
}

# Test service discovery and connectivity
test_service_discovery() {
    print_header "Testing Kubernetes service discovery"

    printf "%-50s %-20s\n" "CONNECTION" "STATUS"
    printf "%-50s %-20s\n" "-------------------------------------------------" "--------------------"

    # Get the service IP
    service_ip=$(kubectl get svc nginx-service -n test-network -o jsonpath='{.spec.clusterIP}' 2>/dev/null)
    if [ -z "$service_ip" ]; then
        echo -e "${RED}Could not get nginx-service IP. Service may not be deployed.${NC}"
        return
    fi

    echo "Service ClusterIP: $service_ip"

    for src_node in "${NODES[@]}"; do
        if ! kubectl get pod netshoot-${src_node} -n test-network &> /dev/null; then
            continue
        fi

        # Test using service DNS name
        connection="netshoot-${src_node} → nginx-service (DNS)"
        result=$(kubectl exec -n test-network netshoot-${src_node} -- curl -s -o /dev/null -w "%{http_code}" --connect-timeout 3 http://nginx-service.test-network.svc.cluster.local 2>/dev/null || echo "Failed")

        if [ "$result" == "200" ]; then
            printf "%-50s ${GREEN}%-20s${NC}\n" "$connection" "SUCCESS"
        else
            printf "%-50s ${RED}%-20s${NC}\n" "$connection" "FAILED ($result)"
        fi

        # Test using service ClusterIP
        connection="netshoot-${src_node} → nginx-service (IP)"
        result=$(kubectl exec -n test-network netshoot-${src_node} -- curl -s -o /dev/null -w "%{http_code}" --connect-timeout 3 http://${service_ip} 2>/dev/null || echo "Failed")

        if [ "$result" == "200" ]; then
            printf "%-50s ${GREEN}%-20s${NC}\n" "$connection" "SUCCESS"
        else
            printf "%-50s ${RED}%-20s${NC}\n" "$connection" "FAILED ($result)"
        fi
    done
}

# Test CoreDNS connectivity
test_coredns_connectivity() {
    print_header "Testing CoreDNS connectivity"

    printf "%-50s %-20s\n" "CONNECTION" "STATUS"
    printf "%-50s %-20s\n" "-------------------------------------------------" "--------------------"

    # Get CoreDNS service IP
    coredns_service_ip=$(kubectl get svc kube-dns -n kube-system -o jsonpath='{.spec.clusterIP}' 2>/dev/null)
    if [ -z "$coredns_service_ip" ]; then
        echo -e "${RED}Could not get CoreDNS service IP. It may have a different name in your cluster.${NC}"
        return
    fi

    echo "CoreDNS Service IP: $coredns_service_ip"

    for src_node in "${NODES[@]}"; do
        if ! kubectl get pod netshoot-${src_node} -n test-network &> /dev/null; then
            continue
        fi

        # Test UDP connection to CoreDNS
        connection="netshoot-${src_node} → CoreDNS (UDP)"

        # Create a dig test command
        if kubectl exec -n test-network netshoot-${src_node} -- dig @${coredns_service_ip} kubernetes.default.svc.cluster.local +short &> /dev/null; then
            printf "%-50s ${GREEN}%-20s${NC}\n" "$connection" "SUCCESS"
        else
            printf "%-50s ${RED}%-20s${NC}\n" "$connection" "FAILED"
        fi

        # Test TCP connection to CoreDNS
        connection="netshoot-${src_node} → CoreDNS (TCP)"

        if kubectl exec -n test-network netshoot-${src_node} -- dig @${coredns_service_ip} kubernetes.default.svc.cluster.local +tcp +short &> /dev/null; then
            printf "%-50s ${GREEN}%-20s${NC}\n" "$connection" "SUCCESS"
        else
            printf "%-50s ${RED}%-20s${NC}\n" "$connection" "FAILED"
        fi
    done
}

# Test MTU settings and path MTU discovery
test_mtu() {
    print_header "Testing MTU configuration"

    printf "%-50s %-20s\n" "POD" "MTU SIZE"
    printf "%-50s %-20s\n" "-------------------------------------------------" "--------------------"

    for src_node in "${NODES[@]}"; do
        if ! kubectl get pod netshoot-${src_node} -n test-network &> /dev/null; then
            continue
        fi

        # Try ifconfig first since ip command appears to be failing
        mtu=$(kubectl exec -n test-network netshoot-${src_node} -- sh -c "ifconfig eth0 2>/dev/null | grep MTU | awk '{print \$NF}'" 2>/dev/null)

        # If ifconfig fails, try to find MTU using netstat
        if [ -z "$mtu" ]; then
            mtu=$(kubectl exec -n test-network netshoot-${src_node} -- sh -c "netstat -i | grep eth0 | awk '{print \$2}'" 2>/dev/null)
        fi

        printf "%-50s %-20s\n" "netshoot-${src_node}" "${mtu:-Unknown}"
    done
}

# Collect node network information
collect_node_info() {
    print_header "Collecting node network configuration"

    # Check if this is a K3s cluster
    if kubectl get node ${NODES[0]} -o jsonpath='{.status.nodeInfo.kubeletVersion}' 2>/dev/null | grep -q "k3s"; then
        echo -e "${YELLOW}Detected K3s cluster. K3s uses its own networking (typically Flannel).${NC}"
    fi

    # Print CNI information if possible
    echo "CNI Plugin Information:"
    kubectl get daemonset --all-namespaces | grep -E 'cni|calico|weave|flannel|cilium' || echo "No CNI daemonset found with standard naming"

    # For K3s, check for flannel config
    if kubectl get configmap -n kube-system flannel-config &> /dev/null; then
        echo -e "\nFound Flannel config in kube-system namespace (typical for K3s)"
    fi

    echo -e "\nContainer Runtime Information:"
    for node in "${NODES[@]}"; do
        echo -n "${node}: "
        kubectl get node ${node} -o jsonpath='{.status.nodeInfo.containerRuntimeVersion}' 2>/dev/null || echo "N/A"
        echo ""
    done
}

# Cleanup resources
cleanup() {
    print_header "Cleaning up test resources"

    echo "Deleting namespace test-network..."
    kubectl delete namespace test-network
    echo -e "${GREEN}Cleanup completed.${NC}"
}

# Main execution
main() {
    # Get all nodes in the cluster
    get_nodes

    create_namespace
    deploy_pods
    wait_for_pods
    test_pod_connectivity
    test_icmp_connectivity
    test_external_connectivity
    test_k8s_api_connectivity
    test_dns_resolution
    test_service_discovery
    test_coredns_connectivity
    test_mtu
    collect_node_info

    # Automatically cleanup
    cleanup
}

# Run the script
main
