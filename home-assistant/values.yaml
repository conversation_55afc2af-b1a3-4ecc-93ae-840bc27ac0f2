# image:
# tag: 2023.9.2

podSecurityContext:
  runAsUser: 1000
  runAsGroup: 1000
  fsGroup: 1000
  fsGroupChangePolicy: "OnRootMismatch"

hostPort:
  # Enable 'hostPort' or not
  enabled: true

hostNetwork: true

# Environment variables
env:
  - name: TZ
    value: Europe/Prague
  - name: PYTHONPATH
    value: "/config/deps"
  - name: USER
    value: "hassuser"
# - name: SOME_VAR_FROM_CONFIG_MAP
#   valueFrom:
#     configMapRef:
#       name: configmap-name
#       key: config-key
# - name: SOME_SECRET
#   valueFrom:
#     secretKeyRef:
#       name: secret-name
#       key: secret-key

securityContext:
  privileged: true
  # capabilities:
  #   drop:
  #   - ALL
  # readOnlyRootFilesystem: true
  # runAsNonRoot: true
  # runAsUser: 1000

ingress:
  enabled: true
  annotations:
    cert-manager.io/cluster-issuer: letsencrypt-cloudflare-sklenarovi
    traefik.ingress.kubernetes.io/router.tls: "true"
  hosts:
    - host: hass.k8s.sklenarovi.cz
      paths:
        - path: /
          pathType: Prefix
  tls:
    - secretName: hass-k8s-sklenarovi-cz-tls
      hosts:
        - hass.k8s.sklenarovi.cz

nodeSelector:
  location: home
  kubernetes.io/hostname: home03

# Persistence values
persistence:
  enabled: true
  accessMode: ReadWriteOnce
  size: 10Gi

# if you need any additional volumes, you can define them here
# additionalVolumes:
#   - hostPath:
#       type: CharDevice
#       path: >-
#         /dev/serial/by-id/usb-ITEAD_SONOFF_Zigbee_3.0_USB_Dongle_Plus_V2_20230509111242-if00
#     name: usb

# if you need any additional volume mounts, you can define them here
# additionalMounts:
#   - mountPath: /dev/ttyACM0
#     name: usb

addons:
  codeserver:
    enabled: true
    ingress:
      enabled: true
      annotations:
        cert-manager.io/cluster-issuer: letsencrypt-cloudflare-sklenarovi
        nginx.ingress.kubernetes.io/auth-url: "https://oauth.k8s.sklenarovi.cz/oauth2/auth"
        nginx.ingress.kubernetes.io/auth-signin: "https://oauth.k8s.sklenarovi.cz/oauth2/start?rd=$pass_access_scheme://$http_host$escaped_request_uri"
      hosts:
        - host: hass-code.k8s.sklenarovi.cz
          paths:
            - path: /
              pathType: ImplementationSpecific
      tls:
        - secretName: hass-code-k8s-sklenarovi-cz-tls
          hosts:
            - hass-code.k8s.sklenarovi.cz
