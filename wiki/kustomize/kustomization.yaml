apiVersion: kustomize.config.k8s.io/v1beta1
kind: Kustomization

namespace: wiki

resources:
  - deployment.yaml
  - service.yaml
  - ingress.yaml

commonLabels:
  app: wiki
  app.kubernetes.io/name: wiki
  app.kubernetes.io/instance: wiki

secretGenerator:
  - name: registry-pull-secret
    type: kubernetes.io/dockerconfigjson
    literals:
      - .dockerconfigjson={"auths":{"registry.k8s.sklenarovi.cz":{"username":"pavel","password":"pajikRegistry7"}}}

images:
  - name: registry.k8s.sklenarovi.cz/wiki
    newName: registry.k8s.sklenarovi.cz/wiki
    newTag: latest
