apiVersion: apps/v1
kind: Deployment
metadata:
  name: wiki
spec:
  replicas: 1
  selector:
    matchLabels:
      app: wiki
  template:
    metadata:
      labels:
        app: wiki
    spec:
      nodeSelector:
        location: home
      imagePullSecrets:
        - name: registry-pull-secret
      volumes:
        - name: wiki-private
          emptyDir: {}
      containers:
      - name: server
        image: registry.k8s.sklenarovi.cz/wiki:latest
        imagePullPolicy: Always
        ports:
        - containerPort: 8080
        volumeMounts:
        - name: wiki-private
          mountPath: /Users/<USER>/repos/home/<USER>
      - name: client
        image: registry.k8s.sklenarovi.cz/wiki:latest
        imagePullPolicy: Always
        command: ["streamlit", "run", "/app/app/client.py"]
        env:
        - name: REMOTE_RUNNABLE_URL
          value: "http://localhost:8080/wiki/"
        ports:
        - containerPort: 8501
