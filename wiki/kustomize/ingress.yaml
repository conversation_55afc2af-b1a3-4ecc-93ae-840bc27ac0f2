apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: wiki
  annotations:
    cert-manager.io/cluster-issuer: letsencrypt-cloudflare-sklenarovi
    nginx.ingress.kubernetes.io/auth-signin: https://oauth.k8s.sklenarovi.cz/oauth2/start?rd=$pass_access_scheme://$http_host$escaped_request_uri
    nginx.ingress.kubernetes.io/auth-url: https://oauth.k8s.sklenarovi.cz/oauth2/auth
spec:
  tls:
    - hosts:
        - "wiki.k8s.sklenarovi.cz"
      secretName: "wiki-k8s-sklenarovi-cz-tls"
  rules:
    - host: "wiki.k8s.sklenarovi.cz"
      http:
        paths:
          - path: "/"
            pathType: Prefix
            backend:
              service:
                name: wiki
                port:
                  number: 8501
