# Setup nebula watcher   

```bash
#ExecStart=/usr/bin/bash /usr/local/bin/nebula-restart-watcher.sh --sleep 60 --restart-service k3s-agent --watch-service nebula-start
sudo nano /etc/systemd/system/nebula-restart-watcher.service

sudo systemctl daemon-reload
sudo nano /usr/local/bin/nebula-restart-watcher.sh
sudo chmod +x /usr/local/bin/nebula-restart-watcher.sh
sudo systemctl enable nebula-restart-watcher
sudo systemctl start nebula-restart-watcher
sudo systemctl status nebula-restart-watcher
```


# Setup read write all to serial device

To allow all users on a Linux system to access a USB serial device, you typically need to modify the permissions or add users to the appropriate group that has access to the device. Here's how you can do it:

Identifying the Device:
First, plug in your USB serial device and identify its device name. It's usually something like /dev/ttyUSB0 or /dev/ttyACM0. You can check it using dmesg:
dmesg | grep tty

Changing Permissions Permanently:
To make a permanent change, you can use udev rules:

a. Create a new udev rule file:
sudo nano /etc/udev/rules.d/99-usb-serial.rules
b. Add the following line to grant permissions to all users for USB serial devices:
KERNEL=="ttyUSB*", MODE="0666"
c. Save and close the file.

d. Reload udev rules:
sudo udevadm control --reload-rules
sudo udevadm trigger
