certManager:
  # DaemonSet or Deployment
  kind: DaemonSet
  # replicaCount: 1
  # Adds additional labels to pods
  additionalPodLabels: {}
  # label1: test
  # label2: test

  podAnnotations:
    # environment: prod
    prometheus.io/scrape: "true"
    prometheus.io/port: "8080"
    prometheus.io/path: /metrics

  image:
    repository: joe<PERSON>ott/cert-exporter
    # The default tag is ".Chart.AppVersion", only set "tag" to override that
    tag:
    pullPolicy: IfNotPresent
    command: ["./app"]
    args:
      - --secrets-include-glob=*.crt
      - --include-cert-glob=/var/lib/rancher/k3s/agent/*.crt
      - --include-kubeconfig-glob=/var/lib/rancher/k3s/agent/*.kubeconfig
      - --logtostderr

  nodeSelector:
    kubernetes.io/arch: amd64
    location: home

  volumes:
    - name: certs
      hostPath:
        path: /var/lib/rancher/k3s/agent
        type: Directory
  volumeMounts:
    - mountPath: /var/lib/rancher/k3s/agent
      mountPropagation: HostToContainer
      name: certs
      readOnly: true

service:
  type: ClusterIP
  port: 8080

  portName: http-metrics

  # Annotations to add to the service
  annotations:
    {}
    # prometheus.io/port: "8080"
    # prometheus.io/scrape: "true"

  # Requires prometheus-operator to be installed

dashboards:
  certManagerDashboard:
    create: false
