---
apiVersion: v1
kind: Service
metadata:
  name: sonoff-bouda
  namespace: home
spec:
  ports:
    - protocol: TCP
      port: 80
      name: http
      targetPort: 80
---
apiVersion: v1
kind: Service
metadata:
  name: sonoff-pow
  namespace: home
spec:
  ports:
    - protocol: TCP
      port: 80
      name: http
      targetPort: 80
---
# apiVersion: v1
# kind: Endpoints
# metadata:
#   name: pihole-home
#   namespace: home
# subsets:
#   - addresses:
#       - ip: *************
#         nodeName: home01
#     ports:
#       - port: 80
apiVersion: discovery.k8s.io/v1
kind: EndpointSlice
metadata:
  name: sonoff-bouda
  namespace: home
  labels:
    kubernetes.io/service-name: sonoff-bouda
addressType: IPv4
ports:
  - name: http
    protocol: TCP
    port: 80
endpoints:
  - addresses:
      - "**************"
    conditions:
      ready: true
    nodeName: home01
---
apiVersion: discovery.k8s.io/v1
kind: EndpointSlice
metadata:
  name: sonoff-pow
  namespace: home
  labels:
    kubernetes.io/service-name: sonoff-pow
addressType: IPv4
ports:
  - name: http
    protocol: TCP
    port: 80
endpoints:
  - addresses:
      - "**************"
    conditions:
      ready: true
    nodeName: home01
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: sonoff
  namespace: home
  annotations:
    cert-manager.io/cluster-issuer: letsencrypt-cloudflare-sklenarovi
    nginx.ingress.kubernetes.io/auth-url: "https://oauth.k8s.sklenarovi.cz/oauth2/auth"
    nginx.ingress.kubernetes.io/auth-signin: "https://oauth.k8s.sklenarovi.cz/oauth2/start?rd=$pass_access_scheme://$http_host$escaped_request_uri"
spec:
  tls:
    - secretName: star-home-sklenarovi-cz-tls
      hosts:
        - "sonoff-bouda.home.sklenarovi.cz"
        - "sonoff-pow.home.sklenarovi.cz"
  rules:
    - host: sonoff-bouda.home.sklenarovi.cz
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: sonoff-bouda
                port:
                  number: 80
    - host: sonoff-pow.home.sklenarovi.cz
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: sonoff-pow
                port:
                  number: 80
