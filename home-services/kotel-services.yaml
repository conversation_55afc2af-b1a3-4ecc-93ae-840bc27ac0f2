---
apiVersion: v1
kind: Service
metadata:
  name: home-kotel
  namespace: home
spec:
  ports:
    - protocol: TCP
      port: 80
      name: http
      targetPort: 80
    - protocol: TCP
      port: 8083
      name: fhem
      targetPort: 8083
---
# apiVersion: v1
# kind: Endpoints
# metadata:
#   name: pihole-home
#   namespace: home
# subsets:
#   - addresses:
#       - ip: *************
#         nodeName: home01
#     ports:
#       - port: 80
apiVersion: discovery.k8s.io/v1
kind: EndpointSlice
metadata:
  name: home-kotel
  namespace: home
  labels:
    kubernetes.io/service-name: home-kotel
addressType: IPv4
ports:
  - name: http
    protocol: TCP
    port: 80
  - name: fhem
    protocol: TCP
    port: 8083
endpoints:
  - addresses:
      - "*************"
    conditions:
      ready: true
    nodeName: home01
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: home-kotel
  namespace: home
  annotations:
    cert-manager.io/cluster-issuer: letsencrypt-cloudflare-sklenarovi
  #   nginx.ingress.kubernetes.io/enable-cors: "true"
spec:
  tls:
    - secretName: star-home-sklenarovi-cz-tls
      hosts:
        - "pihole.home.sklenarovi.cz"
        - "fhem.home.sklenarovi.cz"
  rules:
    - host: pihole.home.sklenarovi.cz
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: home-kotel
                port:
                  number: 80
    - host: fhem.home.sklenarovi.cz
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: home-kotel
                port:
                  number: 8083
