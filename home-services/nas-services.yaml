---
apiVersion: v1
kind: Service
metadata:
  name: nas
  namespace: home
spec:
  ports:
    - protocol: TCP
      port: 8096
      name: http
      targetPort: 8096

---
# apiVersion: v1
# kind: Endpoints
# metadata:
#   name: pihole-home
#   namespace: home
# subsets:
#   - addresses:
#       - ip: *************
#         nodeName: home01
#     ports:
#       - port: 80
apiVersion: discovery.k8s.io/v1
kind: EndpointSlice
metadata:
  name: nas
  namespace: home
  labels:
    kubernetes.io/service-name: nas
addressType: IPv4
ports:
  - name: http
    protocol: TCP
    port: 8096

endpoints:
  - addresses:
      - "**************"
    conditions:
      ready: true
    nodeName: home01
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: nas
  namespace: home
spec:
  tls:
    - secretName: star-home-sklenarovi-cz-tls
      hosts:
        - "jellyfin.home.sklenarovi.cz"
  rules:
    - host: jellyfin.home.sklenarovi.cz
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: nas
                port:
                  number: 8096
