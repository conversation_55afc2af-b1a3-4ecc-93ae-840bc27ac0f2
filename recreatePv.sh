#!/bin/bash

# Script to create a new PV based on an old PV with changed name and nodeAffinity

# Check if the correct number of arguments are provided
if [ "$#" -ne 4 ]; then
    echo "Usage: $0 <OLD_PV_NAME> <NEW_PV_NAME> <OLD_NODE_NAME> <NEW_NODE_NAME>"
    exit 1
fi

OLD_PV_NAME=$1
NEW_PV_NAME=$2
OLD_NODE_NAME=$3
NEW_NODE_NAME=$4

# Fetch the old PV, modify it, and create the new PV
kubectl get pv $OLD_PV_NAME -o yaml | \
sed -E -e "s/name: $OLD_PV_NAME/name: $NEW_PV_NAME/" \
    -e '/uid:/d' \
    -e '/resourceVersion:/d' \
    -e '/creationTimestamp:/d' \
    -e '/selfLink:/d' \
    -e '/status:/,/^spec:/d' \
    -e "s/$OLD_NODE_NAME/$NEW_NODE_NAME/" | \
kubectl apply -f -

echo "New PV $NEW_PV_NAME based on $OLD_PV_NAME has been created with nodeAffinity set to $NEW_NODE_NAME."

