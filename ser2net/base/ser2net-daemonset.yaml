apiVersion: apps/v1
kind: DaemonSet
metadata:
  name: ser2net
spec:
  selector:
    matchLabels:
      app: ser2net
  template:
    metadata:
      labels:
        app: ser2net
    spec:
      nodeSelector:
        kubernetes.io/hostname: home04
      # Home assistnat need IP, not hostname
      hostNetwork: true
      containers:
        - name: ser2net
          image: ghcr.io/jippi/docker-ser2net:4.6.0
          securityContext:
            privileged: true
          volumeMounts:
            - name: ser2net-config-volume
              mountPath: /etc/ser2net/ser2net.yaml
              subPath: ser2net-config.yaml
            - name: serial-device-zigbee2mqtt
              mountPath: /dev/serial/by-id/usb-Silicon_Labs_Sonoff_Zigbee_3.0_USB_Dongle_Plus_0001-if00-port0
            - name: serial-device-hass
              mountPath: /dev/serial/by-id/usb-ITEAD_SONOFF_Zigbee_3.0_USB_Dongle_Plus_V2_20230509111242-if00
      volumes:
        - name: ser2net-config-volume
          configMap:
            name: ser2net-config
        - name: serial-device-zigbee2mqtt
          hostPath:
            path: /dev/serial/by-id/usb-Silicon_Labs_Sonoff_Zigbee_3.0_USB_Dongle_Plus_0001-if00-port0
            type: CharDevice
        - name: serial-device-hass
          hostPath:
            path: /dev/serial/by-id/usb-ITEAD_SONOFF_Zigbee_3.0_USB_Dongle_Plus_V2_20230509111242-if00
            type: CharDevice
