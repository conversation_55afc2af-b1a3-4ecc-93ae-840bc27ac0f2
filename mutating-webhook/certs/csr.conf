[req]
req_extensions = v3_req
distinguished_name = req_distinguished_name
[req_distinguished_name]
[v3_req]
basicConstraints = CA:FALSE
keyUsage = nonRepudiation, digitalSignature, keyEncipherment
extendedKeyUsage = serverAuth
subjectAltName = @alt_names
[alt_names]
DNS.1 = init-container-webhook-svc
DNS.2 = init-container-webhook-svc.kube-system
DNS.3 = init-container-webhook-svc.kube-system.svc
DNS.4 = init-container-webhook-svc.kube-system.svc.cluster.local
