#!/bin/bash
set -e

WEBHOOK_NS=kube-system
SERVICE_NAME=init-container-webhook-svc
SECRET_NAME=init-container-webhook-certs

echo "=== Creating certificates with proper DNS names ==="
TEMP_DIR=$(mktemp -d)
cd $TEMP_DIR

cat > server.conf << EOF
[req]
req_extensions = v3_req
distinguished_name = req_distinguished_name
prompt = no
[req_distinguished_name]
CN = ${SERVICE_NAME}.${WEBHOOK_NS}.svc
[v3_req]
basicConstraints = CA:FALSE
keyUsage = nonRepudiation, digitalSignature, keyEncipherment
extendedKeyUsage = serverAuth
subjectAltName = @alt_names
[alt_names]
DNS.1 = ${SERVICE_NAME}
DNS.2 = ${SERVICE_NAME}.${WEBHOOK_NS}
DNS.3 = ${SERVICE_NAME}.${WEBHOOK_NS}.svc
DNS.4 = ${SERVICE_NAME}.${WEBHOOK_NS}.svc.cluster.local
EOF

# Create a certificate authority
openssl genrsa -out ca.key 2048
openssl req -x509 -new -nodes -key ca.key -days 365 -out ca.crt -subj "/CN=webhook-ca"

# Create a server certificate
openssl genrsa -out server.key 2048
openssl req -new -key server.key -out server.csr -config server.conf
openssl x509 -req -in server.csr -CA ca.crt -CAkey ca.key -CAcreateserial -out server.crt -days 365 -extensions v3_req -extfile server.conf

echo "Generated certificates successfully"

# Create the secret with the certificates
kubectl -n ${WEBHOOK_NS} create secret generic ${SECRET_NAME} \
    --from-file=tls.key=server.key \
    --from-file=tls.crt=server.crt \
    --from-file=ca.crt=ca.crt \
    --dry-run=client -o yaml | kubectl apply -f -

# Update the CA bundle in the webhook or create if it doesn't exist
CA_BUNDLE=$(cat ca.crt | base64 | tr -d '\n')

# Check if webhook config exists
if kubectl get mutatingwebhookconfiguration init-container-webhook-config &>/dev/null; then
  echo "Updating existing webhook configuration with new CA bundle"
  kubectl patch mutatingwebhookconfiguration init-container-webhook-config -p "{\"webhooks\":[{\"name\":\"init-container-webhook.home-iaac.local\",\"clientConfig\":{\"caBundle\":\"${CA_BUNDLE}\"}}]}"
else
  echo "Creating new webhook configuration with CA bundle"
  # Replace CA_BUNDLE placeholder in the webhook configuration file
  sed "s/\${CA_BUNDLE}/${CA_BUNDLE}/g" webhook-configuration.yaml | kubectl apply -f -
fi

echo "Certificates created and stored in the '${SECRET_NAME}' secret"
echo "CA Bundle updated in webhook configuration"

# Clean up
cd -
rm -rf ${TEMP_DIR}

# Check if webhook deployment exists, if so restart the pod
if kubectl get deployment -n kube-system init-container-webhook &>/dev/null; then
  echo "Restarting webhook pod..."
  kubectl delete pod -n kube-system -l app=init-container-webhook
  kubectl wait --for=condition=Ready pod -l app=init-container-webhook -n kube-system --timeout=60s
else
  echo "No webhook deployment found. Apply the deployment manifest first:"
  echo "kubectl apply -f webhook-deployment.yaml"
fi

echo
echo "=== Webhook setup complete ==="
echo "Try creating a test pod:"
echo "kubectl apply -f test/test-nginx-pod.yaml"