apiVersion: apps/v1
kind: Deployment
metadata:
  name: init-container-webhook
  namespace: kube-system
  labels:
    app: init-container-webhook
spec:
  replicas: 1
  selector:
    matchLabels:
      app: init-container-webhook
  template:
    metadata:
      labels:
        app: init-container-webhook
    spec:
      containers:
      - name: webhook
        imagePullPolicy: Always
        image: pajikos/init-container-webhook:latest
        ports:
        - containerPort: 8443
        volumeMounts:
        - name: webhook-certs
          mountPath: /etc/webhook/certs
          readOnly: true
      volumes:
      - name: webhook-certs
        secret:
          secretName: init-container-webhook-certs
---
apiVersion: v1
kind: Service
metadata:
  name: init-container-webhook-svc
  namespace: kube-system
spec:
  ports:
  - port: 443
    targetPort: 8443
  selector:
    app: init-container-webhook
