apiVersion: admissionregistration.k8s.io/v1
kind: MutatingWebhookConfiguration
metadata:
  name: init-container-webhook-config
webhooks:
- name: init-container-webhook.home-iaac.local
  sideEffects: None
  admissionReviewVersions: ["v1"]
  clientConfig:
    service:
      name: init-container-webhook-svc
      namespace: kube-system
      path: "/mutate"
    caBundle: ${CA_BUNDLE}
  rules:
  - operations: ["CREATE"]
    apiGroups: [""]
    apiVersions: ["v1"]
    resources: ["pods"]
    scope: "Namespaced"
  namespaceSelector:
    matchExpressions:
    - key: init-container-injection
      operator: In
      values: ["enabled"]
  objectSelector: {}
  failurePolicy: Fail
