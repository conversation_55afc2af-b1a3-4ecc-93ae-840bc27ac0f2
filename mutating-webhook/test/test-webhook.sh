#!/bin/bash
set -e

echo "=== Creating test pod ==="
kubectl delete pod nginx-test --ignore-not-found
kubectl apply -f test/test-nginx-pod.yaml
sleep 5

echo "=== Pod details ==="
kubectl get pod nginx-test -o wide

echo "=== Checking for init containers ==="
kubectl get pod nginx-test -o jsonpath='{.spec.initContainers[*].name}' || echo "No init containers found"

echo "=== Pod YAML ==="
kubectl get pod nginx-test -o yaml | grep -A 10 initContainers: || echo "No init containers section found"

echo "=== Webhook logs ==="
kubectl logs -n kube-system -l app=init-container-webhook | tail -n 50