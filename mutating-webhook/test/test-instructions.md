# Testing the Mutating Webhook

To properly test the webhook, follow these steps:

## 1. Check if the webhook is properly deployed

```bash
# Check the pod is running
kubectl get pods -n kube-system -l app=init-container-webhook

# Check the service
kubectl get service -n kube-system init-container-webhook-svc

# Check the webhook configuration
kubectl get mutatingwebhookconfiguration init-container-webhook-config
```

## 2. Rebuild and redeploy the webhook (if needed)

```bash
# Build the image
docker build -t pajikos/init-container-webhook:latest .

# Push to Docker Hub
docker push pajikos/init-container-webhook:latest

# Delete the current pod to force a new one with the updated image
kubectl delete pod -n kube-system -l app=init-container-webhook
```

## 3. Verify the namespace label

```bash
# Label the default namespace
kubectl label namespace default init-container-injection=enabled --overwrite

# Verify the label
kubectl get namespace default --show-labels
```

## 4. Create a test pod and verify

```bash
# Delete any existing test pod
kubectl delete pod nginx-test --ignore-not-found

# Create a new test pod
kubectl apply -f test-nginx-pod.yaml

# Check if the init container was added
kubectl get pod nginx-test -o yaml | grep -A 10 initContainers:
```

## 5. Debugging

If the webhook is not working, check the logs:

```bash
kubectl logs -n kube-system -l app=init-container-webhook
```

Update the webhook CA bundle manually:

```bash
CA_BUNDLE=$(kubectl config view --raw --minify --flatten -o jsonpath='{.clusters[].cluster.certificate-authority-data}')
kubectl patch mutatingwebhookconfiguration init-container-webhook-config -p "{\"webhooks\":[{\"name\":\"init-container-webhook.home-iaac.local\",\"clientConfig\":{\"caBundle\":\"$CA_BUNDLE\"}}]}"
```

Verify the webhook configuration:

```bash
kubectl get mutatingwebhookconfiguration init-container-webhook-config -o yaml | grep -A 5 caBundle
```

Make sure the namespace selector is correct:

```bash
kubectl get mutatingwebhookconfiguration init-container-webhook-config -o jsonpath='{.webhooks[0].namespaceSelector}'
```