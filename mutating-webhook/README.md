# Kubernetes Mutating Webhook for Init Container Injection

This webhook automatically adds an init container to all pods created in namespaces with the label `init-container-injection=enabled`. The init container simply sleeps for 2 seconds.

## Prerequisites

- Kubernetes cluster
- kubectl configured to interact with your cluster
- OpenSSL for generating the required certificates

## Installation

### 1. Build and deploy the webhook

```bash
# Build the Docker image
docker build -t pajikos/init-container-webhook:latest .

# Push the image to Docker Hub
docker push pajikos/init-container-webhook:latest

# Deploy the webhook
kubectl apply -f webhook-deployment.yaml
```

### 2. Generate certificates and configure the webhook

```bash
# This script generates TLS certificates and configures the webhook
./create-proper-certs.sh
```

### 3. Label your namespaces

```bash
kubectl label namespace your-namespace init-container-injection=enabled
```

## How it works

The webhook intercepts pod creation requests in namespaces with the label `init-container-injection=enabled` and adds an init container that sleeps for 2 seconds.

## Troubleshooting

Check the logs of the webhook pod:

```bash
kubectl logs -n kube-system -l app=init-container-webhook
```

## Testing

A test script is included to help verify the webhook is working correctly:

```bash
# Run the test script
./test/test-webhook.sh
```

This script will:
1. Create a test pod
2. Check if the init container was added
3. Display the webhook logs
