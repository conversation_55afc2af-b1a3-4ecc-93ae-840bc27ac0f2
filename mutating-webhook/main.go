package main

import (
	"encoding/json"
	"fmt"
	"io"
	"log"
	"net/http"

	admissionv1 "k8s.io/api/admission/v1"
	corev1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/runtime"
	"k8s.io/apimachinery/pkg/runtime/serializer"
)

var (
	runtimeScheme = runtime.NewScheme()
	codecs        = serializer.NewCodecFactory(runtimeScheme)
	deserializer  = codecs.UniversalDeserializer()
)

// Server is the webhook server
type Server struct {
	cert string
	key  string
}

type patchOperation struct {
	Op    string      `json:"op"`
	Path  string      `json:"path"`
	Value interface{} `json:"value,omitempty"`
}

func main() {
	server := &Server{
		cert: "/etc/webhook/certs/tls.crt",
		key:  "/etc/webhook/certs/tls.key",
	}

	mux := http.NewServeMux()
	mux.HandleFunc("/mutate", server.handleMutate)
	
	// Add a health endpoint
	mux.HandleFunc("/health", func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
		w.Write([]byte("OK"))
	})

	log.Printf("Starting webhook server on port 8443...")
	log.Printf("Certificate path: %s", server.cert)
	log.Printf("Key path: %s", server.key)

	s := &http.Server{
		Addr:    ":8443",
		Handler: mux,
	}

	if err := s.ListenAndServeTLS(server.cert, server.key); err != nil {
		log.Fatalf("Failed to start webhook server: %v", err)
	}
}

func (s *Server) handleMutate(w http.ResponseWriter, r *http.Request) {
	log.Printf("Received request to /mutate endpoint - Method: %s, URL: %s", r.Method, r.URL.String())
	
	var body []byte
	if r.Body != nil {
		if data, err := io.ReadAll(r.Body); err == nil {
			body = data
			log.Printf("Request body length: %d bytes", len(body))
		} else {
			log.Printf("Error reading request body: %v", err)
		}
	} else {
		log.Println("Request body is nil")
	}

	if len(body) == 0 {
		log.Println("Empty body")
		http.Error(w, "empty body", http.StatusBadRequest)
		return
	}

	// Log headers
	log.Println("Request headers:")
	for name, values := range r.Header {
		for _, value := range values {
			log.Printf("  %s: %s", name, value)
		}
	}

	// verify the content type is accurate
	contentType := r.Header.Get("Content-Type")
	if contentType != "application/json" {
		log.Printf("Content-Type=%s, expected application/json", contentType)
		http.Error(w, "invalid Content-Type, expected `application/json`", http.StatusUnsupportedMediaType)
		return
	}

	var admissionResponse *admissionv1.AdmissionResponse
	ar := admissionv1.AdmissionReview{}
	if _, _, err := deserializer.Decode(body, nil, &ar); err != nil {
		log.Printf("Can't decode body: %v", err)
		admissionResponse = &admissionv1.AdmissionResponse{
			Result: &metav1.Status{
				Message: err.Error(),
			},
		}
	} else {
		admissionResponse = s.mutate(&ar)
	}

	admissionReview := admissionv1.AdmissionReview{
		TypeMeta: metav1.TypeMeta{
			APIVersion: "admission.k8s.io/v1",
			Kind:       "AdmissionReview",
		},
	}
	if admissionResponse != nil {
		admissionReview.Response = admissionResponse
		if ar.Request != nil {
			admissionReview.Response.UID = ar.Request.UID
		}
	}

	resp, err := json.Marshal(admissionReview)
	if err != nil {
		log.Printf("Could not encode response: %v", err)
		http.Error(w, fmt.Sprintf("could not encode response: %v", err), http.StatusInternalServerError)
	}
	log.Printf("Ready to write response...")

	if _, err := w.Write(resp); err != nil {
		log.Printf("Could not write response: %v", err)
		http.Error(w, fmt.Sprintf("could not write response: %v", err), http.StatusInternalServerError)
	}
}

func (s *Server) mutate(ar *admissionv1.AdmissionReview) *admissionv1.AdmissionResponse {
	req := ar.Request
	if req == nil {
		log.Printf("AdmissionReview request is nil")
		return &admissionv1.AdmissionResponse{
			Allowed: true,
		}
	}

	log.Printf("AdmissionReview for Kind=%v, Namespace=%v Name=%v UID=%v patchOperation=%v UserInfo=%v",
		req.Kind, req.Namespace, req.Name, req.UID, req.Operation, req.UserInfo)
	
	// Dump the raw request object for debugging
	if rawJSON, err := json.Marshal(ar); err == nil {
		log.Printf("Raw AdmissionReview: %s", string(rawJSON))
	} else {
		log.Printf("Error marshaling admission review: %v", err)
	}

	// The API server does not send the GVK for custom resources, so we need to check
	// the kind to make sure we're dealing with a Pod.
	if req.Kind.Kind != "Pod" {
		log.Printf("Skipping mutation for %s/%s, not a Pod", req.Namespace, req.Name)
		return &admissionv1.AdmissionResponse{
			Allowed: true,
		}
	}

	// Parse the Pod object
	var pod corev1.Pod
	if err := json.Unmarshal(req.Object.Raw, &pod); err != nil {
		log.Printf("Could not unmarshal raw object: %v", err)
		return &admissionv1.AdmissionResponse{
			Result: &metav1.Status{
				Message: err.Error(),
			},
		}
	}

	// Check if the namespace has the required label
	if !hasRequiredNamespaceLabels(pod.Namespace) {
		log.Printf("Skipping mutation for %s/%s, namespace does not have required label", req.Namespace, req.Name)
		return &admissionv1.AdmissionResponse{
			Allowed: true,
		}
	}

	// Create patch operations
	var patches []patchOperation

	// Add init container
	initContainer := corev1.Container{
		Name:  "init-sleep",
		Image: "busybox:latest",
		Command: []string{
			"sh",
			"-c",
			"echo 'Sleeping for 2 seconds...' && sleep 2",
		},
	}

	// Check if the pod already has init containers
	path := "/spec/initContainers"
	var value interface{}

	if len(pod.Spec.InitContainers) == 0 {
		value = []corev1.Container{initContainer}
		patches = append(patches, patchOperation{
			Op:    "add",
			Path:  path,
			Value: value,
		})
	} else {
		path = path + "/-"
		value = initContainer
		patches = append(patches, patchOperation{
			Op:    "add",
			Path:  path,
			Value: value,
		})
	}

	patchBytes, err := json.Marshal(patches)
	if err != nil {
		log.Printf("Could not marshal patch: %v", err)
		return &admissionv1.AdmissionResponse{
			Result: &metav1.Status{
				Message: err.Error(),
			},
		}
	}

	log.Printf("AdmissionResponse Patch: %v\n", string(patchBytes))

	return &admissionv1.AdmissionResponse{
		Allowed: true,
		Patch:   patchBytes,
		PatchType: func() *admissionv1.PatchType {
			pt := admissionv1.PatchTypeJSONPatch
			return &pt
		}(),
	}
}

// hasRequiredNamespaceLabels checks if the namespace has the required label
func hasRequiredNamespaceLabels(namespace string) bool {
	// In a real implementation, we would query the Kubernetes API to check namespace labels
	// But for now, we'll return true to apply the mutation to all namespaces
	log.Printf("Checking namespace %s for required labels", namespace)
	return true
}
