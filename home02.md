# Setup forwading to nebula network    

```bash
#Enable forwarding
sudo vi /etc/sysctl.conf
sudo sysctl -p /etc/sysctl.conf
#sudo sysctl -w net.ipv4.ip_forward=1
sudo iptables -t nat -A POSTROUTING -o nebula1 -j MASQUERADE
# Way back to home
#sudo iptables -t nat -A POSTROUTING -o eth0 -j MASQUERADE
sudo apt install iptables-persistent
sudo systemctl is-enabled netfilter-persistent.service
sudo systemctl status netfilter-persistent.service
cat /etc/iptables/rules.v4
```
