# cert-manager configuration
# Based on best practices from https://cert-manager.io/docs/installation/helm/

# Global configuration
global:
  # Set the verbosity of cert-manager. Range of 0 - 6 with 6 being the most verbose.
  logLevel: 2
  # Set to true to enable leader election for controller manager.
  leaderElection:
    namespace: cert-manager
# Install CRDs as part of the Helm release
crds:
  enabled: true
  keep: true
# Controller configuration
replicaCount: 1
image:
  repository: quay.io/jetstack/cert-manager-controller
  # Override the image tag to deploy a different version
  # tag: v1.13.3
  pullPolicy: IfNotPresent
# Resource limits and requests for the controller
resources:
  limits:
    cpu: 100m
    memory: 300Mi
  requests:
    cpu: 10m
    memory: 32Mi
# Node selector for controller deployment
nodeSelector:
  location: oracle
# Service account
serviceAccount:
  # Specifies whether a service account should be created
  create: true
  # The name of the service account to use.
  # If not set and create is true, a name is generated using the fullname template
  name: ""
  # Optional additional annotations to add to the controller's ServiceAccount
  annotations: {}
  # Optional additional labels to add to the controller's ServiceAccount
  labels: {}
# Webhook configuration
webhook:
  replicaCount: 1
  # Resource limits and requests for the webhook
  resources:
    limits:
      cpu: 100m
      memory: 128Mi
    requests:
      cpu: 10m
      memory: 32Mi
  # Node selector for webhook deployment
  nodeSelector:
    location: oracle
# CA Injector configuration
cainjector:
  enabled: true
  replicaCount: 1
  # Resource limits and requests for the CA injector
  resources:
    limits:
      cpu: 100m
      memory: 300Mi
    requests:
      cpu: 10m
      memory: 32Mi
  # Node selector for CA injector deployment
  nodeSelector:
    location: oracle
# Startup API check
startupapicheck:
  enabled: true
  # Resource limits and requests for startup API check
  resources:
    limits:
      cpu: 100m
      memory: 128Mi
    requests:
      cpu: 10m
      memory: 32Mi
  # Node selector for startup API check job
  nodeSelector:
    location: oracle
# Prometheus monitoring
prometheus:
  enabled: true
  servicemonitor:
    enabled: false
    prometheusInstance: default
    targetPort: http-metrics
    path: /metrics
    interval: 60s
    scrapeTimeout: 30s
    labels: {}
# Pod disruption budget
podDisruptionBudget:
  enabled: false
  # minAvailable: 1
  # maxUnavailable: 1
# Enable feature gates
featureGates: ""
# Additional command line flags to pass to cert-manager controller
extraArgs: []
# Additional environment variables to pass to cert-manager controller
extraEnv: []
# Additional volumes to add to the cert-manager controller pod
volumes: []
# Additional volume mounts to add to the cert-manager controller container
volumeMounts: []
# Pod annotations
podAnnotations: {}
# Pod labels
podLabels: {}
# DNS configuration
dns01RecursiveNameservers: 1.1.1.1:53,8.8.8.8:53
dns01RecursiveNameserversOnly: false
# Create additional Kubernetes resources alongside cert-manager
extraObjects:
  # Cloudflare API Token Secret (recommended over API Key)
  - |
    apiVersion: v1
    kind: Secret
    metadata:
      name: cloudflare-api-token-secret
      namespace: cert-manager
    type: Opaque
    stringData:
      # Replace YOUR_CLOUDFLARE_API_TOKEN with your actual Cloudflare API token
      # The API token should have Zone:Read and DNS:Edit permissions for the minilabs.eu domain
      api-token: "yxIi8UKGO5kGFgXKb35yp96IKQaHndjKsjVfQ69H"
  # Cloudflare API Token Secret for sklenarovi.cz domain
  - |
    apiVersion: v1
    kind: Secret
    metadata:
      name: cloudflare-api-token-secret-sklenarovi
      namespace: cert-manager
    type: Opaque
    stringData:
      # API token for sklenarovi.cz domain - to be replaced manually
      # The API token should have Zone:Read and DNS:Edit permissions for the sklenarovi.cz domain
      api-token: "feGR1oWrepBLaZiLOGXfKtniSGjM-_P5K_Qq6h13"
  # Production ClusterIssuer for minilabs.eu domain
  - |
    apiVersion: cert-manager.io/v1
    kind: ClusterIssuer
    metadata:
      name: letsencrypt-cloudflare-minilabs
    spec:
      acme:
        # The ACME server URL
        server: https://acme-v02.api.letsencrypt.org/directory

        # Email address used for ACME registration
        email: <EMAIL>

        # Name of a secret used to store the ACME account private key
        privateKeySecretRef:
          name: letsencrypt-cloudflare-minilabs-private-key

        # Enable the DNS-01 challenge provider
        solvers:
        - dns01:
            cloudflare:
              # Email address associated with the Cloudflare account
              email: <EMAIL>

              # API token reference
              apiTokenSecretRef:
                name: cloudflare-api-token-secret
                key: api-token

          # Optional: specify which domains this solver should be used for
          selector:
            dnsZones:
            - "minilabs.eu"
  # Production ClusterIssuer for sklenarovi.cz domain
  - |
    apiVersion: cert-manager.io/v1
    kind: ClusterIssuer
    metadata:
      name: letsencrypt-cloudflare-sklenarovi
    spec:
      acme:
        # The ACME server URL
        server: https://acme-v02.api.letsencrypt.org/directory

        # Email address used for ACME registration
        email: <EMAIL>

        # Name of a secret used to store the ACME account private key
        privateKeySecretRef:
          name: letsencrypt-cloudflare-sklenarovi-private-key

        # Enable the DNS-01 challenge provider
        solvers:
        - dns01:
            cloudflare:
              # Email address associated with the Cloudflare account
              email: <EMAIL>

              # API token reference
              apiTokenSecretRef:
                name: cloudflare-api-token-secret-sklenarovi
                key: api-token

          # Optional: specify which domains this solver should be used for
          selector:
            dnsZones:
            - "sklenarovi.cz"
  # Staging ClusterIssuer for testing
  - |-
    apiVersion: cert-manager.io/v1
    kind: ClusterIssuer
    metadata:
      name: letsencrypt-cloudflare-minilabs-staging
    spec:
      acme:
        # The ACME staging server URL (for testing)
        server: https://acme-staging-v02.api.letsencrypt.org/directory

        # Email address used for ACME registration
        email: <EMAIL>

        # Name of a secret used to store the ACME account private key
        privateKeySecretRef:
          name: letsencrypt-cloudflare-minilabs-staging-private-key

        # Enable the DNS-01 challenge provider for Cloudflare
        solvers:
        - dns01:
            cloudflare:
              # Email address associated with the Cloudflare account
              email: <EMAIL>

              # API token reference
              apiTokenSecretRef:
                name: cloudflare-api-token-secret
                key: api-token

          # Optional: specify which domains this solver should be used for
          selector:
            dnsZones:
            - "minilabs.eu"
  # Staging ClusterIssuer for sklenarovi.cz domain (for testing)
  - |-
    apiVersion: cert-manager.io/v1
    kind: ClusterIssuer
    metadata:
      name: letsencrypt-cloudflare-sklenarovi-staging
    spec:
      acme:
        # The ACME staging server URL (for testing)
        server: https://acme-staging-v02.api.letsencrypt.org/directory

        # Email address used for ACME registration
        email: <EMAIL>

        # Name of a secret used to store the ACME account private key
        privateKeySecretRef:
          name: letsencrypt-cloudflare-sklenarovi-staging-private-key

        # Enable the DNS-01 challenge provider for Cloudflare
        solvers:
        - dns01:
            cloudflare:
              # Email address associated with the Cloudflare account
              email: <EMAIL>

              # API token reference
              apiTokenSecretRef:
                name: cloudflare-api-token-secret-sklenarovi
                key: api-token

          # Optional: specify which domains this solver should be used for
          selector:
            dnsZones:
            - "sklenarovi.cz"
