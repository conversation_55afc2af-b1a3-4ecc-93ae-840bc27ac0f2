# cert-manager with Cloudflare DNS Challenge Setup

This guide will help you deploy cert-manager in your Kubernetes cluster with Cloudflare DNS-01 challenge support for automatic SSL certificate generation for the `minilabs.eu` domain.

## Overview

cert-manager is a Kubernetes add-on that automates the management and issuance of TLS certificates from various issuing sources. This setup uses:

- **cert-manager**: For automatic certificate management
- **Cloudflare DNS-01 Challenge**: For domain validation via DNS records
- **Let's Encrypt**: As the Certificate Authority
- **ArgoCD**: For GitOps deployment
- **Helm extraObjects**: ClusterIssuer and secret are deployed together with cert-manager as a single unit

## Key Features

- **Single Deployment**: ClusterIssuer and Cloudflare secret are included as `extraObjects` in the Helm chart
- **No Separate Steps**: Everything deploys together via ArgoCD
- **Production Ready**: Includes both staging and production ClusterIssuers
- **Secure Defaults**: Uses official cert-manager security contexts

## Prerequisites

1. Kubernetes cluster with ArgoCD installed
2. Cloudflare account with `minilabs.eu` domain
3. Access to Cloudflare dashboard to generate API credentials

## Step 1: Generate Cloudflare API Credentials

### Option A: API Token (Recommended)

1. Log in to the [Cloudflare Dashboard](https://dash.cloudflare.com/)
2. Go to **My Profile** → **API Tokens**
3. Click **Create Token**
4. Use the **Custom token** template
5. Configure the token with these permissions:
   - **Zone** → **Zone:Read** → **Include** → **Specific zone** → `minilabs.eu`
   - **Zone** → **DNS:Edit** → **Include** → **Specific zone** → `minilabs.eu`
6. Set **Client IP Address Filtering** (optional but recommended)
7. Set **TTL** (optional, for security)
8. Click **Continue to summary** → **Create Token**
9. **Copy and save the token securely** - you won't see it again!

### Option B: Global API Key (Legacy)

1. Log in to the [Cloudflare Dashboard](https://dash.cloudflare.com/)
2. Go to **My Profile** → **API Tokens**
3. In the **API Keys** section, click **View** next to **Global API Key**
4. Enter your password and complete any 2FA challenge
5. **Copy and save the API key securely**

## Step 2: Configure Cloudflare API Token

The Cloudflare API token is now included directly in the cert-manager Helm chart via `extraObjects`. You need to update the values.yaml file with your actual API token.

### Update the API Token in values.yaml

1. Edit `cert-manager/values.yaml` and find the `extraObjects` section
2. Replace `YOUR_CLOUDFLARE_API_TOKEN` with your actual Cloudflare API token:
   ```yaml
   extraObjects:
     - apiVersion: v1
       kind: Secret
       metadata:
         name: cloudflare-api-token-secret
         namespace: cert-manager
       type: Opaque
       stringData:
         api-token: "your-actual-api-token-here"  # Replace this
   ```

**Important Security Note**: Since the API token is now stored in the values.yaml file, ensure this file is properly secured and not committed to public repositories with the actual token. Consider using:
- External secret management (like External Secrets Operator)
- Sealed Secrets
- ArgoCD Vault Plugin
- Or apply the secret manually before deployment

## Step 3: Deploy cert-manager

The cert-manager, ClusterIssuer, and secret will be deployed automatically as a single unit via ArgoCD when you commit the application file.

1. Ensure the ArgoCD application is in your repository:

   ```bash
   git add apps/application-cert-manager.yaml cert-manager/values.yaml
   git commit -m "Add cert-manager application with ClusterIssuer"
   git push
   ```

2. ArgoCD will automatically deploy:
   - cert-manager components
   - Cloudflare API token secret
   - Production and staging ClusterIssuers
   - All configured with the settings from `cert-manager/values.yaml`

**Note**: The ClusterIssuer and secret are now included as `extraObjects` in the Helm chart, so no separate deployment steps are needed.

## Step 4: Verify Installation

### Check cert-manager pods

```bash
kubectl get pods -n cert-manager
```

### Check ClusterIssuer status

```bash
kubectl get clusterissuer
kubectl describe clusterissuer letsencrypt-cloudflare-minilabs
```

### Check for any issues

```bash
kubectl logs -n cert-manager -l app=cert-manager
```

## Step 5: Request a Certificate

Create a test certificate to verify everything works:

```yaml
apiVersion: cert-manager.io/v1
kind: Certificate
metadata:
  name: test-minilabs-eu-tls
  namespace: default
spec:
  secretName: test-minilabs-eu-tls
  issuerRef:
    name: letsencrypt-cloudflare-minilabs-staging  # Use staging first for testing
    kind: ClusterIssuer
  dnsNames:
  - test.minilabs.eu
```

Apply and check:
```bash
kubectl apply -f test-certificate.yaml
kubectl get certificate
kubectl describe certificate test-minilabs-eu-tls
```

## Step 6: Using Certificates in Ingress

Once cert-manager is working, you can use it in your Ingress resources:

```yaml
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: example-ingress
  annotations:
    cert-manager.io/cluster-issuer: "letsencrypt-cloudflare-minilabs"
spec:
  tls:
  - hosts:
    - app.minilabs.eu
    secretName: app-minilabs-eu-tls
  rules:
  - host: app.minilabs.eu
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: app-service
            port:
              number: 80
```

## Troubleshooting

### Common Issues

1. **Certificate stuck in "Pending" state**:
   ```bash
   kubectl describe certificate <certificate-name>
   kubectl describe certificaterequest <request-name>
   kubectl describe order <order-name>
   kubectl describe challenge <challenge-name>
   ```

2. **DNS challenge failing**:
   - Verify Cloudflare API credentials are correct
   - Check that the API token/key has the right permissions
   - Ensure the domain is properly configured in Cloudflare

3. **cert-manager pods not starting**:
   - Check resource limits and node selectors in values.yaml
   - Verify CRDs are installed: `kubectl get crd | grep cert-manager`

### Useful Commands

```bash
# Check cert-manager logs
kubectl logs -n cert-manager -l app=cert-manager

# Check webhook logs
kubectl logs -n cert-manager -l app=webhook

# Check CA injector logs
kubectl logs -n cert-manager -l app=cainjector

# List all cert-manager resources
kubectl get certificates,certificaterequests,orders,challenges --all-namespaces
```

## Security Best Practices

1. **Use API Tokens instead of API Keys** when possible
2. **Limit API Token permissions** to only the required zones
3. **Set IP restrictions** on API tokens if your cluster has static IPs
4. **Use staging issuer first** to avoid rate limits during testing
5. **Monitor certificate expiration** and renewal
6. **Keep API credentials secure** and rotate them regularly

## Configuration Details

### Resource Limits
The values.yaml includes conservative resource limits suitable for most home lab environments:
- Controller: 100m CPU, 300Mi memory
- Webhook: 100m CPU, 128Mi memory  
- CA Injector: 100m CPU, 300Mi memory

### Node Placement
All components are configured to run on nodes with `location: oracle` label. Adjust the `nodeSelector` in values.yaml if needed.

### Security Context
All components use the default cert-manager security contexts for enhanced security.

## Next Steps

1. Test with staging issuer first
2. Switch to production issuer once confirmed working
3. Set up monitoring and alerting for certificate expiration
4. Consider implementing certificate transparency monitoring
5. Document your specific use cases and certificate requirements
