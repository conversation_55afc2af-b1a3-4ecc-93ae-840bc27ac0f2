# Example Certificate for testing cert-manager with Cloudflare DNS challenge
# This creates a test certificate for the minilabs.eu domain

apiVersion: cert-manager.io/v1
kind: Certificate
metadata:
  name: test-minilabs-eu-tls
  namespace: default
spec:
  # Secret name where the certificate will be stored
  secretName: test-minilabs-eu-tls
  # Reference to the ClusterIssuer
  issuerRef:
    name: letsencrypt-cloudflare-minilabs-staging # Use staging first for testing
    kind: ClusterIssuer
    group: cert-manager.io
  # Domain names for the certificate
  dnsNames:
    - test.minilabs.eu
    - staging.minilabs.eu
  # Optional: Certificate duration (default is 90 days for Let's Encrypt)
  duration: 2160h # 90 days
  # Optional: Renew certificate when it has 30 days left
  renewBefore: 720h # 30 days
---
# Production certificate example (use after testing with staging)
apiVersion: cert-manager.io/v1
kind: Certificate
metadata:
  name: prod-minilabs-eu-tls
  namespace: default
spec:
  secretName: prod-minilabs-eu-tls
  issuerRef:
    name: letsencrypt-cloudflare-minilabs # Production issuer
    kind: ClusterIssuer
    group: cert-manager.io
  dnsNames:
    - "*.minilabs.eu" # Wildcard certificate covers all subdomains
  duration: 2160h # 90 days
  renewBefore: 720h # 30 days
---
# Example Ingress using the certificate
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: example-app-ingress
  namespace: default
  annotations:
    # Automatically request certificate using cert-manager
    cert-manager.io/cluster-issuer: letsencrypt-cloudflare-minilabs
    # Other ingress annotations as needed
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
spec:
  tls:
    - hosts:
        - app.minilabs.eu
      secretName: app-minilabs-eu-tls # cert-manager will create this secret
  rules:
    - host: app.minilabs.eu
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: example-app-service
                port:
                  number: 80
