apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: allow-specific-ips
  namespace: registry
spec:
  podSelector: {} # This selects all pods in the namespace
  policyTypes:
    - Ingress
  ingress:
    - from:
        - podSelector: {}
    - from:
        - namespaceSelector:
            matchLabels:
              kubernetes.io/metadata.name: victoria
    - from:
        - namespaceSelector:
            matchLabels:
              kubernetes.io/metadata.name: nginx-controller
    - from:
        - ipBlock:
            cidr: ************/24
        - ipBlock:
            cidr: ***********/24
        - ipBlock:
            cidr: *************/32
        #   - ipBlock:
        #         cidr: *********/16
        #   - ipBlock:
        #         cidr: *********/16
