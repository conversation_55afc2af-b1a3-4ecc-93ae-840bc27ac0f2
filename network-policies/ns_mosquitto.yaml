apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
      name: allow-specific-ips
      namespace: mosquitto
spec:
      podSelector: {} # This selects all pods in the namespace
      policyTypes:
            - Ingress
      ingress:
            - from:
                    - namespaceSelector:
                            matchLabels:
                                  kubernetes.io/metadata.name: victoria
            - from:
                    - namespaceSelector:
                            matchLabels:
                                  kubernetes.io/metadata.name: zigbee2mqtt
            - from:
                    - namespaceSelector:
                            matchLabels:
                                  kubernetes.io/metadata.name: frigate
            - from:
                    - namespaceSelector:
                            matchLabels:
                                  kubernetes.io/metadata.name: nginx-controller
            - from:
                    - namespaceSelector:
                            matchLabels:
                                  kubernetes.io/metadata.name: home-assistant
            - from:
                    - namespaceSelector:
                            matchLabels:
                                  kubernetes.io/metadata.name: default
            - from:
                    - namespaceSelector:
                            matchLabels:
                                  kubernetes.io/metadata.name: fhem
            - from:
                    - namespaceSelector:
                            matchLabels:
                                  kubernetes.io/metadata.name: mqtt-switch
            - from:
                    - ipBlock:
                            cidr: ************/24
                    - ipBlock:
                            cidr: ***********/24
                    - ipBlock:
                            cidr: *************/32
                    # Communitaction from the k8s cluster nodes (e.g. pods running with hostNetwork: true)
                    # 0 contabo
                    - ipBlock:
                            cidr: *********/32
                    # 1 ?
                    - ipBlock:
                            cidr: *********/32
                    # 2 home03
                    - ipBlock:
                            cidr: *********/32
                    # 3 home05
                    - ipBlock:
                            cidr: *********/32
                    - ipBlock:
                            cidr: *********/32
                    - ipBlock:
                            cidr: *********/32
                    #   - ipBlock:
                    #         cidr: *********/16
