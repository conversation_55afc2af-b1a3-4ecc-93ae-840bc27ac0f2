prometheus:
  # scrape annotations specifies whether to add Prometheus metric
  # auto-collection annotations to pods. See
  # https://github.com/prometheus/prometheus/blob/release-2.1/documentation/examples/prometheus-kubernetes.yml
  # for a corresponding Prometheus configuration. Alternatively, you
  # may want to use the Prometheus Operator
  # (https://github.com/coreos/prometheus-operator) for more powerful
  # monitoring configuration. If you use the Prometheus operator, this
  # can be left at false.
  scrapeAnnotations: true

# controller contains configuration specific to the MetalLB cluster
# controller.
controller:
  enabled: true
  # -- Controller log level. Must be one of: `all`, `debug`, `info`, `warn`, `error` or `none`
  logLevel:
    info
    # limits:
    # cpu: 100m
    # memory: 100Mi
  nodeSelector:
    location: oracle
    # kubernetes.io/os: linux

# speaker contains configuration specific to the MetalLB speaker
# daemonset.
speaker:
  enabled: true
  # command: /speaker
  # -- Speaker log level. Must be one of: `all`, `debug`, `info`, `warn`, `error` or `none`
  logLevel: info
  tolerateMaster: true
  nodeSelector:
    location: home
  # nodeSelector:
  #   location: home
  # kubernetes.io/os: linux

crds:
  enabled: true
  validationFailurePolicy: Fail
