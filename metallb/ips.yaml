apiVersion: metallb.io/v1beta1
kind: IPAddressPool
metadata:
  name: default-pool
  namespace: metallb
spec:
  addresses:
    - *************-*************
---
apiVersion: metallb.io/v1beta1
kind: L2Advertisement
metadata:
  name: default
  namespace: metallb
spec:
  ipAddressPools:
    - default-pool
  nodeSelectors:
    - matchLabels:
        location: home
  interfaces:
    - enp3s0
    - enp1s0
    - eno1
    - enp2s0
    - eth0
