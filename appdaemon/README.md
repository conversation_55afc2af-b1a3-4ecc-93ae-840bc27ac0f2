# AppDaemon Git-Sync Configuration

This guide explains how to configure AppDaemon to automatically sync configuration from a private GitHub repository using a git-sync sidecar container.

## Overview

The git-sync sidecar container will:
- Clone your private `appdaemon-config` repository on startup
- Continuously sync changes from the repository every 60 seconds
- Make the configuration available to the AppDaemon container at `/conf`
- Support GitHub token authentication for private repositories
- Replace the need for a persistent volume (PVC)

## Prerequisites

1. GitHub personal access token for accessing the private repository
2. Access to create Kubernetes secrets
3. AppDaemon deployment running in the `appdaemon` namespace

## Setup Instructions

### Step 1: Create GitHub Personal Access Token

1. Go to https://github.com/settings/tokens?type=beta
2. Click "Generate new token" → "Generate new token (fine-grained)"
3. Token name: `appdaemon-k8s-readonly`
4. Expiration: Choose your preference (90 days recommended)
5. Repository access: Select "Selected repositories" → Choose `pajikos/appdaemon-config`
6. Repository permissions: Contents → Read
7. Click "Generate token" and save it

### Step 2: Update the Secret File

Edit the secret file and replace the placeholder with your token:

```bash
# Edit the secret file
vim appdaemon/kustomize/secret.yaml

# Replace REPLACE_WITH_YOUR_GITHUB_TOKEN with your actual token
```

### Step 3: Deploy the Changes

```bash
# Commit and push
git add appdaemon/
git commit -m "Add git-sync sidecar for AppDaemon config synchronization"
git push

# ArgoCD will sync automatically, or manually sync:
argocd app sync appdaemon
```

## What This Setup Does

- **Init Container**: Clones your git repository before AppDaemon starts (prevents AppDaemon from creating default files)
- **Sidecar Container**: Continuously syncs changes every 60 seconds from your GitHub repository
- **No PVC Required**: Configuration is stored entirely in Git
- **Writable Directory**: Uses emptyDir volume so AppDaemon can write logs, cache, etc.

## Configuration Structure

Your `appdaemon-config` repository should have this structure:
```
├── appdaemon.yaml      # Main configuration
├── apps/               # Your AppDaemon apps
│   ├── hello.py
│   └── ...
├── dashboards/         # Optional HADashboard configs
└── secrets.yaml        # Home Assistant token and other secrets
```

## Git-Sync Environment Variables

| Variable | Description | Current Value |
|----------|-------------|---------------|
| `GITSYNC_REPO` | Repository URL | `https://github.com/pajikos/appdaemon-config.git` |
| `GITSYNC_REF` | Branch/tag to sync | `main` |
| `GITSYNC_PERIOD` | Sync interval | `60s` |
| `GITSYNC_DEPTH` | Clone depth | `1` (shallow) |


## Troubleshooting

### Check Git-Sync Logs
```bash
kubectl logs -n appdaemon deployment/appdaemon -c git-sync
```

### Verify SSH Key Secret
```bash
kubectl get secret -n appdaemon appdaemon-git-ssh -o yaml
```


### Common Issues

1. **Authentication Failed**: Verify token has read access to the repository
2. **Repository Not Found**: Check repository name and token permissions
3. **Sync Not Working**: Check git-sync logs for specific errors
4. **AppDaemon Can't Write Files**: The emptyDir volume should be writable
5. **Permission Denied Errors**: Delete the pod to get fresh volumes: `kubectl delete pod -n appdaemon -l app.kubernetes.io/name=appdaemon`

## Important Notes

1. **No PVC Migration Needed**: The old PVC (`appdaemon-config-lg`) is no longer used
2. **Config in Git**: All configuration must be committed to your Git repository
3. **Writable Directories**: AppDaemon can write to `/conf` for logs, compiled dashboards, and cache
4. **Token Security**: Never commit the actual token to Git


## Testing the Setup

1. Make a change to your `appdaemon-config` repository
2. Push the change to GitHub
3. Wait up to 60 seconds for the sync
4. Verify the change is reflected:
   ```bash
   kubectl exec -n appdaemon deployment/appdaemon -c appdaemon -- ls -la /conf/
   kubectl exec -n appdaemon deployment/appdaemon -c appdaemon -- cat /conf/apps/your-app.py
   ```

## References

- [Git-Sync Documentation](https://github.com/kubernetes/git-sync)
- [AppDaemon Documentation](https://appdaemon.readthedocs.io/)
- [Kubernetes Secrets](https://kubernetes.io/docs/concepts/configuration/secret/)