apiVersion: v1
kind: Service
metadata:
  name: appdaemon
  labels:
    app.kubernetes.io/service: appdaemon
    app.kubernetes.io/instance: appdaemon
    app.kubernetes.io/name: appdaemon
  annotations:
spec:
  type: ClusterIP
  ports:
    - port: 5050
      targetPort: http
      protocol: TCP
      name: http
  selector:
    app.kubernetes.io/instance: appdaemon
    app.kubernetes.io/name: appdaemon
