apiVersion: apps/v1
kind: Deployment
metadata:
  name: appdaemon
  labels:
    app.kubernetes.io/instance: appdaemon
    app.kubernetes.io/name: appdaemon
spec:
  revisionHistoryLimit: 3
  replicas: 1
  strategy:
    type: Recreate
  selector:
    matchLabels:
      app.kubernetes.io/name: appdaemon
      app.kubernetes.io/instance: appdaemon
  template:
    metadata:
      labels:
        app.kubernetes.io/name: appdaemon
        app.kubernetes.io/instance: appdaemon
    spec:
      serviceAccountName: default
      automountServiceAccountToken: true
      securityContext:
        fsGroup: 65533
      initContainers:
        - name: git-clone
          image: registry.k8s.io/git-sync/git-sync:v4.2.1
          env:
            - name: GITSYNC_REPO
              value: "https://github.com/pajikos/appdaemon-config.git"
            - name: GITSYNC_REF
              value: "main"
            - name: GITSYNC_ROOT
              value: "/tmp/git"
            - name: GIT<PERSON>YNC_LINK
              value: "current"
            - name: GITSYNC_ONE_TIME
              value: "true"
            - name: GITSYNC_DEPTH
              value: "1"
            - name: GITSYNC_USERNAME
              valueFrom:
                secretKeyRef:
                  name: appdaemon-git-token
                  key: username
            - name: GITSYNC_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: appdaemon-git-token
                  key: password
          volumeMounts:
            - name: git-config
              mountPath: /tmp/git
          securityContext:
            runAsUser: 65533
            runAsGroup: 65533
      containers:
        - name: appdaemon
          image: acockburn/appdaemon:4.4.2
          env:
            - name: ELEVATION
              value: "497"
            - name: LATITUDE
              value: "49.00313898"
            - name: LONGITUDE
              value: "14.54330206"
            - name: TZ
              value: Europe/Prague
          ports:
            - name: http
              containerPort: 5050
              protocol: TCP
          volumeMounts:
            - name: git-config
              mountPath: /conf
              subPath: current
          livenessProbe:
            failureThreshold: 3
            initialDelaySeconds: 0
            periodSeconds: 10
            tcpSocket:
              port: 5050
            timeoutSeconds: 1
          readinessProbe:
            failureThreshold: 3
            initialDelaySeconds: 0
            periodSeconds: 10
            tcpSocket:
              port: 5050
            timeoutSeconds: 1
          startupProbe:
            failureThreshold: 30
            initialDelaySeconds: 0
            periodSeconds: 5
            tcpSocket:
              port: 5050
            timeoutSeconds: 1
          resources:
            requests:
              memory: "256Mi"
              cpu: "100m"
            limits:
              memory: "512Mi"
              cpu: "500m"
        - name: git-sync
          image: registry.k8s.io/git-sync/git-sync:v4.2.1
          env:
            - name: GITSYNC_REPO
              value: "https://github.com/pajikos/appdaemon-config.git"
            - name: GITSYNC_REF
              value: "main"
            - name: GITSYNC_ROOT
              value: "/tmp/git"
            - name: GITSYNC_LINK
              value: "current"
            - name: GITSYNC_PERIOD
              value: "60s"
            - name: GITSYNC_DEPTH
              value: "1"
            - name: GITSYNC_USERNAME
              valueFrom:
                secretKeyRef:
                  name: appdaemon-git-token
                  key: username
            - name: GITSYNC_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: appdaemon-git-token
                  key: password
          volumeMounts:
            - name: git-config
              mountPath: /tmp/git
          securityContext:
            runAsUser: 65533
            runAsGroup: 65533
          resources:
            requests:
              memory: "32Mi"
              cpu: "10m"
            limits:
              memory: "128Mi"
              cpu: "100m"
      volumes:
        - name: git-config
          emptyDir: {}
      nodeSelector:
        kubernetes.io/hostname: home03
        location: home
