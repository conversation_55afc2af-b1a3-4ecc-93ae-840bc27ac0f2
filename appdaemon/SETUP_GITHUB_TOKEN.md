# GitHub Token Setup for AppDaemon Git-Sync

## Quick Setup

1. **Create a GitHub Personal Access Token**
   - Go to https://github.com/settings/tokens?type=beta
   - Click "Generate new token" → "Generate new token (fine-grained)"
   - Token name: `appdaemon-k8s-readonly`
   - Expiration: Choose your preference (90 days recommended)
   - Repository access: Select "Selected repositories" → Choose `pajikos/appdaemon-config`
   - Repository permissions: 
     - Contents: Read
   - Click "Generate token"
   - **SAVE THE TOKEN** - you won't be able to see it again!

2. **Update the Secret File**
   ```bash
   # Edit the secret file
   vim appdaemon/kustomize/secret.yaml
   
   # Replace REPLACE_WITH_YOUR_GITHUB_TOKEN with your actual token
   ```

3. **Deploy the Changes**
   ```bash
   # Commit and push
   git add appdaemon/
   git commit -m "Add git-sync sidecar for AppDaemon config synchronization"
   git push
   
   # ArgoCD will sync automatically, or manually sync:
   argocd app sync appdaemon
   
   # If you get permission errors, delete the pod to force a fresh start:
   kubectl delete pod -n appdaemon -l app.kubernetes.io/name=appdaemon
   ```

## What This Setup Does

- **Init Container**: Clones your config repo before AppDaemon starts
- **Sidecar Container**: Continuously syncs changes every 60 seconds
- **Mount Path**: Config is mounted at `/conf/apps/` inside AppDaemon container
- **Read-Only**: The synced config is read-only to prevent accidental modifications

## Testing

After deployment, verify the setup:

```bash
# Check if git-sync containers are running
kubectl get pods -n appdaemon

# Check git-sync logs
kubectl logs -n appdaemon deployment/appdaemon -c git-sync

# Check if config was synced
kubectl exec -n appdaemon deployment/appdaemon -c appdaemon -- ls -la /conf/apps/
```

## Troubleshooting

1. **Authentication Failed**
   - Verify token has read access to the repository
   - Check secret was created correctly: `kubectl get secret -n appdaemon appdaemon-git-token`

2. **Repository Not Found**
   - Ensure the repository is not deleted or renamed
   - Verify token has access to the specific repository

3. **Sync Not Working**
   - Check git-sync logs: `kubectl logs -n appdaemon deployment/appdaemon -c git-sync`
   - Verify network connectivity to GitHub

## Security Notes

- The token is stored as a Kubernetes secret
- Use fine-grained tokens with minimal permissions (read-only)
- Rotate tokens periodically
- Never commit the actual token to git