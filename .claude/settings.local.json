{"permissions": {"allow": ["<PERSON><PERSON>(chmod:*)", "<PERSON><PERSON>(sed:*)", "Bash(grep:*)", "Bash(for:*)", "Bash(do sed -i '' 's/^      - CreateNamespace=true/    - CreateNamespace=true/g' \"$file\")", "Bash(done)", "mcp__ide__getDiagnostics", "Bash(./scripts/validate-local.sh:*)", "<PERSON><PERSON>(yamllint:*)", "WebFetch(domain:appdaemon.readthedocs.io)"], "deny": []}, "enableAllProjectMcpServers": false}