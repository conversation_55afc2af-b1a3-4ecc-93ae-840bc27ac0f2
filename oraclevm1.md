# Setup wireguard on Oracle VM

Source: 

* https://www.digitalocean.com/community/tutorials/how-to-set-up-wireguard-on-ubuntu-22-04
* https://vegastack.com/tutorials/how-to-set-up-wireguard-vpn-on-ubuntu-22-04/
* https://cloudinfrastructureservices.co.uk/how-to-install-wireguard-on-ubuntu-20-04-22-04-step-by-step/
* https://www.reddit.com/r/WireGuard/comments/oxmcvx/cant_seem_to_get_wireguard_working_on_oracle/

Bash history:
```
sudo apt update
sudo apt install wireguard
wg genkey | sudo tee /etc/wireguard/private.key
sudo chmod go= /etc/wireguard/private.key
sudo cat /etc/wireguard/private.key | wg pubkey | sudo tee /etc/wireguard/public.key
sudo nano /etc/wireguard/wg0.conf
```

```bash
[Interface]
Address = ***********/24
SaveConfig = true
PostUp = /etc/wireguard/helper/add-nat-routing.sh
PostDown = /etc/wireguard/helper/remove-nat-routing.sh
ListenPort = 51820
PrivateKey = gAaz/BQF0qLwZxKp+iIUmG+/CHD8f1RK5aqejy4+EHo=

[Peer]
PublicKey = wvpiKut6wcVkwv/bJ3cap8MysiBL5hUbmf1PWkTjbCI=
AllowedIPs = ***********/32
Endpoint = ***********:44028
```

```bash
sudo sysctl -p
sudo <NAME_EMAIL>
sudo <NAME_EMAIL>
sudo <NAME_EMAIL>
sudo wg set wg0 peer wvpiKut6wcVkwv/bJ3cap8MysiBL5hUbmf1PWkTjbCI= allowed-ips ***********
```

```bash
ubuntu@oraclevm1:~$ sudo cat /etc/wireguard/helper/add-nat-routing.sh
#!/bin/bash

IPT="/sbin/iptables"
IPT6="/sbin/ip6tables"

IN_FACE="ens3"                   # NIC connected to the internet
WG_FACE="wg0"                    # WG NIC
NEBULA_FACE="nebula1"
SUB_NET="***********/24"          # WG IPv4 sub/net aka CIDR
WG_PORT="51820"                  # WG udp port
SUB_NET_6="fd42:42:42::/64"      # WG IPv6 sub/net

## IPv4 ##
$IPT -t nat -I POSTROUTING 1 -s $SUB_NET -o $IN_FACE -j MASQUERADE
$IPT -I INPUT 1 -i $WG_FACE -j ACCEPT
$IPT -I FORWARD 1 -i $IN_FACE -o $WG_FACE -j ACCEPT
$IPT -I FORWARD 1 -i $WG_FACE -o $IN_FACE -j ACCEPT
$IPT -I INPUT 1 -i $IN_FACE -p udp --dport $WG_PORT -j ACCEPT
# Nebula
$IPT -I FORWARD 1 -i $WG_FACE -o $NEBULA_FACE -j ACCEPT
$IPT -t nat -I POSTROUTING 1 -s $SUB_NET -o $NEBULA_FACE -j MASQUERADE
$IPT -I FORWARD 1 -i $NEBULA_FACE -o $WG_FACE -j ACCEPT
## IPv6 (Uncomment) ##
#$IPT6 -t nat -I POSTROUTING 1 -s $SUB_NET_6 -o $IN_FACE -j MASQUERADE
#$IPT6 -I INPUT 1 -i $WG_FACE -j ACCEPT
#$IPT6 -I FORWARD 1 -i $IN_FACE -o $WG_FACE -j ACCEPT
#$IPT6 -I FORWARD 1 -i $WG_FACE -o $IN_FACE -j ACCEPT
```

```bash
ubuntu@oraclevm1:~$ sudo cat /etc/wireguard/helper/remove-nat-routing.sh
#!/bin/bash
IPT="/sbin/iptables"
IPT6="/sbin/ip6tables"

IN_FACE="ens3"                   # NIC connected to the internet
WG_FACE="wg0"                    # WG NIC
NEBULA_FACE="nebula1"
SUB_NET="***********/24"          # WG IPv4 sub/net aka CIDR
WG_PORT="51820"                  # WG udp port
SUB_NET_6="fd42:42:42::/64"      # WG IPv6 sub/net

# IPv4 rules #
$IPT -t nat -D POSTROUTING -s $SUB_NET -o $IN_FACE -j MASQUERADE
$IPT -D INPUT -i $WG_FACE -j ACCEPT
$IPT -D FORWARD -i $IN_FACE -o $WG_FACE -j ACCEPT
$IPT -D FORWARD -i $WG_FACE -o $IN_FACE -j ACCEPT
$IPT -D INPUT -i $IN_FACE -p udp --dport $WG_PORT -j ACCEPT
#Nebula
$IPT -D FORWARD -i $WG_FACE -o $NEBULA_FACE -j ACCEPT
$IPT -t nat -D POSTROUTING -s $SUB_NET -o $NEBULA_FACE -j MASQUERADE
$IPT -D FORWARD -i $NEBULA_FACE -o $WG_FACE -j ACCEPT
# IPv6 rules (uncomment) #
#$IPT6 -t nat -D POSTROUTING -s $SUB_NET_6 -o $IN_FACE -j MASQUERADE
#$IPT6 -D INPUT -i $WG_FACE -j ACCEPT
#$IPT6 -D FORWARD -i $IN_FACE -o $WG_FACE -j ACCEPT
#$IPT6 -D FORWARD -i $WG_FACE -o $IN_FACE -j ACCEPT
```

    