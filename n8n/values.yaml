# Default helm values for n8n.
# Default values within the n8n application can be found under https://github.com/n8n-io/n8n/blob/master/packages/cli/config/index.ts

# Global image settings
image:
  repository: n8nio/n8n
  pullPolicy: Always

# Main n8n application configuration
main:
  config:
    n8n:
      encryption_key: 7k6t4o4CZsP72leNt7K91Ue6TAZZ3q5V
    database:
      type: sqlite # Type of database to use - Other possible types ['sqlite', 'mariadb', 'mysqldb', 'postgresdb'] - default: sqlite
    executions:
      timeout: 3600 # Max run time (seconds) before stopping the workflow execution - default: -1
      saveDataOnError: all # What workflow execution data to save on error - possible values [all , none] - default: all
      saveDataOnSuccess: none # What workflow execution data to save on success - possible values [all , none] - default: all
      saveDataManualExecutions: "false" # Save data of executions when started manually via editor - default: false
      pruneData: "true" # Delete data of past executions on a rolling basis - default: false
      pruneDataMaxAge: 168 # How old (hours) the execution data has to be to get deleted - default: 336
    generic:
      timezone: Europe/Prague # The timezone to use - default: America/New_York
    host: n8n.k8s.sklenarovi.cz # Host name n8n can be reached - default: localhost
    port: 5678 # HTTP port n8n can be reached - default: 5678
    protocol: https # HTTP Protocol via which n8n can be reached - possible values [http , https] - default: http

  # Dict with all n8n json config options, unlike config the values here will end up in a secret.
  secret: {}

  # Set additional environment variables on the Deployment
  extraEnv:
    N8N_RUNNERS_ENABLED:
      value: "true"
    DB_SQLITE_VACUUM_ON_STARTUP:
      value: "true"
    # N8N_BASIC_AUTH_ACTIVE: "true"
    # N8N_BASIC_AUTH_USER: pavel
    # N8N_BASIC_AUTH_PASSWORD: heslo
    # Set this if running behind a reverse proxy and the external port is different from the port n8n runs on
    WEBHOOK_URL:
      value: "https://n8n.k8s.sklenarovi.cz/"

  # Set additional environment from existing secrets
  extraEnvSecrets: {}

  # # Persistence configuration
  persistence:
    # # If true, use a Persistent Volume Claim, If false, use emptyDir
    # #
    enabled: true
    type: existing # what type volume, possible options are [existing, emptyDir, dynamic] dynamic for Dynamic Volume Provisioning, existing for using an existing Claim
    # # Persistent Volume Access Mode
    # #
    existingClaim: n8n-lh

  # Number of replicas
  replicaCount: 1

  deploymentStrategy:
    type: "Recreate"

  nodeSelector:
    location: oracle

# Worker configuration (disabled by default)
worker:
  enabled: false

# Webhook configuration (disabled by default)
webhook:
  enabled: false

# Ingress configuration
ingress:
  enabled: true
  annotations:
    cert-manager.io/cluster-issuer: letsencrypt-cloudflare-sklenarovi
  # annotations:
  #   nginx.ingress.kubernetes.io/auth-url: "https://oauth.k8s.sklenarovi.cz/oauth2/auth"
  #   nginx.ingress.kubernetes.io/auth-signin: "https://oauth.k8s.sklenarovi.cz/oauth2/start?rd=$pass_access_scheme://$http_host$escaped_request_uri"
  hosts:
    - host: n8n.k8s.sklenarovi.cz
      paths: ["/"]
  tls:
    - secretName: n8n-k8s-sklenarovi-cz-tls
      hosts:
        - "n8n.k8s.sklenarovi.cz"
  # define a custom ingressClassName, like "traefik" or "nginx"
  className: ""

# Scaling configuration for worker/webhook instances
scaling:
  enabled: false

# # Bitnami Redis/Valkey configuration
# # https://github.com/bitnami/charts/tree/master/bitnami/redis
redis:
  enabled: false

# # Valkey (Redis replacement) configuration
valkey:
  enabled: false
