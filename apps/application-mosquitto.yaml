apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: mosquitto
  annotations:
    argocd-image-updater.argoproj.io/image-list: eclipse-mosquitto:^2.0.15
    argocd-image-updater.argoproj.io/write-back-method: git
    argocd-image-updater.argoproj.io/git-branch: main
  namespace: argocd
spec:
  destination:
    server: https://kubernetes.default.svc
    namespace: mosquitto
  project: default
  source:
    repoURL: '**************:pajikos/home-iaac.git'
    path: mosquitto/chart
    targetRevision: HEAD
    helm:
      valueFiles:
      - ../values.yaml
  syncPolicy:
    syncOptions:
    - CreateNamespace=true
