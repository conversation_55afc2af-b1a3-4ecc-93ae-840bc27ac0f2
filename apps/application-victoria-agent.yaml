apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: victoria-metrics-agent
  namespace: argocd
spec:
  destination:
    server: https://kubernetes.default.svc
    namespace: victoria
  project: default
  sources:
    - repoURL: "https://victoriametrics.github.io/helm-charts"
      chart: victoria-metrics-agent
      targetRevision: 0.*
      helm:
        valueFiles:
          - $values/victoria-metrics/agent-values.yaml
    - repoURL: "**************:pajikos/home-iaac.git"
      targetRevision: HEAD
      ref: values
  syncPolicy:
    managedNamespaceMetadata:
      labels:
        level: monitoring
    syncOptions:
      - CreateNamespace=true
