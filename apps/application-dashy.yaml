apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: dashy
  # annotations:
  #   argocd-image-updater.argoproj.io/image-list: lissy93/dashy:^4.4.0
  #   # argocd-image-updater.argoproj.io/write-back-method: git
  #   argocd-image-updater.argoproj.io/git-branch: main
  namespace: argocd
spec:
  destination:
    server: https://kubernetes.default.svc
    namespace: dashy
  project: default
  source:
    repoURL: "**************:pajikos/home-iaac.git"
    path: dashy/kustomize
    targetRevision: HEAD
  syncPolicy:
    syncOptions:
    - CreateNamespace=true
