apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: metallb
  namespace: argocd
spec:
  destination:
    server: https://kubernetes.default.svc
    namespace: metallb
  project: default
  sources:
    - repoURL: "https://metallb.github.io/metallb"
      chart: metallb
      targetRevision: 0.*
      helm:
        valueFiles:
          - $values/metallb/values.yaml
    - repoURL: "**************:pajikos/home-iaac.git"
      targetRevision: HEAD
      ref: values
  syncPolicy:
    syncOptions:
      - CreateNamespace=true
  ignoreDifferences:
    - group: apiextensions.k8s.io
      kind: CustomResourceDefinition
      name: addresspools.metallb.io
      jsonPointers:
        - /spec/conversion/webhook/clientConfig/caBundle
    - group: apiextensions.k8s.io
      kind: CustomResourceDefinition
      name: bgppeers.metallb.io
      jsonPointers:
        - /spec/conversion/webhook/clientConfig/caBundle
