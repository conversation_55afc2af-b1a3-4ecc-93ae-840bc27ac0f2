apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: registryui
  namespace: argocd
spec:
  destination:
    server: https://kubernetes.default.svc
    namespace: registry
  project: default
  sources:
  - repoURL: 'https://helm.joxit.dev'
    chart: docker-registry-ui
    targetRevision: 1.*
    helm:
      valueFiles:
      - $values/registry/values-registryui.yaml
  - repoURL: '**************:pajikos/home-iaac.git'
    targetRevision: HEAD
    ref: values
  syncPolicy:
    syncOptions:
    - CreateNamespace=true
