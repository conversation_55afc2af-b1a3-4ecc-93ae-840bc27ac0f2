apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: openwebui
  namespace: argocd
spec:
  destination:
    server: https://kubernetes.default.svc
    namespace: openwebui
  project: default
  sources:
    - repoURL: "https://helm.openwebui.com/"
      chart: open-webui
      targetRevision: 5.*
      helm:
        releaseName: open-webui
        valueFiles:
          - $values/openwebui/values.yaml
    - repoURL: '**************:pajikos/home-iaac.git'
      targetRevision: HEAD
      ref: values
  syncPolicy:
    syncOptions:
      - CreateNamespace=true
