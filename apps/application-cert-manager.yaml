apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: cert-manager
  namespace: argocd
spec:
  destination:
    server: https://kubernetes.default.svc
    namespace: cert-manager
  project: default
  sources:
    - repoURL: "https://charts.jetstack.io"
      chart: cert-manager
      targetRevision: v1.*
      helm:
        valueFiles:
          - $values/cert-manager/values.yaml
    - repoURL: "**************:pajikos/home-iaac.git"
      targetRevision: HEAD
      ref: values
  syncPolicy:
    syncOptions:
      - CreateNamespace=true
    automated:
      prune: true
      selfHeal: true
  ignoreDifferences:
    - group: apiextensions.k8s.io
      kind: CustomResourceDefinition
      jsonPointers:
        - /spec/conversion/webhook/clientConfig/caBundle
