apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: nginx-app
  namespace: argocd
spec:
  project: default
  source:
    chart: ingress-nginx
    repoURL: https://kubernetes.github.io/ingress-nginx
    targetRevision: 4.*
    helm:
      releaseName:
        ingress-nginx
        # Values file as block file
      values: |

        controller:

          admissionWebhooks:
            enabled: false
            name: admission
            failurePolicy: Ignore

          tolerations:
          - key: "preparing"
            operator: "Equal"
            value: "yes"
            effect: "NoSchedule"

          config:
            whitelist-source-range: ************/24,***********/24,*************/32,*********/16,*********/16,**********/24
          # -- Configures the ports that the nginx-controller listens on
          containerPort:
            http: 80
            https: 443

          # -- Optionally change this to ClusterFirstWithHostNet in case you have 'hostNetwork: true'.
          # By default, while using host network, name resolution uses the host's DNS. If you wish nginx-controller
          # to keep resolving names inside the k8s network, use ClusterFirstWithHostNet.
          dnsPolicy: ClusterFirst

          # -- Bare-metal considerations via the host network
          # https://kubernetes.github.io/ingress-nginx/deploy/baremetal/#via-the-host-network
          # Ingress status was blank because there is no Service exposing the NGINX Ingress controller
          # in a configuration using the host network, the default --publish-service flag used in
          # standard cloud setups does not apply
          reportNodeInternalIp: true

          # -- Process Ingress objects without ingressClass annotation/ingressClassName field
          # Overrides value for --watch-ingress-without-class flag of the controller binary
          # Defaults to false
          watchIngressWithoutClass: true

          # -- Process IngressClass per name (additionally as per spec.controller).
          ingressClassByName: false

          # -- This configuration defines if Ingress Controller should allow users to set
          # their own *-snippet annotations, otherwise this is forbidden / dropped
          # when users add those annotations.
          # Global snippets in ConfigMap are still respected
          allowSnippetAnnotations: true

          # -- Required for use with CNI based kubernetes installations (such as ones set up by kubeadm),
          # since CNI and hostport don't mix yet. Can be deprecated once https://github.com/kubernetes/kubernetes/issues/23920
          # is merged
          hostNetwork: false

          # # Use host ports 80 and 443
          # # Disabled by default
          hostPort:
            # -- Enable 'hostPort' or not
            enabled: true
            ports:
              # -- 'hostPort' http port
              http: 80
              # -- 'hostPort' https port
              https: 443

          # -- Use a `DaemonSet` or `Deployment`
          kind: DaemonSet

          # nodeSelector:
          #   kubernetes.io/os: linux

          # # Define requests resources to avoid probe issues due to CPU utilization in busy nodes
          # # ref: https://github.com/kubernetes/ingress-nginx/issues/4735#issuecomment-551204903
          # # Ideally, there should be no limits.
          # # https://engineering.indeedblog.com/blog/2019/12/cpu-throttling-regression-fix/
          resources:
          # #  limits:
          # #    cpu: 100m
          # #    memory: 90Mi
            requests:
              cpu: 100m
              memory: 90Mi

          service:
            enabled: true
            externalTrafficPolicy: "Local"

          metrics:
            enabled: true
            service:
              annotations:
                prometheus.io/scrape: "true"
                prometheus.io/port: "10254"

        # # Default 404 backend
        # #
        defaultBackend:
          # #
          enabled: false

        # -- TCP service key-value pairs
        # # Ref: https://github.com/kubernetes/ingress-nginx/blob/main/docs/user-guide/exposing-tcp-udp-services.md
        # #
        # tcp:
        #   8555: "frigate/frigate:8555"
        #   8554: "frigate/frigate:8554"

        # # -- UDP service key-value pairs
        # ## Ref: https://github.com/kubernetes/ingress-nginx/blob/main/docs/user-guide/exposing-tcp-udp-services.md
        # ##
        # udp:
        #   8555: "frigate/frigate:8555"
        # #  53: "kube-system/kube-dns:53"

  destination:
    server: https://kubernetes.default.svc
    namespace: nginx-controller
  syncPolicy:
    automated:
      prune: true
    syncOptions:
    - CreateNamespace=true
