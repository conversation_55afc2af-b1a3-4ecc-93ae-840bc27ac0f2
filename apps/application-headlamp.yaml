apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: headlamp-app
  namespace: argocd
spec:
  project: default
  source:
    chart: headlamp
    repoURL: https://headlamp-k8s.github.io/headlamp/
    targetRevision: 0.*
    helm:
      releaseName:
        headlamp
        # Values file as block file
      values: |
        ingress:
          enabled: true
          # Values can be templated
          annotations:
            cert-manager.io/cluster-issuer: letsencrypt-cloudflare-sklenarovi
            nginx.ingress.kubernetes.io/auth-url: "https://oauth.k8s.sklenarovi.cz/oauth2/auth"
            nginx.ingress.kubernetes.io/auth-signin: "https://oauth.k8s.sklenarovi.cz/oauth2/start?rd=$pass_access_scheme://$http_host$escaped_request_uri"
          labels: {}

          hosts:
            - host: headlamp.k8s.sklenarovi.cz
              paths:
              - path: /
                type: ImplementationSpecific
          # -- Ingress TLS configuration
          tls:
           - secretName: headlamp-k8s-sklenarovi-cz-tls
             hosts:
               - headlamp.k8s.sklenarovi.cz

  destination:
    server: https://kubernetes.default.svc
    namespace: headlamp
  syncPolicy:
    automated:
      prune: true
    syncOptions:
    - CreateNamespace=true
