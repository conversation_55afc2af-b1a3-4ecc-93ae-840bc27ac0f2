apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: loki
  namespace: argocd
spec:
  destination:
    server: https://kubernetes.default.svc
    namespace: loki
  project: default
  sources:
  - repoURL: 'https://grafana.github.io/helm-charts'
    chart: loki-stack
    targetRevision: 2.*
    helm:
      valueFiles:
      - $values/loki-stack/values.yaml
  - repoURL: '**************:pajikos/home-iaac.git'
    targetRevision: HEAD
    ref: values
  syncPolicy:
    syncOptions:
    - CreateNamespace=true
