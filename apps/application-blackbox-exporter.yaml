apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: blackbox-exporter
  namespace: argocd
spec:
  destination:
    server: https://kubernetes.default.svc
    namespace: victoria
  project: default
  sources:
  - chart: prometheus-blackbox-exporter
    repoURL: 'https://prometheus-community.github.io/helm-charts'
    targetRevision: 7.*
    helm:
      valueFiles:
      - $values/blackbox-exporter/values.yaml
  - repoURL: '**************:pajikos/home-iaac.git'
    targetRevision: HEAD
    ref: values
  syncPolicy:
    syncOptions:
    - CreateNamespace=true
