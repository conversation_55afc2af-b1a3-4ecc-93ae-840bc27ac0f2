apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: unifi
  namespace: argocd
spec:
  destination:
    server: https://kubernetes.default.svc
    namespace: unifi
  project: default
  sources:
    - repoURL: "ghcr.io/mkilchhofer/unifi-chart"
      chart: unifi
      targetRevision: 1.*
      helm:
        releaseName: unifi
        valueFiles:
          - $values/unifi/values.yaml
    - repoURL: "**************:pajikos/home-iaac.git"
      targetRevision: HEAD
      ref: values
  syncPolicy:
    syncOptions:
      - CreateNamespace=true
