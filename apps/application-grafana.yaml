apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: grafana
  namespace: argocd
spec:
  destination:
    server: https://kubernetes.default.svc
    namespace: grafana
  project: default
  sources:
  - repoURL: 'https://grafana.github.io/helm-charts'
    chart: grafana
    targetRevision: 9.*
    helm:
      valueFiles:
      - $values/grafana/values.yaml
  - repoURL: '**************:pajikos/home-iaac.git'
    targetRevision: HEAD
    ref: values
  syncPolicy:
    syncOptions:
    - CreateNamespace=true
