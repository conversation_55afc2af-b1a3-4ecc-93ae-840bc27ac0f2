apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: snmp-exporter-synology
  namespace: argocd
spec:
  destination:
    server: https://kubernetes.default.svc
    namespace: snmp-exporter
  project: default
  sources:
    - repoURL: "https://prometheus-community.github.io/helm-charts"
      chart: prometheus-snmp-exporter
      targetRevision: 1.*
      helm:
        values: |

          extraConfigmapMounts:
          - name: snmp-exporter-configmap
            mountPath: /etc/snmp_exporter/
            # subPath: snmp.yaml # (optional)
            configMap: snmp-exporter-synology-configmap
            readOnly: true
            defaultMode: 420

          # Values
          nodeSelector:
            location: home
  syncPolicy:
    syncOptions:
      - CreateNamespace=true
