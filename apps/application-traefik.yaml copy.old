apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: traefik
  namespace: argocd
spec:
  project: default
  source:
    chart: traefik
    repoURL: https://helm.traefik.io/traefik
    targetRevision: 10.*
    helm:
      releaseName: traefik
        # Values file as block file
      values: |

        deployment:
          # Can be either Deployment or DaemonSet
          kind: DaemonSet
        
        env:
          - name: WEDOS_USERNAME
            value: <EMAIL>
          - name: WEDOS_WAPI_PASSWORD
            value: pajikWapi7!
        
        # # Enable persistence using Persistent Volume Claims
        # # ref: http://kubernetes.io/docs/user-guide/persistent-volumes/
        # # It can be used to store TLS certificates, see `storage` in certResolvers
        # persistence:
        #   enabled: true
        #   name: data
        #   size: 128Mi
        #   # storageClass: ""
        #   # volumeName: ""
        #   path: /data

        # volumes:
        # - name: data-volume
        #   emptyDir:
        #     sizeLimit: 500Mi

        # Additional volumeMounts to add to the Traefik container
        # additionalVolumeMounts:
        # - mountPath: /data
        #   name: data-volume

        # Use ingressClass. Ignored if Traefik version < 2.3 / kubernetes < 1.18.x
        ingressClass:
          # true is not unit-testable yet, pending https://github.com/rancher/helm-unittest/pull/12
          enabled: true
          isDefaultClass: true

        # Activate Pilot integration
        pilot:
          enabled: false
          token: ""
          # Toggle Pilot Dashboard
          dashboard: false

        # Enable experimental features
        experimental:
          plugins:
            enabled: false

        #
        # Configure providers
        #
        providers:
          kubernetesCRD:
            enabled: true
            allowCrossNamespace: true
            allowExternalNameServices: true
            allowEmptyServices: true

          kubernetesIngress:
            enabled: true
            allowExternalNameServices: true
            allowEmptyServices: true


        # Logs
        # https://docs.traefik.io/observability/logs/
        logs:
          # Traefik logs concern everything that happens to Traefik itself (startup, configuration, events, shutdown, and so on).
          general:
            # By default, the logs use a text format (common), but you can
            # also ask for the json format in the format option
            # format: json
            # By default, the level is set to ERROR. Alternative logging levels are DEBUG, PANIC, FATAL, ERROR, WARN, and INFO.
            level: DEBUG
          access:
            # To enable access logs
            enabled: true



        globalArguments:
          - "--global.checknewversion"

        #
        # Configure Traefik static configuration
        # Additional arguments to be passed at Traefik's binary
        # All available options available on https://docs.traefik.io/reference/static-configuration/cli/
        ## Use curly braces to pass values: `helm install --set="additionalArguments={--providers.kubernetesingress.ingressclass=traefik-internal,--log.level=DEBUG}"`
        additionalArguments: 
          - "--serversTransport.insecureSkipVerify=true"
            # - "--certificatesresolvers.azure.acme.dnschallenge.provider=azure"
            # - "--certificatesresolvers.azure.acme.email=<EMAIL>"
            # - "--certificatesresolvers.azure.acme.dnschallenge.resolvers=*******"
            # - "--certificatesresolvers.azure.acme.storage=/certs/acme.json"
        #  - "--providers.kubernetesingress.ingressclass=traefik-internal"
        #  - "--log.level=DEBUG"


        # Configure ports
        ports:
          # The name of this one can't be changed as it is used for the readiness and
          # liveness probes, but you can adjust its config to your liking
          traefik:
            expose: false

          web:
            hostPort: 80
            expose: false
            redirectTo: websecure
          websecure:
            hostPort: 443
            expose: false

          metrics:
            port: 9100
            # hostPort: 9100
            # Defines whether the port is exposed if service.type is LoadBalancer or
            # NodePort.
            #
            # You may not want to expose the metrics port on production deployments.
            # If you want to access it from outside of your cluster,
            # use `kubectl port-forward` or create a secure ingress
            expose: false
            # The exposed port for this service
            exposedPort: 9100
            # The port protocol (TCP/UDP)
            protocol: TCP


        # Options for the main traefik service, where the entrypoints traffic comes
        # from.
        service:
          enabled: false
        
        # certResolvers:
        #   wedos:
        #     # for challenge options cf. https://doc.traefik.io/traefik/https/acme/
        #     email: <EMAIL>
        #     storage: /data/acme.json
        #     caServer: "https://acme-staging-v02.api.letsencrypt.org/directory"
        #     # storage: "traefik/acme/account"
        #     dnsChallenge:
        #       # also add the provider's required configuration under env
        #       # or expand then from secrets/configmaps with envfrom
        #       # cf. https://doc.traefik.io/traefik/https/acme/#providers
        #       provider: wedos
        #       # add futher options for the dns challenge as needed
        #       # cf. https://doc.traefik.io/traefik/https/acme/#dnschallenge
        #       delayBeforeCheck: 30
        #       resolvers:
        #         - *******
        #         - *******


        extraObjects:
          - apiVersion: traefik.containo.us/v1alpha1
            kind: Middleware
            metadata:
              name: ipwhitelist
            spec:
              ipWhiteList:
                sourceRange:
                  - 127.0.0.1/32  
                  - *************/32

          - apiVersion: v1
            kind: Secret
            metadata:
              name: loki-secret-basic-auth
              namespace: traefik
            type: kubernetes.io/basic-auth
            stringData:
              username: pavel      # required field for kubernetes.io/basic-auth
              password: hes123Lo   # required field for kubernetes.io/basic-auth

          # - apiVersion: v1
          #   kind: Secret
          #   metadata:
          #     name: traefik-auth
          #     namespace: traefik
          #   data:
          #     users: |1
          #       "YWRtaW46JGFwcjEkWHcub1JIZDYkTUtvVEFlbHhSTVVEU0JFWjBRMHdSLgoK"

          - apiVersion: traefik.containo.us/v1alpha1
            kind: Middleware
            metadata:
              name: loki-basicauth
              namespace: traefik
            spec:
              basicAuth:
                secret: loki-secret-basic-auth

          - apiVersion: traefik.containo.us/v1alpha1
            kind: Middleware
            metadata:
              name: oauth-auth-wo-redirect
              namespace: traefik
            spec:
              forwardAuth:
                address: https://oauth.k8s.sklenarovi.cz/oauth2/auth
                trustForwardHeader: true
                authResponseHeaders:
                  - X-Auth-Request-Access-Token
                  - Authorization

          - apiVersion: traefik.containo.us/v1alpha1
            kind: Middleware
            metadata:
              name: oauth-auth-redirect
              namespace: traefik
            spec:
              forwardAuth:
                address: https://oauth.k8s.sklenarovi.cz
                trustForwardHeader: true
                authResponseHeaders:
                  - X-Auth-Request-Access-Token
                  - Authorization

          - apiVersion: traefik.containo.us/v1alpha1
            kind: Middleware
            metadata:
              name: auth-headers
              namespace: traefik
            spec:
              headers:
                sslRedirect: true
                stsSeconds: 315360000
                browserXssFilter: true
                contentTypeNosniff: true
                forceSTSHeader: true
                sslHost: k8s.sklenarovi.cz
                stsIncludeSubdomains: true
                stsPreload: true
                frameDeny: true

          - apiVersion: networking.k8s.io/v1
            kind: Ingress
            metadata:
              annotations:
                traefik.ingress.kubernetes.io/router.middlewares: traefik-oauth-auth-wo-redirect@kubernetescrd
                traefik.ingress.kubernetes.io/router.tls: "true"
              name: zigbee2mqtt-no-auto-redirect
              namespace: zigbee2mqtt
            spec:
              ingressClassName: traefik
              rules:
              - host: zigbee2mqtt.k8s.sklenarovi.cz
                http:
                  paths:
                  - backend:
                      service:
                        name: zigbee2mqtt
                        port:
                          number: 8080
                    path: /no-auto-redirect
                    pathType: Prefix
              tls:
              - hosts:
                - zigbee2mqtt.k8s.sklenarovi.cz
                secretName: zigbee2mqtt-k8s-sklenarovi-cz-tls

          - apiVersion: networking.k8s.io/v1
            kind: Ingress
            metadata:
              annotations:
                traefik.ingress.kubernetes.io/router.middlewares: traefik-auth-headers@kubernetescrd
                traefik.ingress.kubernetes.io/router.tls: "true"
              name: zigbee2mqtt-oauth
              namespace: oauth-proxy
            spec:
              ingressClassName: traefik
              rules:
              - host: zigbee2mqtt.k8s.sklenarovi.cz
                http:
                  paths:
                  - backend:
                      service:
                        name: oauth2-proxy
                        port:
                          number: 80
                    path: /oauth2/
                    pathType: Prefix
              tls:
              - hosts:
                - zigbee2mqtt.k8s.sklenarovi.cz
                secretName: zigbee2mqtt-k8s-sklenarovi-cz-tls

          # - apiVersion: traefik.containo.us/v1alpha1
          #   kind: Middleware
          #   metadata:
          #     name: stripprefix-dashboard
          #   spec:
          #     stripPrefix:
          #       prefixes:
          #         - /ceph-dashboard
          
          # - apiVersion: traefik.containo.us/v1alpha1
          #   kind: IngressRoute
          #   metadata:
          #     name: oauth2-cilium
          #     namespace: kube-system
          #   spec:
          #     entryPoints:
          #       - websecure
          #       - web
          #     routes:
          #     - match: Host(`cilium-ui.dev-hub.esc.esetrs.cz`)
          #       kind: Rule
          #       services:
          #       - name: hubble-ui
          #         kind: Service
          #         namespace: kube-system
          #         port: 80
          #       middlewares:
          #       - name: oauth-auth
          #         namespace: traefik

  destination:
    server: https://kubernetes.default.svc
    namespace: traefik
  syncPolicy:
    automated:
      prune: true
    syncOptions:
      - CreateNamespace=true