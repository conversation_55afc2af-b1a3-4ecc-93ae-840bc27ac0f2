apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: home-assistant
  namespace: argocd
spec:
  destination:
    server: https://kubernetes.default.svc
    namespace: home-assistant
  project: default
  sources:
  - repoURL: 'http://pajikos.github.io/home-assistant-helm-chart'
    chart: home-assistant
    targetRevision: 0.*
    helm:
      releaseName: home-assistant
      valueFiles:
      - $values/home-assistant/values.yaml
  - repoURL: '**************:pajikos/home-iaac.git'
    targetRevision: HEAD
    ref: values
  syncPolicy:
    syncOptions:
    - CreateNamespace=true
