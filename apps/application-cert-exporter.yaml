apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: cert-exporter
  namespace: argocd
spec:
  destination:
    server: https://kubernetes.default.svc
    namespace: cert-exporter
  project: default
  sources:
  - repoURL: 'https://joe-elliott.github.io/cert-exporter'
    chart: cert-exporter
    targetRevision: 3.*
    helm:
      valueFiles:
      - $values/cert-exporter/values-new.yaml
  - repoURL: '**************:pajikos/home-iaac.git'
    targetRevision: HEAD
    ref: values
  syncPolicy:
    syncOptions:
    - CreateNamespace=true
