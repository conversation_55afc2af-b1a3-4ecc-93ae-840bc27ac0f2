apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: victoria-metrics
  namespace: argocd
spec:
  destination:
    server: https://kubernetes.default.svc
    namespace: victoria
  project: default
  sources:
  - repoURL: 'https://victoriametrics.github.io/helm-charts'
    chart: victoria-metrics-single
    targetRevision: 0.*
    helm:
      valueFiles:
      - $values/victoria-metrics/values.yaml
  - repoURL: '**************:pajikos/home-iaac.git'
    targetRevision: HEAD
    ref: values
  syncPolicy:
    syncOptions:
    - CreateNamespace=true
