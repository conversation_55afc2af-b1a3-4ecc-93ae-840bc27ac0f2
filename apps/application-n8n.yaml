apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: n8n
  namespace: argocd
spec:
  destination:
    server: https://kubernetes.default.svc
    namespace: n8n
  project: default
  sources:
    - repoURL: "8gears.container-registry.com/library"
      chart: n8n
      targetRevision: 1.*
      helm:
        releaseName: n8n
        valueFiles:
          - $values/n8n/values.yaml
    - repoURL: "**************:pajikos/home-iaac.git"
      targetRevision: HEAD
      ref: values
  syncPolicy:
    syncOptions:
      - CreateNamespace=true
