apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: victoria-metrics-alert
  namespace: argocd
spec:
  destination:
    server: https://kubernetes.default.svc
    namespace: victoria
  project: default
  sources:
  - repoURL: 'https://github.com/VictoriaMetrics/helm-charts.git'
    path: charts/victoria-metrics-alert
    targetRevision: HEAD
    helm:
      releaseName: vmalert
      valueFiles:
      - $values/victoria-metrics/vmalerts-values.yaml
  # - repoURL: 'https://victoriametrics.github.io/helm-charts'
  #   chart: victoria-metrics-alert
  #   targetRevision: 0.*
  #   helm:
  #     releaseName: vmalert
  #     valueFiles:
  #     - $values/victoria-metrics/vmalerts-values.yaml
  - repoURL: '**************:pajikos/home-iaac.git'
    targetRevision: HEAD
    ref: values
  syncPolicy:
    syncOptions:
    - CreateNamespace=true
