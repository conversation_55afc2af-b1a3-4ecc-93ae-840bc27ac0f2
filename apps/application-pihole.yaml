apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: pihole
  namespace: argocd
spec:
  destination:
    server: https://kubernetes.default.svc
    namespace: pihole
  project: default
  sources:
    - repoURL: "https://mojo2600.github.io/pihole-kubernetes"
      chart: pihole
      targetRevision: 2.27.0
      helm:
        releaseName: mojo2600
        valueFiles:
          - $values/pihole/values.yaml
    - repoURL: "**************:pajikos/home-iaac.git"
      targetRevision: HEAD
      ref: values
  syncPolicy:
    syncOptions:
      - CreateNamespace=true
