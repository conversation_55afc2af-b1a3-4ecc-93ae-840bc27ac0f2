apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: frigate
  namespace: argocd
spec:
  destination:
    server: https://kubernetes.default.svc
    namespace: frigate
  project: default
  sources:
  - repoURL: "https://blakeblackshear.github.io/blakeshome-charts/"
    chart: frigate
    targetRevision: 7.*
    helm:
      valueFiles:
      - $values/frigate/values.yaml
  - repoURL: '**************:pajikos/home-iaac.git'
    targetRevision: HEAD
    ref: values
  syncPolicy:
    syncOptions:
    - CreateNamespace=true
