apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: registry
  namespace: argocd
spec:
  destination:
    server: https://kubernetes.default.svc
    namespace: registry
  project: default
  sources:
  - repoURL: 'https://helm.twun.io'
    chart: docker-registry
    targetRevision: 2.*
    helm:
      valueFiles:
      - $values/registry/values.yaml
  - repoURL: '**************:pajikos/home-iaac.git'
    targetRevision: HEAD
    ref: values
  syncPolicy:
    syncOptions:
    - CreateNamespace=true
  ignoreDifferences:
  # for the specified json pointers
  - group: apps
    kind: Deployment
    jqPathExpressions:
    - .spec.template.metadata.annotations["checksum/secret"]
  - group: ""
    kind: Secret
    jqPathExpressions:
    - .data.haSharedSecret
