apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: kube-state-metrics
  namespace: argocd
spec:
  destination:
    server: https://kubernetes.default.svc
    namespace: prometheus
  project: default
  sources:
  - repoURL: 'https://prometheus-community.github.io/helm-charts'
    chart: kube-state-metrics
    targetRevision: 5.*
    # helm:
      # values: |
        # Values
        # nodeSelector:
        #  location: contabo
  syncPolicy:
    syncOptions:
    - CreateNamespace=true
