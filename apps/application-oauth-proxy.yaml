apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: oauth-proxy
  namespace: argocd
spec:
  destination:
    server: https://kubernetes.default.svc
    namespace: oauth-proxy
  project: default
  sources:
  - repoURL: 'https://oauth2-proxy.github.io/manifests'
    chart: oauth2-proxy
    targetRevision: 7.*
    helm:
      valueFiles:
      - $values/oauth-proxy/values.yaml
  - repoURL: '**************:pajikos/home-iaac.git'
    targetRevision: HEAD
    ref: values
  syncPolicy:
    syncOptions:
    - CreateNamespace=true
