apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: dashy-addon-codeserver
  labels:
    app.kubernetes.io/instance: dashy
    app.kubernetes.io/name: dashy
  annotations:
    cert-manager.io/cluster-issuer: letsencrypt-cloudflare-sklenarovi
    nginx.ingress.kubernetes.io/auth-signin: https://oauth.k8s.sklenarovi.cz/oauth2/start?rd=$pass_access_scheme://$http_host$escaped_request_uri
    nginx.ingress.kubernetes.io/auth-url: https://oauth.k8s.sklenarovi.cz/oauth2/auth
spec:
  tls:
    - hosts:
        - "dashy-code.k8s.sklenarovi.cz"
      secretName: "dashy-code-k8s-sklenarovi-cz-tls"
  rules:
    - host: "dashy-code.k8s.sklenarovi.cz"
      http:
        paths:
          - path: "/"
            pathType: Prefix
            backend:
              service:
                name: dashy-addon-codeserver
                port:
                  number: 12321
