apiVersion: apps/v1
kind: Deployment
metadata:
  name: dashy
  labels:
    app.kubernetes.io/instance: dashy
    app.kubernetes.io/name: dashy
spec:
  replicas: 1
  selector:
    matchLabels:
      app.kubernetes.io/name: dashy
      app.kubernetes.io/instance: dashy
  template:
    metadata:
      labels:
        app.kubernetes.io/name: dashy
        app.kubernetes.io/instance: dashy
    spec:
      serviceAccountName: default
      containers:
        - name: dashy
          image: lissy93/dashy:3.x
          env:
            - name: TZ
              value: Europe/Prague
          ports:
            - name: http
              containerPort: 8080
              protocol: TCP
          volumeMounts:
            - mountPath: /app/user-data/conf.yml
              name: config
              subPath: conf.yml
          # ... (keep the probes as they were)
        - name: codeserver
          image: codercom/code-server:4.91.1
          args:
            - --auth
            - none
            - --user-data-dir
            - /config/.vscode
            - --extensions-dir
            - /config/.vscode
            - --port
            - "12321"
            - /config
          ports:
            - containerPort: 12321
              name: codeserver
              protocol: TCP
          securityContext:
            runAsUser: 0
          volumeMounts:
            - mountPath: /config/conf.yml
              name: config
              subPath: conf.yml
      volumes:
        - name: config
          configMap:
            name: dashy-config
      nodeSelector:
        location: home
