apiVersion: v1
kind: Service
metadata:
  name: dashy-addon-codeserver
  labels:
    app.kubernetes.io/service: dashy-addon-codeserver
    app.kubernetes.io/instance: dashy
    app.kubernetes.io/name: dashy
spec:
  type: ClusterIP
  ports:
    - port: 12321
      targetPort: 12321
      protocol: TCP
      name: codeserver
  selector:
    app.kubernetes.io/instance: dashy
    app.kubernetes.io/name: dashy
