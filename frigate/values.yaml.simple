

image:
  # -- Docker registry/repository to pull the image from
  repository: ghcr.io/blakeblackshear/frigate
  # -- Overrides the default tag (appVersion) used in Chart.yaml ([Docker Hub](https://hub.docker.com/r/blakeblackshear/frigate/tags?page=1))
  tag: 0.12.0


# -- additional ENV variables to set. Prefix with FRIGATE_ to target Frigate configuration values
env: 
  TZ: Europe/Prague


coral:
  # -- enables the use of a Coral device
  enabled: true
  # -- path on the host to which to mount the Coral device
  hostPath: /dev/bus/usb

# -- frigate configuration - see [Docs](https://docs.frigate.video/configuration/index) for more info
config: |
  mqtt:
    # Required: host name
    host: mosquitto.mosquitto.svc
    # Optional: port (default: shown below)
    port: 1883
    # Optional: topic prefix (default: shown below)
    # WARNING: must be unique if you are running multiple instances
    topic_prefix: frigate
    # Optional: client id (default: shown below)
    # WARNING: must be unique if you are running multiple instances
    client_id: frigate
    # Optional: interval in seconds for publishing stats (default: shown below)
    stats_interval: 60

  motion:
    improve_contrast: True
    contour_area: 15

  detectors:
    coral:
      type: edgetpu
      device: usb

  ffmpeg:
    # global_args: ["-hide_banner", "-loglevel", "warning"]
    hwaccel_args: preset-vaapi
    # hwaccel_args: ["-hwaccel", "vaapi", "-hwaccel_device", "/dev/dri/renderD128", "-hwaccel_output_format", "yuv420p"]
    # output_args:
    #   record: preset-record-generic-audio-aac

  objects:
    track:
      - person
    filters:
      person:
        threshold: 0.70

  record:
    enabled: True
    events:
      retain:
        default: 10

  snapshots:
    enabled: true
    # timestamp: false
    bounding_box: true
    crop: false
    retain:
      default: 7

  # go2rtc:
  #   streams:
  #     # ulice_detect:
  #     #   - rtsp://view:333777999a@*************:554/Streaming/Channels/102/
  #     ulice_record:
  #       - rtsp://view:333777999a@*************:554/Streaming/Channels/101/
  #     # zahrada_detect:
  #     #   - rtsp://view:333777999a@172.16.100.70:554/Streaming/Channels/102/
  #     zahrada_record:
  #       - rtsp://view:hes123Lo@172.16.100.70:554/Streaming/Channels/101/
  #   webrtc:
  #     candidates:
  #       - 10.42.1.114:8555
  #       - 172.16.100.12:8555
  #       - 172.16.88.12:8555
  #       - stun:8555

  cameras:
    ulice:
      motion:
        mask:
        - 389,108,645,95,629,52,609,0,358,0,0,0,0,328
      objects:
        filters:
          person:
            mask:
              - 363,100,0,234,0,374,0,1080,1225,1080,1762,0,817,0
      ffmpeg:
        inputs:
          # - path: rtsp://127.0.0.1:8554/ulice_detect?video=copy&audio=copy
          #- path: rtsp://view:333777999a@*************:554/Streaming/Channels/102/
          #   # input_args: preset-rtsp-restream
          #   roles:
          #     - detect
          # - path: rtsp://127.0.0.1:8554/ulice_record
          - path: rtsp://view:333777999a@*************:554/Streaming/Channels/101/
            # input_args: preset-rtsp-restream
            roles:
              - record
              - detect
        output_args:
          record: preset-record-generic
      # live:
      #   stream_name: ulice_record
      detect:
        width: 1920
        # Optional: height of the frame for the input with the detect role (default: shown below)
        height: 1080
        fps: 5
    zahrada:
      motion:
        mask:
          - 280,0,0,0,0,1080,183,1080
          - 1920,0,1920,375,1180,242,0,163,0,0
      objects:
        filters:
          person:
            mask:
              - 1048,198,1920,354,1920,1080,289,1080,255,202
      ffmpeg:
        inputs:
          # - path: rtsp://127.0.0.1:8554/ulice_detect?video=copy&audio=copy
          #- path: rtsp://view:333777999a@*************:554/Streaming/Channels/102/
          #   # input_args: preset-rtsp-restream
          #   roles:
          #     - detect
          # - path: rtsp://127.0.0.1:8554/zahrada_record
          - path: rtsp://view:hes123Lo@172.16.100.70:554/Streaming/Channels/101/
            # input_args: preset-rtsp-restream
            roles:
              - record
              - detect
        output_args:
          record: preset-record-generic
      # live:
      #   stream_name: zahrada_record
      detect:
        width: 1920
        # Optional: height of the frame for the input with the detect role (default: shown below)
        height: 1080
        fps: 5



ingress:
  # -- Enables the use of an Ingress Controller to front the Service and can provide HTTPS
  enabled: true

  # -- annotations to configure your Ingress. See your Ingress Controller's Docs for more info.
  annotations: {}
    # kubernetes.io/ingress.class: nginx
    # kubernetes.io/tls-acme: "true"
    # For NGINX INC, these may be neccessary
    # nginx.org/proxy-read-timeout: "3600"
    # nginx.org/proxy-send-timeout: "3600"
    # nginx.org/websocket-services: "<release_name>-frigate" # TODO: can this be automated?

  # -- list of hosts and their paths that ingress controller should repsond to.
  hosts:
    - host: frigate.k8s.sklenarovi.cz
      paths:
        - '/'

  # -- list of TLS configurations
  tls:
   - secretName: frigate-k8s-sklenarovi-cz-tls
     hosts:
       - frigate.k8s.sklenarovi.cz

persistence:
  data:
    # -- Enables persistence for the data directory
    enabled: true


    # -- size/capacity of the PVC
    size: 100Gi

    # -- Do not delete the pvc upon helm uninstall
    skipuninstall: false


# -- Set Security Context
securityContext: 
  # capabilities:
  #   drop:
  #   - ALL
  # readOnlyRootFilesystem: true
  # runAsNonRoot: true
  # runAsUser: 1000
  privileged: true

# -- Node Selector configuration
nodeSelector:
  location: home

