image:
  # -- Docker registry/repository to pull the image from
  repository: ghcr.io/blakeblackshear/frigate
  # -- Overrides the default tag (appVersion) used in Chart.yaml ([Docker Hub](https://hub.docker.com/r/blakeblackshear/frigate/tags?page=1))
  tag: 0.16.0

# -- additional ENV variables to set. Prefix with FRIGATE_ to target Frigate configuration values
env:
  TZ: Europe/Prague

coral:
  # -- enables the use of a Coral device
  enabled: true
  # -- path on the host to which to mount the Coral device
  hostPath: /dev/bus/usb

# -- frigate configuration - see [Docs](https://docs.frigate.video/configuration/index) for more info
config: |

  # TLS disable
  tls:
    enabled: false
  auth:
    enabled: false

  # database:
  #   path: /media/frigate.db
  mqtt:
    # Required: host name
    host: *************
    # Optional: port (default: shown below)
    port: 1883
    # Optional: topic prefix (default: shown below)
    # WARNING: must be unique if you are running multiple instances
    topic_prefix: frigate
    # Optional: client id (default: shown below)
    # WARNING: must be unique if you are running multiple instances
    client_id: frigate
    # Optional: interval in seconds for publishing stats (default: shown below)
    stats_interval: 30

  # motion:
  #   improve_contrast: True
  #   contour_area: 15

  detectors:
    coral:
      type: edgetpu
      device: usb

  # ffmpeg:
  #   # global_args: ["-hide_banner", "-loglevel", "warning"]
  #   hwaccel_args: preset-vaapi
    # hwaccel_args: ["-hwaccel", "vaapi", "-hwaccel_device", "/dev/dri/renderD128", "-hwaccel_output_format", "yuv420p"]
    # output_args:
    #   record: preset-record-generic-audio-aac

  objects:
    track:
      - person
    filters:
      person:
        threshold: 0.70

  record:
    enabled: true
    alerts:
      retain:
        days: 10
    detections:
      retain:
        days: 10
  snapshots:
    enabled: true
    # timestamp: false
    bounding_box: true
    crop: false
    retain:
      default: 7

  go2rtc:
    streams:
      # ulice_detect:
      #   - rtsp://view:333777999a@*************:554/Streaming/Channels/102/
      ulice_record:
        - rtsp://view:333777999a@*************:554/Streaming/Channels/101/
      # zahrada_detect:
      #   - rtsp://view:333777999a@172.16.100.70:554/Streaming/Channels/102/
      zahrada_record:
        - rtsp://view:hes123Lo@172.16.100.70:554/Streaming/Channels/101/
      zahrada_reo_record:
        - rtsp://admin:pajikReolink7@172.16.100.160:554/Preview_01_main
    webrtc:
      candidates:
        - 10.42.1.114:8555
        - 172.16.100.12:8555
        - 172.16.88.12:8555
        - stun:8555

  cameras:
    ulice:
      motion:
        mask:
          - 389,108,645,95,629,52,609,0,358,0,0,0,0,328
      objects:
        filters:
          person:
            mask:
              - 389,108,645,95,629,52,609,0,358,0,0,0,0,328
      ffmpeg:
        inputs:
          # - path: rtsp://127.0.0.1:8554/ulice_detect?video=copy&audio=copy
          # # - path: rtsp://view:333777999a@*************:554/Streaming/Channels/102/
          #   # input_args: preset-rtsp-restream
          #   roles:
          #     - detect
          - path: rtsp://127.0.0.1:8554/ulice_record
          # - path: rtsp://view:333777999a@*************:554/Streaming/Channels/101/
            # input_args: preset-rtsp-restream
            roles:
              - record
              - detect
        output_args:
          record: preset-record-generic
      live:
        stream_name: ulice_record
      detect:
        width: 1920
        # Optional: height of the frame for the input with the detect role (default: shown below)
        height: 1080
        fps: 5
    zahrada:
      motion:
        mask:
          - 280,0,0,0,0,1080,183,1080
          - 1920,0,1920,343,1142,203,0,163,0,0
      objects:
        filters:
          person:
            mask:
              - 280,0,0,0,0,1080,183,1080
              - 1920,0,1920,343,1142,203,0,163,0,0
      ffmpeg:
        inputs:
          # - path: rtsp://127.0.0.1:8554/ulice_detect?video=copy&audio=copy
          # # - path: rtsp://view:333777999a@*************:554/Streaming/Channels/102/
          #   # input_args: preset-rtsp-restream
          #   roles:
          #     - detect
          - path: rtsp://127.0.0.1:8554/zahrada_record
          # - path: rtsp://view:333777999a@*************:554/Streaming/Channels/101/
            # input_args: preset-rtsp-restream
            roles:
              - record
              - detect
        output_args:
          record: preset-record-generic
      live:
        stream_name: zahrada_record
      detect:
        width: 1920
        # Optional: height of the frame for the input with the detect role (default: shown below)
        height: 1080
        fps: 5
    zahrada-bouda:
      motion:
        mask:
          - 0.385,0.143,0.37,0.256,0.376,0.279,0.391,0.283,0.416,0.283,0.453,0.267,0.461,0.256,0.461,0.197,0.448,0.135,0.392,0.116,0.378,0.114
          - 0.334,-0.002,0.334,0.02,0.335,0.065,0.381,0.081,0.48,0.099,0.592,0.116,0.598,0.049,0.629,0.05,0.632,0.024,0.742,0.046,0.745,0.189,0.759,0.23,0.999,0.296,0.998,0
      objects:
        filters:
          person: {}
      ffmpeg:
        inputs:
          - path: rtsp://127.0.0.1:8554/zahrada_reo_record
            roles:
              - record
              - detect
        output_args:
          record: preset-record-generic
      live:
        stream_name: zahrada_reo_record
      detect:
        width: 3840
        # Optional: height of the frame for the input with the detect role (default: shown below)
        height: 2160
        fps: 5

ingress:
  # -- Enables the use of an Ingress Controller to front the Service and can provide HTTPS
  enabled: true

  # -- annotations to configure your Ingress. See your Ingress Controller's Docs for more info.
  annotations:
    cert-manager.io/cluster-issuer: letsencrypt-cloudflare-sklenarovi
    # kubernetes.io/ingress.class: nginx
    # kubernetes.io/tls-acme: "true"
    # For NGINX INC, these may be neccessary
    # nginx.org/proxy-read-timeout: "3600"
    # nginx.org/proxy-send-timeout: "3600"
    # nginx.org/websocket-services: "<release_name>-frigate" # TODO: can this be automated?

  # -- list of hosts and their paths that ingress controller should repsond to.
  hosts:
    - host: frigate.k8s.sklenarovi.cz
      paths:
        - path: '/'
          portName: http-auth

  # -- list of TLS configurations
  tls:
    - secretName: frigate-k8s-sklenarovi-cz-tls
      hosts:
        - frigate.k8s.sklenarovi.cz

persistence:
  data:
    # -- Enables persistence for the data directory
    enabled: false

    # -- size/capacity of the PVC
    size: 100Gi

    # -- Do not delete the pvc upon helm uninstall
    skipuninstall: false
  config:
    # -- Enables persistence for the config directory
    enabled: true
  media:
    # -- Enables persistence for the media directory
    enabled: true
    size: 100Gi

# -- Set Security Context
securityContext:
  # capabilities:
  #   drop:
  #   - ALL
  # readOnlyRootFilesystem: true
  # runAsNonRoot: true
  # runAsUser: 1000
  privileged: true

# -- Set resource limits/requests for the Pod(s)
resources:
  limits:
    cpu: 2000m
    memory: 12000Mi
  requests:
    cpu: 500m
    memory: 1024Mi

# -- Node Selector configuration
nodeSelector:
  location: home
  kubernetes.io/hostname: home03
# -- declare extra volumes to use for Frigate
# extraVolumes:
#   - name: config-cache
#     emptyDir: {}
#   - name: config-timeline
#     emptyDir: {}
# # -- declare additional volume mounts
# extraVolumeMounts:
#   - name: config-cache
#     mountPath: /config/model_cache
#     readOnly: false
#   - name: config-timeline
#     mountPath: /config/.timeline
#     readOnly: false
