# Default values for mosquitto.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.

deployType: deployment

mqttOverWebsocket: true

general:
  maxInflightMessages: 20
  maxQueuedMessages: 1000

auth:
  enabled: false
#   users:
#     - username: admin
#       # "admin" password in sha512-pbkdf2 format, generated with mosquitto_passwd
#       password: $7$101$T1RBXD5MGIImHq8g$hSCHVAyZtAif0qN9Fhuam9mVCd0xLomREHIwzdreGjjADCQewz9VpfhK6AumcZyVFHFpHd2EhZdqU+Lq7393Xw==
#       acl:
#         - topic: "#"
#           access: readwrite

service:
  type: LoadBalancer
  loadBalancerIP: *************
  externalTrafficPolicy: Local
  mqttPort: 1883
  mqttOverWebsocketPort: 9001

ingress:
  enabled: true
  annotations:
    cert-manager.io/cluster-issuer: letsencrypt-cloudflare-sklenarovi
  hosts:
    - host: wssmqtt.k8s.sklenarovi.cz
      paths:
        - path: /
          pathType: Prefix
  tls:
    - secretName: wssmqtt-k8s-sklenarovi-cz-tls
      hosts:
        - "wssmqtt.k8s.sklenarovi.cz"

nodeSelector:
  location: home
  # kubernetes.io/hostname: home04
# tolerations:
#   - key: "preparing"
#     operator: "Equal"
#     value: "yes"
#     effect: "NoSchedule"
