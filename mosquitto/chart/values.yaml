# Default values for mosquitto.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.

# daemonset, deployment
deployType: deployment

image:
  repository: eclipse-mosquitto
  pullPolicy: IfNotPresent
  # Overrides the image tag whose default is the chart appVersion.
  tag: ""

mqttOverWebsocket: true

general:
  maxInflightMessages: 20
  maxQueuedMessages: 1000

auth:
  enabled: true
  users:
    - username: admin
      # "admin" password in sha512-pbkdf2 format, generated with mosquitto_passwd
      password: $7$101$T1RBXD5MGIImHq8g$hSCHVAyZtAif0qN9Fhuam9mVCd0xLomREHIwzdreGjjADCQewz9VpfhK6AumcZyVFHFpHd2EhZdqU+Lq7393Xw==
      acl:
        - topic: "#"
          access: readwrite

imagePullSecrets: []
nameOverride: ""
fullnameOverride: ""

serviceAccount:
  # Specifies whether a service account should be created
  create: true
  # Annotations to add to the service account
  annotations: {}
  # The name of the service account to use.
  # If not set and create is true, a name is generated using the fullname template
  name: ""

podAnnotations: {}

podSecurityContext:
  {}
  # fsGroup: 1883

securityContext:
  {}
  # capabilities:
  #   drop:
  #     - ALL
  #   add:
  #     - NET_BIND_SERVICE
  # readOnlyRootFilesystem: true
  # runAsNonRoot: true
  # runAsUser: 1883

service:
  type: ClusterIP
  mqttPort: 1883
  mqttOverWebsocketPort: 9001
  externalTrafficPolicy: Cluster

ingress:
  enabled: false
  className: ""
  annotations:
    {}
    # kubernetes.io/ingress.class: nginx
    # kubernetes.io/tls-acme: "true"
  hosts:
    - host: chart-example.local
      paths:
        - path: /
          pathType: ImplementationSpecific
  tls: []
  #  - secretName: chart-example-tls
  #    hosts:
  #      - chart-example.local

resources:
  {}
  # We usually recommend not to specify default resources and to leave this as a conscious
  # choice for the user. This also increases chances charts run on environments with little
  # resources, such as Minikube. If you do want to specify resources, uncomment the following
  # lines, adjust them as necessary, and remove the curly braces after 'resources:'.
  # limits:
  #   cpu: 100m
  #   memory: 128Mi
  # requests:
  #   cpu: 100m
  #   memory: 128Mi

nodeSelector: {}

tolerations: []

affinity: {}
