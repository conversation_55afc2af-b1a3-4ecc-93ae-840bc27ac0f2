{{- if eq .Values.deployType "daemonset" }}
apiVersion: apps/v1
kind: DaemonSet
metadata:
  name: {{ include "mosquitto.fullname" . }}
  labels:
{{ include "mosquitto.labels" . | indent 4 }}
spec:
  selector:
    matchLabels:
{{ include "mosquitto.selectorLabels" . | indent 6 }}
  template:
    metadata:
      {{- with .Values.podAnnotations }}
      annotations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      labels:
{{ include "mosquitto.selectorLabels" . | indent 8 }}
    spec:
      {{- with .Values.imagePullSecrets }}
      imagePullSecrets:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      serviceAccountName: {{ include "mosquitto.serviceAccountName" . }}
      securityContext:
        {{- toYaml .Values.podSecurityContext | nindent 8 }}
      containers:
        - name: {{ .Chart.Name }}
          securityContext:
            {{- toYaml .Values.securityContext | nindent 12 }}
          image: "{{ .Values.image.repository }}:{{ .Values.image.tag | default .Chart.AppVersion }}"
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          ports:
            - name: mqtt
              containerPort: 1883
              hostPort: 1883
              protocol: TCP
            {{- if .Values.mqttOverWebsocket }}
            - name: mqtt-ws
              containerPort: 9001
              protocol: TCP
            {{- end }}
          volumeMounts:
            - name: config
              mountPath: /mosquitto/config/mosquitto.conf
              subPath: mosquitto.conf
              readOnly: true
            {{- if .Values.auth.enabled }}
            - name: secrets
              mountPath: /mosquitto/config/passwords.conf
              subPath: passwords.conf
              readOnly: true
            - name: config
              mountPath: /mosquitto/config/acl.conf
              subPath: acl.conf
              readOnly: true
            {{- end }}
          livenessProbe:
            tcpSocket:
              port: 1883
          readinessProbe:
            tcpSocket:
              port: 1883
          resources:
            {{- toYaml .Values.resources | nindent 12 }}
      volumes:
        - name: config
          configMap:
            name: {{ include "mosquitto.fullname" . }}
        {{- if .Values.auth.enabled }}
        - name: secrets
          secret:
            secretName: {{ include "mosquitto.fullname" . }}
        {{- end }}
      {{- with .Values.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
      {{- end }}
      {{- with .Values.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
      {{- end }}
{{- end }}
