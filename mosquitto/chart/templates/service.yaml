apiVersion: v1
kind: Service
metadata:
  name: {{ include "mosquitto.fullname" . }}
  labels:
{{ include "mosquitto.labels" . | indent 4 }}
spec:
  type: {{ .Values.service.type }}
  {{- if .Values.service.loadBalancerIP }}
  loadBalancerIP: {{ .Values.service.loadBalancerIP }}
  {{- end }}
  {{- if .Values.service.externalTrafficPolicy }}
  externalTrafficPolicy: {{ .Values.service.externalTrafficPolicy }}
  {{- end }}
  {{- if .Values.service.localTrafficPolicy }}
  localTrafficPolicy: {{ .Values.service.localTrafficPolicy }}
  {{- end }}
  ports:
    - port: {{ .Values.service.mqttPort }}
      targetPort: mqtt
      protocol: TCP
      name: mqtt
    {{- if .Values.mqttOverWebsocket }}
    - port: {{ .Values.service.mqttOverWebsocketPort }}
      targetPort: mqtt-ws
      protocol: TCP
      name: mqtt-ws
    {{- end }}
  selector:
{{ include "mosquitto.selectorLabels" . | indent 4 }}
