loki:
  enabled: true

  ingress:
    enabled: true
    annotations:
      cert-manager.io/cluster-issuer: letsencrypt-cloudflare-sklenarovi
      traefik.ingress.kubernetes.io/router.tls: "true"
      traefik.ingress.kubernetes.io/router.middlewares: traefik-loki-basicauth@kubernetescrd
    hosts:
      - host: loki.k8s.sklenarovi.cz
        paths: [/]
    tls:
      - secretName: loki-k8s-sklenarovi-cz-tls
        hosts:
          - "loki.k8s.sklenarovi.cz"

  config:
    # existingSecret:
    auth_enabled: false
    table_manager:
      retention_deletes_enabled: true
      retention_period: 2160h

  # # ref: https://kubernetes.io/docs/concepts/configuration/assign-pod-node/
  nodeSelector:
    kubernetes.io/hostname: home05

  # # ref: https://kubernetes.io/docs/concepts/storage/persistent-volumes/
  # # If you set enabled as "True", you need :
  # # - create a pv which above 10Gi and has same namespace with loki
  # # - keep storageClassName same with below setting
  persistence:
    enabled: true
    accessModes:
      - ReadWriteOnce
    size: 50Gi

promtail:
  enabled: true
  config:
    logLevel: info
    snippets:
      extraScrapeConfigs: |
        - job_name: syslog
          syslog:
            listen_address: 0.0.0.0:1514
            label_structured_data: yes
            idle_timeout: 60s
            labels:
              job: "syslog"
          relabel_configs:
            - source_labels: ["__syslog_connection_ip_address"]
              target_label: "ip_address"
            - source_labels: ["__syslog_message_severity"]
              target_label: "severity"
            - source_labels: ["__syslog_message_facility"]
              target_label: "facility"
            - source_labels: ["__syslog_message_hostname"]
              target_label: "host"
  extraPorts:
    syslog:
      name: tcp-syslog
      #  annotations: {}
      #  labels: {}
      containerPort: 1514
      protocol: TCP
      service:
        type: LoadBalancer
        #  clusterIP: null
        port: 1514
        #  externalIPs: []
        #  nodePort: null
        loadBalancerIP: *************
    #      loadBalancerSourceRanges: []
    #      externalTrafficPolicy: null

fluent-bit:
  enabled: false

grafana:
  enabled: false

prometheus:
  enabled: false

filebeat:
  enabled: false

logstash:
  enabled: false
