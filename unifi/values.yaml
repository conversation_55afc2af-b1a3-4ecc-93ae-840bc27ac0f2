# Upgrade strategy type
strategyType: Recreate

# Timezone setting
timezone: Europe/Prague

# Disable privileged ports binding
bindPrivilegedPorts: false

# Node selector to deploy to home05
nodeSelector:
  kubernetes.io/hostname: home05

# Enable persistence
persistence:
  enabled: true
  storageClass: ""
  accessMode: ReadWriteOnce
  size: 5Gi
  skipuninstall: true
  existingClaim: "unifi-lh"

# Configure ingress
ingress:
  enabled: true
  ingressClassName: ""
  annotations:
    cert-manager.io/cluster-issuer: letsencrypt-cloudflare-sklenarovi
    nginx.ingress.kubernetes.io/backend-protocol: "HTTPS"
  path: /
  hosts:
    - unifi.k8s.sklenarovi.cz
  tls:
    - secretName: unifi-k8s-sklenarovi-cz-tls
      hosts:
        - unifi.k8s.sklenarovi.cz

# Configure services
guiService:
  enabled: true
  type: LoadBalancer
  loadBalancerIP: "*************"
  annotations:
    metallb.io/ip-allocated-from-pool: default-pool
    metallb.io/allow-shared-ip: unifi-svc


captivePortalService:
  enabled: true
  type: LoadBalancer
  loadBalancerIP: "*************"
  annotations:
    metallb.io/ip-allocated-from-pool: default-pool
    metallb.io/allow-shared-ip: unifi-svc

controllerService:
  enabled: true
  type: LoadBalancer
  loadBalancerIP: "*************"
  annotations:
    metallb.io/ip-allocated-from-pool: default-pool
    metallb.io/allow-shared-ip: unifi-svc

stunService:
  enabled: true
  type: LoadBalancer
  loadBalancerIP: "*************"
  annotations:
    metallb.io/ip-allocated-from-pool: default-pool
    metallb.io/allow-shared-ip: unifi-svc

discoveryService:
  enabled: true
  type: LoadBalancer
  loadBalancerIP: "*************"
  annotations:
    metallb.io/ip-allocated-from-pool: default-pool
    metallb.io/allow-shared-ip: unifi-svc

syslogService:
  enabled: true
  type: LoadBalancer
  loadBalancerIP: "*************"
  annotations:
    metallb.io/ip-allocated-from-pool: default-pool
    metallb.io/allow-shared-ip: unifi-svc

speedtestService:
  enabled: true
  type: LoadBalancer
  loadBalancerIP: "*************"
  annotations:
    metallb.io/ip-allocated-from-pool: default-pool
    metallb.io/allow-shared-ip: unifi-svc
