# Default values for victoria-metrics-agent.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.

replicaCount: 1

nodeSelector:
  location: home

rbac:
  create: true
  pspEnabled: false

remoteWrite:
  - url: http://victoria-metrics-victoria-metrics-single-server.victoria.svc.cluster.local:8428/api/v1/write

extraArgs:
  envflag.enable: "true"
  envflag.prefix: VM_
  loggerFormat: json
  influxListenAddr: ":8189"
  remoteWrite.vmProtoCompressLevel: "15"

service:
  enabled: true
  servicePort: 8429
  type: ClusterIP

ingress:
  enabled: true
  annotations:
    cert-manager.io/cluster-issuer: letsencrypt-cloudflare-sklenarovi
    nginx.ingress.kubernetes.io/auth-url: "https://oauth.k8s.sklenarovi.cz/oauth2/auth"
    nginx.ingress.kubernetes.io/auth-signin: "https://oauth.k8s.sklenarovi.cz/oauth2/start?rd=$pass_access_scheme://$http_host$escaped_request_uri"
  hosts:
    - name: vmagent.k8s.sklenarovi.cz
      path: /
      port: http
  tls:
    - secretName: vmagent-k8s-sklenarovi-cz-tls
      hosts:
        - "vmagent.k8s.sklenarovi.cz"
  pathType: Prefix

config:
  global:
    scrape_interval: 60s

  scrape_configs:
    - job_name: "blackbox-http200"
      scrape_interval: 120s
      metrics_path: /probe
      params:
        module: [http_2xx] # Look for a HTTP 200 response.
      static_configs:
        - targets:
            - http://************:5000/health
      relabel_configs:
        - source_labels: [__address__]
          target_label: __param_target
        - source_labels: [__param_target]
          target_label: instance
        - target_label: __address__
          replacement: blackbox-exporter-prometheus-blackbox-exporter.victoria.svc:9115
    - job_name: "blackbox-https200"
      scrape_interval: 120s
      metrics_path: /probe
      params:
        module: [https_2xx] # Look for a HTTP 200 response.
      static_configs:
        - targets:
            - https://hass.k8s.sklenarovi.cz
            - https://vmagent.k8s.sklenarovi.cz
            - https://vmalert.k8s.sklenarovi.cz
            - https://grafana.k8s.sklenarovi.cz
            - https://frigate.k8s.sklenarovi.cz
            - https://argocd.k8s.sklenarovi.cz
            - https://oauth.k8s.sklenarovi.cz
            - https://registry.k8s.sklenarovi.cz
            - https://registryui.k8s.sklenarovi.cz
            - https://zigbee2mqtt.k8s.sklenarovi.cz
            - https://nas.sklenarovi.cz:5001
            - https://n8n.k8s.sklenarovi.cz
      relabel_configs:
        - source_labels: [__address__]
          target_label: __param_target
        - source_labels: [__param_target]
          target_label: instance
        - target_label: __address__
          replacement: blackbox-exporter-prometheus-blackbox-exporter.victoria.svc:9115 # The blackbox exporter's real hostname:port.

    - job_name: "blackbox-https40x"
      scrape_interval: 120s
      metrics_path: /probe
      params:
        module: [https_40x] # Look for a HTTP 40x response.
      static_configs:
        - targets:
            - https://oauth.k8s.sklenarovi.cz
      relabel_configs:
        - source_labels: [__address__]
          target_label: __param_target
        - source_labels: [__param_target]
          target_label: instance
        - target_label: __address__
          replacement: blackbox-exporter-prometheus-blackbox-exporter.victoria.svc:9115 # The blackbox exporter's real hostname:port.

    # - job_name: "blackbox-http200"
    #   scrape_interval: 120s
    #   metrics_path: /probe
    #   params:
    #     module: [http_2xx] # Look for a HTTP 200 response.
    #   static_configs:
    #     - targets:
    #         - http://*************:8083
    #   relabel_configs:
    #     - source_labels: [__address__]
    #       target_label: __param_target
    #     - source_labels: [__param_target]
    #       target_label: instance
    #     - target_label: __address__
    #       replacement: blackbox-exporter-prometheus-blackbox-exporter.victoria.svc:9115 # The blackbox exporter's real hostname:port.

    - job_name: "blackbox-tcpconnect"
      scrape_interval: 120s
      metrics_path: /probe
      params:
        module: [tcp_connect] # Look for a HTTP 200 response.
      static_configs:
        - targets:
            - **************:5001
            - mosquitto.mosquitto.svc:1883
            - *************:554
            - *************:554
      relabel_configs:
        - source_labels: [__address__]
          target_label: __param_target
        - source_labels: [__param_target]
          target_label: instance
        - target_label: __address__
          replacement: blackbox-exporter-prometheus-blackbox-exporter.victoria.svc:9115 # The blackbox exporter's real hostname:port.

    - job_name: "blackbox-ping"
      scrape_interval: 120s
      metrics_path: /probe
      params:
        module: [ping] # Look for a HTTP 200 response.
      static_configs:
        - targets:
            - google.com
            - **************
            - ************
            - ************96
      relabel_configs:
        - source_labels: [__address__]
          target_label: __param_target
        - source_labels: [__param_target]
          target_label: instance
        - target_label: __address__
          replacement: blackbox-exporter-prometheus-blackbox-exporter.victoria.svc:9115 # The blackbox exporter's real hostname:port.

    - job_name: Mikrotik
      static_configs:
        - targets:
            - ************ # SNMP device IP.
      metrics_path: /snmp
      params:
        module: [mikrotik]
      relabel_configs:
        - source_labels: [__address__]
          target_label: __param_target
        - source_labels: [__param_target]
          target_label: instance
        - target_label: __address__
          replacement: snmp-exporter-mikrotik-prometheus-snmp-exporter.snmp-exporter.svc:9116 # The SNMP exporter's real hostname:port.

    - job_name: "Synology"
      static_configs:
        - targets: ["**************"]
      metrics_path: /snmp
      params:
        module: [synology]
      relabel_configs:
        - source_labels: [__address__]
          target_label: __param_target
        - source_labels: [__param_target]
          target_label: instance
        - target_label: __address__
          replacement: snmp-exporter-synology-prometheus-snmp-exporter.snmp-exporter.svc:9116 # The SNMP exporter's real hostname:port.

    - job_name: "hass"
      scrape_interval: 60s
      metrics_path: /api/prometheus

      # Long-Lived Access Token
      bearer_token: "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJpc3MiOiI3MTMwYzMwZjc1N2Y0NmI1ODBmMzE5OTgxODBmOThhYSIsImlhdCI6MTY0NzQzNDk4NiwiZXhwIjoxOTYyNzk0OTg2fQ.ggACsRUBgf9mQIYpcPoYzmV3rx6s8YGn8AqFeUu5NCM"

      scheme: http
      static_configs:
        - targets: ["http://************:8123"]

    - job_name: vmagent
      static_configs:
        - targets: ["localhost:8429"]

    - job_name: victoriametrics
      static_configs:
        - targets: ["victoria-metrics-victoria-metrics-single-server:8428"]

    - job_name: node_exporter
      static_configs:
        - targets:
            - "oraclevm1.nb.sklenarovi.cz:9100"
            - "oraclevm2.nb.sklenarovi.cz:9100"
            - "azure1.nb.sklenarovi.cz:9100"
            - "ampere1.nb.sklenarovi.cz:9100"
            - "contabo2.nb.sklenarovi.cz:9100"
            - "home02.nb.sklenarovi.cz:9100"
            - "home03.nb.sklenarovi.cz:9100"
            - "home04.nb.sklenarovi.cz:9100"
            - "home05.nb.sklenarovi.cz:9100"
    - job_name: "kubernetes-nodes"

      # Default to scraping over https. If required, just disable this or change to
      # `http`.
      scheme: https

      # This TLS & bearer token file config is used to connect to the actual scrape
      # endpoints for cluster components. This is separate to discovery auth
      # configuration because discovery & scraping are two separate concerns in
      # Prometheus. The discovery auth config is automatic if Prometheus runs inside
      # the cluster. Otherwise, more config options have to be provided within the
      # <kubernetes_sd_config>.
      tls_config:
        ca_file: /var/run/secrets/kubernetes.io/serviceaccount/ca.crt
      bearer_token_file: /var/run/secrets/kubernetes.io/serviceaccount/token

      kubernetes_sd_configs:
        - role: node

      relabel_configs:
        - target_label: __address__
          replacement: kubernetes.default.svc:443
        - source_labels: [__meta_kubernetes_node_name]
          regex: (.+)
          target_label: __metrics_path__
          replacement: /api/v1/nodes/${1}/proxy/metrics

    # Example scrape config for pods
    #
    # The relabeling allows the actual pod scrape endpoint to be configured via the
    # following annotations:
    #
    # * `prometheus.io/scrape`: Only scrape pods that have a value of `true`
    # * `prometheus.io/path`: If the metrics path is not `/metrics` override this.
    # * `prometheus.io/port`: Scrape the pod on the indicated port instead of the
    # pod's declared ports (default is a port-free target if none are declared).
    - job_name: "kubernetes-pods-containers"

      kubernetes_sd_configs:
        - role: pod

      relabel_configs:
        - source_labels: [__meta_kubernetes_pod_annotation_prometheus_io_scrape]
          action: keep
          regex: true
        - source_labels: [__meta_kubernetes_pod_annotation_prometheus_io_path]
          action: replace
          target_label: __metrics_path__
          regex: (.+)
        - source_labels:
            [__address__, __meta_kubernetes_pod_annotation_prometheus_io_port]
          action: replace
          regex: ([^:]+)(?::\d+)?;(\d+)
          replacement: $1:$2
          target_label: __address__

    # Scrape config for service endpoints.
    #
    # The relabeling allows the actual service scrape endpoint to be configured
    # via the following annotations:
    #
    # * `prometheus.io/scrape`: Only scrape services that have a value of `true`
    # * `prometheus.io/scheme`: If the metrics endpoint is secured then you will need
    # to set this to `https` & most likely set the `tls_config` of the scrape config.
    # * `prometheus.io/path`: If the metrics path is not `/metrics` override this.
    # * `prometheus.io/port`: If the metrics are exposed on a different port to the
    # service then set this appropriately.
    # - job_name: 'kubernetes-service-endpoints'

    #   kubernetes_sd_configs:
    #   - role: endpoints

    #   relabel_configs:
    #   - source_labels: [__meta_kubernetes_service_annotation_prometheus_io_scrape]
    #     action: keep
    #     regex: true
    #   - source_labels: [__meta_kubernetes_service_annotation_prometheus_io_scheme]
    #     action: replace
    #     target_label: __scheme__
    #     regex: (https?)
    #   - source_labels: [__meta_kubernetes_service_annotation_prometheus_io_path]
    #     action: replace
    #     target_label: __metrics_path__
    #     regex: (.+)
    #   - source_labels: [__address__, __meta_kubernetes_service_annotation_prometheus_io_port]
    #     action: replace
    #     target_label: __address__
    #     regex: ([^:]+)(?::\d+)?;(\d+)
    #     replacement: $1:$2

    # Scrape config for k8s services
    - job_name: "kubernetes-services"

      kubernetes_sd_configs:
        - role: service

      relabel_configs:
        - source_labels:
            [__meta_kubernetes_service_annotation_prometheus_io_scrape]
          action: keep
          regex: true
        - action: labelmap
          regex: __meta_kubernetes_service_label_(.+)
        - source_labels:
            [__meta_kubernetes_service_annotation_prometheus_io_path]
          action: replace
          target_label: __metrics_path__
        - source_labels:
            [
              __address__,
              __meta_kubernetes_service_annotation_prometheus_io_port,
            ]
          action: replace
          target_label: __address__
          regex: (.+)(?::\d+);(\d+)
          replacement: $1:$2

    - job_name: "kubernetes-nodes-cadvisor"
      # Default to scraping over https. If required, just disable this or change to
      # `http`.
      scheme: https
      # This TLS & bearer token file config is used to connect to the actual scrape
      # endpoints for cluster components. This is separate to discovery auth
      # configuration because discovery & scraping are two separate concerns in
      # Prometheus. The discovery auth config is automatic if Prometheus runs inside
      # the cluster. Otherwise, more config options have to be provided within the
      # <kubernetes_sd_config>.
      tls_config:
        ca_file: /var/run/secrets/kubernetes.io/serviceaccount/ca.crt
        # If your node certificates are self-signed or use a different CA to the
        # master CA, then disable certificate verification below. Note that
        # certificate verification is an integral part of a secure infrastructure
        # so this should only be disabled in a controlled environment. You can
        # disable certificate verification by uncommenting the line below.
        #
        insecure_skip_verify: true
      bearer_token_file: /var/run/secrets/kubernetes.io/serviceaccount/token
      kubernetes_sd_configs:
        - role: node
      # This configuration will work only on kubelet 1.7.3+
      # As the scrape endpoints for cAdvisor have changed
      # if you are using older version you need to change the replacement to
      # replacement: /api/v1/nodes/$1:4194/proxy/metrics
      # more info here https://github.com/coreos/prometheus-operator/issues/633
      relabel_configs:
        - action: labelmap
          regex: __meta_kubernetes_node_label_(.+)
        - target_label: __address__
          replacement: kubernetes.default.svc:443
        - source_labels: [__meta_kubernetes_node_name]
          regex: (.+)
          target_label: __metrics_path__
          replacement: /api/v1/nodes/$1/proxy/metrics/cadvisor
