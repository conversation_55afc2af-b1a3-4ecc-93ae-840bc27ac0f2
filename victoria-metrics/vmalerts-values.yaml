

server:
  enabled: true

  # -- Additional environment variables (ex.: secret tokens, flags) https://github.com/VictoriaMetrics/VictoriaMetrics#environment-variables
  env:
    []
    # - name: VM_remoteWrite_basicAuth_password
    #   valueFrom:
    #     secretKeyRef:
    #       name: auth_secret
    #       key: password


  # vmal<PERSON> reads metrics from source, next section represents its configuration. It can be any service which supports
  # MetricsQL or PromQL.
  datasource:
    url: "http://victoria-metrics-victoria-metrics-single-server.victoria.svc.cluster.local:8428"

  remote:
    write:
      url: "http://victoria-metrics-victoria-metrics-single-server.victoria.svc.cluster.local:8428"
    read:
      url: "http://victoria-metrics-victoria-metrics-single-server.victoria.svc.cluster.local:8428"

  extraArgs:
    envflag.enable: "true"
    envflag.prefix: VM_
    loggerFormat: json
    rule: "/alerts/*.yml"
  extraVolumes:
    - name: alerts
      configMap:
        name: vmalert-rules

    # Extra Volume Mounts for the container
  extraVolumeMounts:
    - name: alerts
      mountPath: /alerts


  ingress:
    enabled: true
    annotations:
      cert-manager.io/cluster-issuer: letsencrypt-cloudflare-sklenarovi
      nginx.ingress.kubernetes.io/auth-url: "https://oauth.k8s.sklenarovi.cz/oauth2/auth"
      nginx.ingress.kubernetes.io/auth-signin: "https://oauth.k8s.sklenarovi.cz/oauth2/start?rd=$pass_access_scheme://$http_host$escaped_request_uri"
    hosts:
      - name: vmalert.k8s.sklenarovi.cz
        path: /
        port: http
    tls:
      - secretName: vmalert-k8s-sklenarovi-cz-tls
        hosts:
          - "vmalert.k8s.sklenarovi.cz"
    # -- pathType is only for k8s >= 1.1=
    pathType: Prefix


  nodeSelector:
    location: oracle


  # vmalert alert rules configuration configuration:
  # use existing configmap if specified
  # otherwise .config values will be used
  configMap: ""
  config:
    alerts:
      groups: []


alertmanager:
  enabled: true
  nodeSelector:
    kubernetes.io/hostname: ampere1
  extraArgs: {}

  persistentVolume:
    # -- Create/use Persistent Volume Claim for alertmanager component. Empty dir if false
    enabled: true
    existingClaim: "vmalert-alertmanager-lh"
  # key: value

  # external URL, that alertmanager will expose to receivers
  baseURL: "https://alertmanager.k8s.sklenarovi.cz"
  # external URL Prefix, Prefix for the internal routes of web endpoints
  baseURLPrefix: ""
  # use existing configmap if specified
  # otherwise .config values will be used
  configMap: ""
  config:
    global:
      resolve_timeout: 5m
    route:
      # default receiver
      receiver: pushover
      # tag to group by
      group_by: ["namespace", "alertname"]
      # How long to initially wait to send a notification for a group of alerts
      group_wait: 30s
      # How long to wait before sending a notification about new alerts that are added to a group
      group_interval: 5m
      # How long to wait before sending a notification again if it has already been sent successfully for an alert
      repeat_interval: 24h
    receivers:
      - name: devnull
      - name: 'pushover'
        pushover_configs:
        - token: avm27yom8fm31f7xfs5wf2gbtzq36c
          user_key: ufgocv28ity5gajsm4sybvrykm97qu
          priority: '-1'
          retry: '120s'
          expire: '60s'
  templates: {}
  #  alertmanager.tmpl: |-
  ingress:
    enabled: true
    annotations:
      cert-manager.io/cluster-issuer: letsencrypt-cloudflare-sklenarovi
      nginx.ingress.kubernetes.io/auth-url: "https://oauth.k8s.sklenarovi.cz/oauth2/auth"
      nginx.ingress.kubernetes.io/auth-signin: "https://oauth.k8s.sklenarovi.cz/oauth2/start?rd=$pass_access_scheme://$http_host$escaped_request_uri"
    hosts:
      - name: alertmanager.k8s.sklenarovi.cz
        path: /
        port: web
    tls:
      - secretName: alertmanager-k8s-sklenarovi-cz-tls
        hosts:
          - "alertmanager.k8s.sklenarovi.cz"

    # For Kubernetes >= 1.18 you should specify the ingress-controller via the field ingressClassName
    # See https://kubernetes.io/blog/2020/04/02/improvements-to-the-ingress-api-in-kubernetes-1.18/#specifying-the-class-of-an-ingress
    # ingressClassName: nginx
    # -- pathType is only for k8s >= 1.1=
    pathType: Prefix