server:
  # -- Enable deployment of server component. Deployed as StatefulSet
  enabled: true
  persistentVolume:
    # -- Create/use Persistent Volume Claim for server component. Empty dir if false
    enabled: true
    # -- Array of access modes. Must match those of existing PV or dynamic provisioner. Ref: [http://kubernetes.io/docs/user-guide/persistent-volumes/](http://kubernetes.io/docs/user-guide/persistent-volumes/)
    accessModes:
      - ReadWriteOnce
    # -- Size of the volume. Better to set the same as resource limit memory property.
    size: 32Gi
  ingress:
    # -- Enable deployment of ingress for server component
    enabled: false
  nodeSelector:
    kubernetes.io/hostname: ampere1
  service:
    # -- Service port
    servicePort: 8428
    # -- Service type
    type: ClusterIP
  mode: statefulSet
  # -- Scrape configuration for victoriametrics
  scrape:
    # -- If true scrapes targets, creates config map or use specified one with scrape targets
    enabled: false
rbac:
  # Note: The PSP will only be deployed, if Kubernetes (<1.25) supports the resource.
  pspEnabled: false
