groups:

- name: EmbeddedExporter

  rules:

  - alert: ArgocdServiceNotSynced
    expr: 'argocd_app_info{sync_status!="Synced"} != 0'
    for: 15m
    labels:
      severity: warning
    annotations:
      summary: ArgoCD service not synced (instance {{ $labels.instance }})
      description: "Service {{ $labels.name }} run by argo is currently not in sync.\n  VALUE = {{ $value }}\n  LABELS = {{ $labels }}"

  - alert: ArgocdServiceUnhealthy
    expr: 'argocd_app_info{health_status!="Healthy"} != 0'
    for: 15m
    labels:
      severity: warning
    annotations:
      summary: ArgoCD service unhealthy (instance {{ $labels.instance }})
      description: "Service {{ $labels.name }} run by argo is currently not healthy.\n  VALUE = {{ $value }}\n  LABELS = {{ $labels }}"
