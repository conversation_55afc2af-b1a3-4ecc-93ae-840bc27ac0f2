#!/bin/bash

# Script to replace an existing PVC with a new one pointing to a different PV

# Check if the correct number of arguments are provided
if [ "$#" -ne 3 ]; then
    echo "Usage: $0 <NAMESPACE> <PVC_NAME> <NEW_PV_NAME>"
    exit 1
fi

NAMESPACE=$1
PVC_NAME=$2
NEW_PV_NAME=$3
TEMP_PVC_NAME="${PVC_NAME}-temp"

# Create a new PVC based on the old one, but with the new PV name
kubectl get pvc $PVC_NAME -n $NAMESPACE -o yaml | \
sed -E -e "s/name: $PVC_NAME/name: $TEMP_PVC_NAME/" \
    -e "s/volumeName: .+/volumeName: $NEW_PV_NAME/" \
    -e '/uid:/d' \
    -e '/resourceVersion:/d' \
    -e '/creationTimestamp:/d' \
    -e '/selfLink:/d' \
    -e '/status:/,/^spec:/d' | \
kubectl apply -n $NAMESPACE -f -

echo "Temporary PVC $TEMP_PVC_NAME created, pointing to PV $NEW_PV_NAME."

# Wait for the new PVC to be bound
echo "Waiting for the new PVC to be bound..."
# kubectl wait --for=condition=Bound pvc/$TEMP_PVC_NAME -n $NAMESPACE --timeout=60s

# Delete the old PVC
echo "Deleting old PVC $PVC_NAME..."
kubectl delete pvc $PVC_NAME -n $NAMESPACE

# Rename the new PVC to the original name
echo "Renaming new PVC to $PVC_NAME..."
kubectl get pvc $TEMP_PVC_NAME -n $NAMESPACE -o yaml | \
sed -E -e "s/name: $TEMP_PVC_NAME/name: $PVC_NAME/" | \
kubectl apply -n $NAMESPACE -f -

# Delete the temporary PVC
kubectl delete pvc $TEMP_PVC_NAME -n $NAMESPACE

echo "PVC $PVC_NAME in namespace $NAMESPACE has been replaced with a new one pointing to PV $NEW_PV_NAME."

