# Default values for docker-registry-ui.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.

ui:
  # title of the registry
  title: "Docker registry UI"
  # allow delete of images
  delete_images: true

  # The URL of your docker registry, may be a service (when proxy is on) or an external URL.
  dockerRegistryUrl: http://registry-docker-registry:5000
  # UI behave as a proxy of the registry
  proxy: true

  # nodeSelector:
  #   location: contabo
  affinity: {}
    # nodeAffinity:
    #   requiredDuringSchedulingIgnoredDuringExecution:
    #     nodeSelectorTerms:
    #     - matchExpressions:
    #       - key: kubernetes.io/hostname
    #         operator: In
    #         values:
    #         - contabo2
  # additionalSpec:
  #   affinity:
  #     nodeAffinity:
  #       requiredDuringSchedulingIgnoredDuringExecution:
  #         nodeSelectorTerms:
  #         - matchExpressions:
  #           - key: kubernetes.io/hostname
  #             operator: In
  #             values:
  #             - contabo2

  ingress:
    # Enable the ingress for the user interface.
    enabled: true
    # Fully qualified domain name of a network host.
    host: registryui.k8s.sklenarovi.cz
    # Path is matched against the path of an incoming request.
    path: /
    # Determines the interpretation of the Path matching, must be Prefix to serve assets.
    pathType: Prefix
    # TLS configuration
    tls:
      - secretName: registryui-k8s-sklenarovi-cz-tls
        hosts:
          - "registryui.k8s.sklenarovi.cz"
    # Annotations to apply to the user interface ingress.
    annotations:
      cert-manager.io/cluster-issuer: letsencrypt-cloudflare-sklenarovi
      traefik.ingress.kubernetes.io/router.tls: "true"
    # If you want a custom path, you can try this example:
    # path: /ui(/|$)(.*)
    # annotations:
    #  { nginx.ingress.kubernetes.io/rewrite-target: /$2 }
