# Default values for docker-registry.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.
replicaCount: 1

podAnnotations:
  prometheus.io/port: "5001"
  prometheus.io/scrape: "true"

nodeSelector:
  location: oracle

service:
  type: ClusterIP
ingress:
  enabled: true
  annotations:
    cert-manager.io/cluster-issuer: letsencrypt-cloudflare-sklenarovi
    nginx.ingress.kubernetes.io/proxy-body-size: "5000m"
    # nginx.ingress.kubernetes.io/proxy-body-size: 2048m
  path: /
  # Used to create an Ingress record.
  hosts:
    - registry.k8s.sklenarovi.cz
  tls:
    - secretName: registry-k8s-sklenarovi-cz-tls
      hosts:
        - "registry.k8s.sklenarovi.cz"
persistence:
  enabled: true
  existingClaim: registry-docker-registry-lh
  # size: 20Gi

# set the type of filesystem to use: filesystem, s3
storage: filesystem

# Set this to name of secret for tls certs
# tlsSecretName: registry.docker.example.com
# pajikRegistry7
secrets:
  htpasswd: "pavel:$2y$05$bqb5/q4tP4eAgFyNaX5J4OsWnS8noQby5R98gH75RDbx/UsAVUjsC"
# Secrets for Azure
#   azure:
#     accountName: ""
#     accountKey: ""
#     container: ""
# Secrets for S3 access and secret keys
# Use a secretRef with keys (accessKey, secretKey) for secrets stored outside the chart
#   s3:
#     secretRef: ""
#     accessKey: ""
#     secretKey: ""
# Secrets for Swift username and password
#   swift:
#     username: ""
#     password: ""

# Options for s3 storage type:
# s3:
#  region: us-east-1
#  regionEndpoint: s3.us-east-1.amazonaws.com
#  bucket: my-bucket
#  rootdirectory: /object/prefix
#  encrypt: false
#  secure: true

# Options for swift storage type:
# swift:
#  authurl: http://swift.example.com/
#  container: my-container

# https://docs.docker.com/registry/recipes/mirror/
proxy:
  enabled: false
  remoteurl: https://registry-1.docker.io
  username: ""
  password: ""
  # the ref for a secret stored outside of this chart
  # Keys: proxyUsername, proxyPassword
  secretRef: ""

metrics:
  enabled: true

configData:
  http:
    headers:
      Access-Control-Allow-Origin: ["https://registryui.k8s.sklenarovi.cz"]
      X-Content-Type-Options: [nosniff]
      Access-Control-Allow-Methods: ['HEAD', 'GET', 'OPTIONS', 'DELETE']
      Access-Control-Allow-Headers: ['Authorization', 'Accept']
      Access-Control-Max-Age: [1728000]
      Access-Control-Allow-Credentials: [true]
      Access-Control-Expose-Headers: ['Docker-Content-Digest']
    debug:
      prometheus:
        enabled: true
