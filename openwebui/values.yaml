ollama:
  enabled: false

pipelines:
  enabled: false

image:
  repository: "ghcr.io/open-webui/open-webui"
  tag: "v0.6.26"

ingress:
  enabled: true
  host: "ai.k8s.sklenarovi.cz"
  tls: true
  existingSecret: "ai-k8s-sklenarovi-cz-tls"
  annotations:
    cert-manager.io/cluster-issuer: letsencrypt-cloudflare-sklenarovi
    nginx.ingress.kubernetes.io/proxy-body-size: "0"
    nginx.ingress.kubernetes.io/proxy-read-timeout: "3600"
    nginx.ingress.kubernetes.io/proxy-send-timeout: "3600"

persistence:
  enabled: true
  # -- Use existingClaim if you want to re-use an existing Open WebUI PVC instead of creating a new one
  existingClaim: "open-webui-lh"

websocket:
  # -- Enables websocket support in Open WebUI with env `ENABLE_WEBSOCKET_SUPPORT`
  enabled: false

extraEnvVars:
  - name: GLOBAL_LOG_LEVEL
    value: "DEBUG"
  - name: OPENAI_API_KEY
    value: "********************************************************************************************************************************************************************"
  - name: AZURE_OPENAI_API_KEY_GPT
    value: ********************************
  - name: AZURE_OPENAI_ENDPOINT_GPT
    value: https://openai-sklenar.openai.azure.com/
  - name: AZURE_OPENAI_DEPLOYMENT_NAME_GPT
    value: gpt-4o-latest-2024-08
  - name: AZURE_OPENAI_API_VERSION_GPT
    value: 2024-08-01-preview
