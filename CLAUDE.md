# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a home Kubernetes infrastructure repository using GitOps principles with ArgoCD. It manages home automation services, monitoring, and supporting infrastructure deployed to a home Kubernetes cluster.

## Architecture

- **GitOps Pattern**: ArgoCD app-of-apps pattern where `/argocd/app-of-apps.yaml` manages all applications
- **Application Definitions**: Individual apps defined in `/apps/application-*.yaml`
- **Configuration Methods**: Mix of Helm charts (with values.yaml) and Kustomize overlays
- **Storage**: Uses Longhorn with multiple storage classes (global, home, local)
- **Networking**: MetalLB for LoadBalancer services, network policies for security

## Common Commands

### Initial ArgoCD Deployment
```bash
./argocd/deploy-argocd.sh
```

### Network Testing
```bash
./scripts/test-k8s-network.sh
```

### PVC/PV Operations
```bash
# Migrate PVC between storage classes
./migrate-pvc.sh <namespace> <pvc-name> <new-storage-class> [node-selector]

# Recreate PVC (useful for changing parameters)
./recreatePvc.sh <namespace> <pvc-name> <pv-name>

# Recreate PV (useful for node migrations)
./recreatePv.sh <pv-name> <new-pv-name> <new-node-name>
```

### Application Management
Applications are managed through ArgoCD. To add/modify applications:
1. Edit or create new `application-*.yaml` in `/apps/`
2. Commit and push changes
3. ArgoCD will automatically sync

## Key Directories

- `/apps/` - ArgoCD application definitions
- `/*/values.yaml` - Helm chart configurations for each service
- `/*/kustomize/` - Kustomize configurations for services not using Helm
- `/network-policies/` - Network security policies per namespace
- `/prepare-app/` - Pre-deployment configurations
- `/home-services/` - Service endpoints for home devices

## Important Services

### Core Infrastructure
- **ArgoCD** - GitOps deployment
- **MetalLB** - Bare metal load balancer
- **Longhorn** - Distributed storage
- **Victoria Metrics** - Monitoring stack
- **Loki** - Log aggregation

### Home Automation
- **Home Assistant** - Main automation platform
- **Zigbee2MQTT** - Zigbee device bridge
- **Mosquitto** - MQTT broker
- **Frigate** - AI-powered NVR
- **AppDaemon** - Automation framework

## Development Guidelines

1. **Helm Values**: When modifying services, update the appropriate `values.yaml` file
2. **Network Policies**: Each namespace should have corresponding network policies in `/network-policies/`
3. **Storage Classes**: Use appropriate storage class based on requirements (global, home, local)
4. **Ingress**: Services typically use either Traefik or Nginx ingress controllers
5. **Secrets**: Never commit secrets directly; use Kubernetes secrets or external secret management

## Testing Changes

1. For network changes: Run `./scripts/test-k8s-network.sh`
2. For webhook changes: Use scripts in `/mutating-webhook/test/`
3. For application changes: Let ArgoCD sync and monitor the application status

## Dependency Updates

Renovate bot is configured to automatically create PRs for dependency updates. Configuration is in `/renovate.json`.