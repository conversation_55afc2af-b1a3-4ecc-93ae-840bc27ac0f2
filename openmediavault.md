Feb 20th 2019
#13
I have described in my previous post how to start rrdcached manually. I found a thread on this forum where someone (raco<PERSON><PERSON>) found the solution. Cause of this problem is a fake startupscript in debian. The fake startupscript was used during installation and should be replaced by the REAL one. This replacement has failed for some reason, but you can do it manaually once, then reboot your system and rrdcached starts fine.



See this thread to read racoonsn fix: https://forum.openmediavault.o…3-rrdcached-plugin-error/



Solution is to replace a fake startup script with a real startup scripts. The real file already exists, you just have to copy it over the fake one. They both exist in the /sbin directory.



Code
ls -al /sbin/start-stop-daemon*
-rwxr-xr-x 1 <USER> <GROUP> 94 Feb 14 23:12 /sbin/start-stop-daemon
-rwxr-xr-x 1 <USER> <GROUP> 31848 Jun 26 2018 /sbin/start-stop-daemon.REAL
Content of fake script, which is active by default:

Code
cat start-stop-daemon
#!/bin/sh
echo 1>&2
echo 'Warning: Fake start-stop-daemon called, doing nothing.' 1>&2
exit 0
Now I just copied the REAL script (binary) over the fake script.



Code
cd /sbin/
cp start-stop-daemon.REAL start-stop-daemon
Reboot you omv installation and rrdcached starts automatically. Errors are gone and graphs start to work.