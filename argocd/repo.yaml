apiVersion: v1
kind: Secret
metadata:
  name: pajikos-home-iaac
  namespace: argocd
  labels:
    argocd.argoproj.io/secret-type: repository
stringData:
  type: git
  url: **************:pajikos/home-iaac.git

---

apiVersion: v1
kind: Secret
metadata:
  labels:
    argocd.argoproj.io/secret-type: repository
  name: ghcr-io-mkilchhofer-unifi-chart
  namespace: argocd
stringData:
  url: ghcr.io/mkilchhofer/unifi-chart
  name: unifi-chart
  type: helm
  enableOCI: "true"

---

apiVersion: v1
kind: Secret
metadata:
  labels:
    argocd.argoproj.io/secret-type: repository
  name: 8gears-n8n-chart
  namespace: argocd
stringData:
  url: 8gears.container-registry.com/library
  name: n8n-chart
  type: helm
  enableOCI: "true"
