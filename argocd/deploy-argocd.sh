#!/bin/bash

# Set strict mode
set -euo pipefail

# Define variables
CHART_NAME="argo-cd"
CHART_REPO="oci://ghcr.io/argoproj/argo-helm"
CHART_VERSION="8.3.1"
RELEASE_NAME="argocd"
NAMESPACE="argocd"
VALUES_FILE="values.yaml"

# Function to check if a command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Check if required commands are available
if ! command_exists helm; then
    echo "Error: helm is not installed. Please install helm and try again."
    exit 1
fi

if ! command_exists kubectl; then
    echo "Error: kubectl is not installed. Please install kubectl and try again."
    exit 1
fi

# Check and fix Kubernetes config file permissions
KUBE_CONFIG="${KUBECONFIG:-$HOME/.kube/config}"
if [ -f "$KUBE_CONFIG" ]; then
    echo "Fixing Kubernetes config file permissions..."
    chmod 600 "$KUBE_CONFIG"
fi

# Check if the namespace exists, create if it doesn't
if ! kubectl get namespace "$NAMESPACE" >/dev/null 2>&1; then
    echo "Creating namespace $NAMESPACE..."
    kubectl create namespace "$NAMESPACE"
fi

# Deploy Argo CD using Helm
echo "Deploying Argo CD..."
helm upgrade --install "$RELEASE_NAME" "$CHART_REPO/$CHART_NAME" \
    --namespace "$NAMESPACE" \
    --version "$CHART_VERSION" \
    --values "$VALUES_FILE" \
    --set crds.install=true

echo "Argo CD deployment completed successfully!"

echo "Argo CD is now ready to use!"

