# # Argo CD configuration
global:
  addPrometheusAnnotations: true

  # Default image used by all components
  # image:
  #   # -- Overrides the global Argo CD image tag whose default is the chart appVersion
  #  tag: "v2.10.9"
  logging:
    # -- Set the global logging level. One of: `debug`, `info`, `warn` or `error`
    level: info

  nodeSelector:
    node-role.kubernetes.io/master: "true"
    location: "oracle"


controller:
  metrics:
    enabled: true

# # Server
server:
  # # Server metrics service configuration
  metrics:
    # -- Deploy metrics service
    enabled: true


  ingress:
    # -- Enable an ingress resource for the Argo CD server
    enabled: true

    annotations:
      cert-manager.io/cluster-issuer: letsencrypt-cloudflare-sklenarovi
      nginx.ingress.kubernetes.io/backend-protocol: "HTTP"

      #          - argocd.sklenarovi.cz
    hostname: argocd.k8s.sklenarovi.cz
    extraTls:
    - hosts:
      # - argocd.sklenarovi.cz
      - argocd.k8s.sklenarovi.cz
      secretName: argocd-k8s-sklenarovi-cz-tls

    # -- Uses `server.service.servicePortHttps` instead `server.service.servicePortHttp`
    #          https: false


configs:

  rbac:
    policy.default: 'role:admin'

  cm:
    controller.diff.server.side: "true"
    reposerver.enable.git.submodule: "true"
    # exec.enabled indicates whether the UI exec feature is enabled. It is disabled by default.
    # Ref: https://argo-cd.readthedocs.io/en/latest/operator-manual/rbac/#exec-resource
    exec.enabled: "true"
    # https://argo-cd.readthedocs.io/en/stable/operator-manual/health/#custom-health-checks
    # https://github.com/argoproj/argo-cd/issues/1704
    # https://argo-cd.readthedocs.io/en/stable/faq/#why-is-my-application-stuck-in-progressing-state

    resource.customizations: |
      networking.k8s.io/Ingress:
        health.lua: |
          hs = {}
          hs.status = "Healthy"
          return hs

    admin.enabled: "true"
    url: https://argocd.k8s.sklenarovi.cz
    users.anonymous.enabled: "false"
    dex.config: |
      connectors:
      - config:
          issuer: https://accounts.google.com
          clientID: ************-9d98qf9jcjkp9i1uc716h656raamntc1.apps.googleusercontent.com
          clientSecret: 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
        type: oidc
        id: google
        name: Google

      # oidc.config: |
      # name: Google
      # issuer: https://accounts.google.com
      # clientID: ************-9d98qf9jcjkp9i1uc716h656raamntc1.apps.googleusercontent.com
      # clientSecret: 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
      # requestedScopes:
      # - openid
      # - email

  params:
    # # Server properties
    # -- Run server without TLS
    server.insecure: true
