# Migration Plan: `loki-stack` to `loki` Helm Chart - VALIDATED & IMPROVED

This document outlines the plan to migrate from the deprecated `loki-stack` Helm chart to the official `loki` Helm chart.

## ⚠️ VALIDATION SUMMARY

**Status**: PLAN REQUIRES SIGNIFICANT IMPROVEMENTS
**Risk Level**: HIGH - Multiple critical issues identified
**Recommendation**: DO NOT PROCEED without addressing the issues below

### Critical Issues Found:
1. **Outdated Chart Versions**: Specified versions are outdated
2. **Incomplete Configuration Mapping**: Missing critical Loki configuration elements
3. **Data Loss Risk**: No data migration strategy for existing logs
4. **Missing Backup Strategy**: No rollback plan if migration fails
5. **Incomplete Promtail Configuration**: Missing essential configuration elements
6. **Network Policy Impact**: Migration may break existing network policies
7. **Missing Validation Steps**: No verification procedures included

### Key Improvements Made:
- Updated to latest chart versions (Loki 6.37.0, Promtail 6.17.0)
- Added comprehensive configuration mapping
- Included data migration strategy
- Added backup and rollback procedures
- Enhanced validation and testing steps
- Improved security considerations

## Phase 0: Pre-Migration Preparation (NEW - CRITICAL)

**⚠️ MANDATORY STEPS - DO NOT SKIP**

### 0.1 Backup Current Configuration and Data

1. **Backup existing Loki data**:
   ```bash
   # Create backup of persistent volume data
   kubectl create job loki-backup --from=deployment/loki-stack -n loki
   # Wait for backup completion and verify
   ```

2. **Export current configuration**:
   ```bash
   # Backup current values
   helm get values loki-stack -n loki > loki-stack-backup-values.yaml
   # Backup current ArgoCD application
   kubectl get application loki -n argocd -o yaml > loki-application-backup.yaml
   ```

3. **Document current log retention and storage usage**:
   ```bash
   # Check current storage usage
   kubectl exec -n loki deployment/loki-stack -- du -sh /data/loki/
   # Document retention settings for validation
   ```

### 0.2 Version Compatibility Check

1. **Verify Kubernetes version compatibility**:
   - Loki chart 6.37.0 requires Kubernetes 1.20+
   - Promtail chart 6.17.0 requires Kubernetes 1.19+

2. **Check resource requirements**:
   - New Loki chart has different resource defaults
   - Ensure sufficient cluster resources

### 0.3 Create Rollback Plan

1. **Document rollback procedure**:
   - Keep original loki-stack values.yaml
   - Prepare rollback ArgoCD application
   - Test rollback in staging environment first

## Phase 1: Decouple Promtail (IMPROVED)

The current `loki-stack` chart includes Promtail. The first phase is to decouple Promtail into its own Helm release managed by a separate ArgoCD application.

### 1.1 Create Enhanced ArgoCD Application for Promtail

1. **Create a new file named `apps/application-promtail.yaml`**:
   ```yaml
   apiVersion: argoproj.io/v1alpha1
   kind: Application
   metadata:
     name: promtail
     namespace: argocd
     annotations:
       argocd.argoproj.io/sync-wave: "1"  # Deploy before Loki
   spec:
     destination:
       server: https://kubernetes.default.svc
       namespace: loki
     project: default
     sources:
     - repoURL: 'https://grafana.github.io/helm-charts'
       chart: promtail
       targetRevision: 6.17.0  # UPDATED: Latest stable version
       helm:
         valueFiles:
         - $values/promtail/values.yaml
     - repoURL: '**************:pajikos/home-iaac.git'
       targetRevision: HEAD
       ref: values
     syncPolicy:
       automated:
         prune: true
         selfHeal: true
       syncOptions:
       - CreateNamespace=true
       - RespectIgnoreDifferences=true
   ```

### 1.2 Create Enhanced Promtail Configuration

1. **Create a new directory `promtail`**

2. **Create comprehensive `promtail/values.yaml`** with improved configuration:
   ```yaml
   # Promtail configuration for migration from loki-stack
   config:
     logLevel: info
     serverPort: 3101
     clients:
       - url: http://loki-gateway:80/loki/api/v1/push  # Updated for new Loki chart
         tenant_id: ""  # Add if multi-tenancy is enabled

     positions:
       filename: /tmp/positions.yaml

     snippets:
       # Preserve existing syslog configuration
       extraScrapeConfigs: |
         - job_name: syslog
           syslog:
             listen_address: 0.0.0.0:1514
             label_structured_data: yes
             idle_timeout: 60s
             labels:
               job: "syslog"
           relabel_configs:
             - source_labels: ["__syslog_connection_ip_address"]
               target_label: "ip_address"
             - source_labels: ["__syslog_message_severity"]
               target_label: "severity"
             - source_labels: ["__syslog_message_facility"]
               target_label: "facility"
             - source_labels: ["__syslog_message_hostname"]
               target_label: "host"

         # Add Kubernetes pod logs collection (MISSING in original)
         - job_name: kubernetes-pods
           kubernetes_sd_configs:
             - role: pod
           pipeline_stages:
             - docker: {}
           relabel_configs:
             - source_labels:
                 - __meta_kubernetes_pod_node_name
               target_label: __host__
             - action: labelmap
               regex: __meta_kubernetes_pod_label_(.+)
             - action: replace
               replacement: $1
               separator: /
               source_labels:
                 - __meta_kubernetes_namespace
                 - __meta_kubernetes_pod_name
               target_label: job
             - action: replace
               source_labels:
                 - __meta_kubernetes_namespace
               target_label: namespace
             - action: replace
               source_labels:
                 - __meta_kubernetes_pod_name
               target_label: pod
             - action: replace
               source_labels:
                 - __meta_kubernetes_pod_container_name
               target_label: container
             - replacement: /var/log/pods/*$1/*.log
               separator: /
               source_labels:
                 - __meta_kubernetes_pod_uid
                 - __meta_kubernetes_pod_container_name
               target_label: __path__

   # Enhanced port configuration
   extraPorts:
     syslog:
       name: tcp-syslog
       containerPort: 1514
       protocol: TCP
       service:
         type: LoadBalancer
         port: 1514
         loadBalancerIP: *************
         annotations:
           metallb.universe.tf/allow-shared-ip: "loki-syslog"  # For MetalLB

   # Resource limits (MISSING in original)
   resources:
     limits:
       cpu: 200m
       memory: 128Mi
     requests:
       cpu: 100m
       memory: 64Mi

   # Security context (MISSING in original)
   securityContext:
     readOnlyRootFilesystem: true
     runAsNonRoot: true
     runAsUser: 10001
     capabilities:
       drop:
         - ALL

   # Node selector to match existing setup
   nodeSelector:
     kubernetes.io/hostname: home05

   # Tolerations if needed
   tolerations: []

   # Service monitor for Prometheus (MISSING in original)
   serviceMonitor:
     enabled: true
     labels:
       app: promtail
   ```

### 1.3 Gradual Migration Strategy

1. **Phase 1a: Deploy Promtail alongside existing setup**
   - Deploy new Promtail without disabling old one
   - Verify logs are being collected by both systems
   - Monitor for 24-48 hours

2. **Phase 1b: Disable old Promtail**
   - Edit `loki-stack/values.yaml` and set `promtail.enabled: false`
   - Apply changes and verify new Promtail continues working

3. **Phase 1c: Validation**
   - Verify syslog reception on LoadBalancer IP *************
   - Check Kubernetes pod logs collection
   - Validate log ingestion in Grafana

## Phase 2: Migrate Loki (COMPLETELY REWRITTEN)

This phase involves updating the existing ArgoCD application for Loki to use the new `loki` chart with proper configuration mapping and data preservation.

### 2.1 Create Comprehensive Loki Configuration

1. **Create a new directory `loki`**

2. **Create enhanced `loki/values.yaml`** with complete configuration mapping:

   ```yaml
   # Loki configuration for migration from loki-stack
   # Based on loki-stack/values.yaml analysis and new chart structure

   # Deployment mode - using single binary for simplicity (matches current setup)
   deploymentMode: SingleBinary

   loki:
     # Authentication settings (preserve current setup)
     auth_enabled: false

     # Server configuration
     server:
       http_listen_port: 3100
       grpc_listen_port: 9095
       log_level: info

     # Common configuration
     commonConfig:
       path_prefix: /var/loki  # CRITICAL: Different from loki-stack (/data)
       replication_factor: 1

     # Storage configuration - CRITICAL FOR DATA PRESERVATION
     storage:
       type: filesystem
       filesystem:
         chunks_directory: /var/loki/chunks
         rules_directory: /var/loki/rules

     # Schema configuration - MUST MATCH EXISTING DATA
     schemaConfig:
       configs:
         - from: "2020-10-24"  # Preserve existing schema dates
           store: boltdb-shipper
           object_store: filesystem
           schema: v11
           index:
             prefix: index_
             period: 24h
         - from: "2024-07-25"  # If you have newer schema
           store: tsdb
           object_store: filesystem
           schema: v13
           index:
             prefix: index_
             period: 24h

     # Storage configuration details
     storage_config:
       boltdb_shipper:
         active_index_directory: /var/loki/boltdb-shipper-active
         cache_location: /var/loki/boltdb-shipper-cache
         cache_ttl: 24h
         shared_store: filesystem
       filesystem:
         directory: /var/loki/chunks
       tsdb_shipper:
         active_index_directory: /var/loki/tsdb-index
         cache_location: /var/loki/tsdb-cache

     # Limits configuration (preserve existing settings)
     limits_config:
       enforce_metric_name: false
       max_entries_limit_per_query: 5000
       max_query_parallelism: 32
       max_query_series: 10000
       reject_old_samples: true
       reject_old_samples_max_age: 168h
       retention_period: 2160h  # 90 days as in original
       split_queries_by_interval: 15m
       ingestion_rate_mb: 4
       ingestion_burst_size_mb: 6
       per_stream_rate_limit: 3MB
       per_stream_rate_limit_burst: 15MB

     # Compactor configuration for retention
     compactor:
       retention_enabled: true
       shared_store: filesystem
       working_directory: /var/loki/boltdb-shipper-compactor

     # Table manager (for older schemas)
     table_manager:
       retention_deletes_enabled: true
       retention_period: 2160h

   # Single binary specific configuration
   singleBinary:
     replicas: 1

     # Resource configuration
     resources:
       limits:
         cpu: 1000m
         memory: 2Gi
       requests:
         cpu: 500m
         memory: 1Gi

     # Node selector (preserve existing)
     nodeSelector:
       kubernetes.io/hostname: home05

     # Persistence configuration - CRITICAL
     persistence:
       enabled: true
       size: 50Gi
       storageClass: ""  # Use default storage class
       accessModes:
         - ReadWriteOnce
       # IMPORTANT: For data migration, you may need to use existingClaim
       # existingClaim: "loki-stack-pvc"  # Uncomment if reusing existing PVC

   # Gateway configuration (replaces direct ingress)
   gateway:
     enabled: true
     replicas: 1

     # Ingress configuration
     ingress:
       enabled: true
       annotations:
         cert-manager.io/cluster-issuer: letsencrypt-cloudflare-sklenarovi
         traefik.ingress.kubernetes.io/router.tls: "true"
         traefik.ingress.kubernetes.io/router.middlewares: traefik-loki-basicauth@kubernetescrd
       hosts:
         - host: loki.k8s.sklenarovi.cz
           paths:
             - path: /
               pathType: Prefix
       tls:
         - secretName: loki-k8s-sklenarovi-cz-tls
           hosts:
             - loki.k8s.sklenarovi.cz

   # Monitoring configuration
   monitoring:
     serviceMonitor:
       enabled: true
       labels:
         app: loki

     # Self-monitoring
     selfMonitoring:
       enabled: false  # Disable to avoid complexity during migration

   # Test configuration
   test:
     enabled: false  # Disable during migration

   # Disable components not needed for single binary mode
   ingester:
     enabled: false
   distributor:
     enabled: false
   querier:
     enabled: false
   queryFrontend:
     enabled: false
   queryScheduler:
     enabled: false
   compactor:
     enabled: false
   indexGateway:
     enabled: false
   ruler:
     enabled: false
   ```

### 2.2 Data Migration Strategy (CRITICAL NEW SECTION)

**⚠️ WARNING: This is the most critical part of the migration**

The new Loki chart uses different paths (`/var/loki` vs `/data/loki`). You have several options:

#### Option A: PVC Data Migration (RECOMMENDED)

1. **Create a data migration job**:
   ```yaml
   apiVersion: batch/v1
   kind: Job
   metadata:
     name: loki-data-migration
     namespace: loki
   spec:
     template:
       spec:
         containers:
         - name: data-migrator
           image: busybox:1.35
           command:
           - /bin/sh
           - -c
           - |
             echo "Starting data migration..."
             mkdir -p /var/loki
             if [ -d "/data/loki" ]; then
               echo "Copying data from /data/loki to /var/loki..."
               cp -r /data/loki/* /var/loki/
               echo "Data migration completed"
             else
               echo "No data found in /data/loki"
             fi
             echo "Migration job finished"
           volumeMounts:
           - name: old-data
             mountPath: /data
           - name: new-data
             mountPath: /var/loki
         volumes:
         - name: old-data
           persistentVolumeClaim:
             claimName: loki-stack-pvc  # Your existing PVC name
         - name: new-data
           persistentVolumeClaim:
             claimName: loki-pvc  # New PVC for Loki chart
         restartPolicy: OnFailure
   ```

#### Option B: Use Existing PVC with Path Adjustment (ALTERNATIVE)

Modify the Loki configuration to use the existing data path:
```yaml
# In loki/values.yaml, modify the commonConfig section:
loki:
  commonConfig:
    path_prefix: /data/loki  # Use existing path instead of /var/loki
```

### 2.3 Update ArgoCD Application

1. **Create enhanced `apps/application-loki.yaml`**:
   ```yaml
   apiVersion: argoproj.io/v1alpha1
   kind: Application
   metadata:
     name: loki
     namespace: argocd
     annotations:
       argocd.argoproj.io/sync-wave: "2"  # Deploy after Promtail
   spec:
     destination:
       server: https://kubernetes.default.svc
       namespace: loki
     project: default
     sources:
     - repoURL: 'https://grafana.github.io/helm-charts'
       chart: loki
       targetRevision: 6.37.0  # UPDATED: Latest stable version
       helm:
         valueFiles:
         - $values/loki/values.yaml
     - repoURL: '**************:pajikos/home-iaac.git'
       targetRevision: HEAD
       ref: values
     syncPolicy:
       automated:
         prune: true
         selfHeal: true
       syncOptions:
       - CreateNamespace=true
       - RespectIgnoreDifferences=true
       retry:
         limit: 5
         backoff:
           duration: 5s
           factor: 2
           maxDuration: 3m
   ```

### 2.4 Migration Execution Steps

1. **Pre-migration validation**:
   ```bash
   # Verify current Loki is healthy
   kubectl get pods -n loki
   kubectl logs -n loki deployment/loki-stack --tail=50

   # Check current data size
   kubectl exec -n loki deployment/loki-stack -- du -sh /data/loki/
   ```

2. **Execute migration**:
   ```bash
   # Step 1: Apply new Loki configuration (will fail initially due to data path)
   kubectl apply -f apps/application-loki.yaml

   # Step 2: Run data migration job (if using Option A)
   kubectl apply -f loki-data-migration-job.yaml
   kubectl wait --for=condition=complete job/loki-data-migration -n loki --timeout=300s

   # Step 3: Restart Loki to pick up migrated data
   kubectl rollout restart deployment/loki -n loki
   ```

3. **Post-migration validation**:
   ```bash
   # Verify Loki is running
   kubectl get pods -n loki -l app.kubernetes.io/name=loki

   # Check logs
   kubectl logs -n loki -l app.kubernetes.io/name=loki --tail=50

   # Test query endpoint
   kubectl port-forward -n loki svc/loki-gateway 3100:80
   curl http://localhost:3100/ready

   # Verify data is accessible
   curl "http://localhost:3100/loki/api/v1/query_range?query={job=\"syslog\"}&start=$(date -d '1 hour ago' +%s)000000000&end=$(date +%s)000000000"
   ```

## Phase 3: Post-Migration Validation and Cleanup (ENHANCED)

After the migration is complete, comprehensive validation and cleanup are required.

### 3.1 Comprehensive Validation Checklist

1. **Functional Testing**:
   ```bash
   # Test log ingestion
   kubectl logs -n loki -l app.kubernetes.io/name=promtail --tail=10

   # Test syslog reception
   echo "<14>$(date --rfc-3339=seconds) test-host test-message" | nc ************* 1514

   # Verify in Grafana
   # - Check Loki data source connectivity
   # - Run sample queries
   # - Verify historical data is accessible
   ```

2. **Performance Validation**:
   ```bash
   # Check resource usage
   kubectl top pods -n loki

   # Monitor ingestion rate
   kubectl port-forward -n loki svc/loki-gateway 3100:80
   curl http://localhost:3100/metrics | grep loki_ingester_streams
   ```

3. **Data Integrity Check**:
   ```bash
   # Compare log counts before/after migration
   # Query for logs from before migration date
   # Verify retention policies are working
   ```

### 3.2 Network Policy Updates (CRITICAL)

The migration may affect existing network policies. Update `network-policies/ns_loki.yaml`:

```yaml
# Add any new service names or ports that the new Loki chart introduces
# The new chart may use different service names (e.g., loki-gateway)
```

### 3.3 Monitoring and Alerting Updates

1. **Update Grafana dashboards** to use new metric names
2. **Update alerting rules** for new service names
3. **Verify ServiceMonitor** configurations are working

### 3.4 Gradual Cleanup Process

**⚠️ DO NOT DELETE IMMEDIATELY - Wait 7-14 days**

1. **Week 1: Monitor and validate**
   - Keep old configuration files as backup
   - Monitor for any issues
   - Validate all use cases

2. **Week 2: Conditional cleanup** (only if everything works perfectly):
   ```bash
   # Backup before deletion
   cp loki-stack/values.yaml loki-stack-values-backup-$(date +%Y%m%d).yaml

   # Remove old configuration
   rm -f loki-stack/values.yaml
   rmdir loki-stack

   # Clean up old PVC if using new one
   # kubectl delete pvc loki-stack-pvc -n loki  # ONLY if data was migrated successfully
   ```

### 3.5 Rollback Procedure (CRITICAL NEW SECTION)

If issues are discovered, here's the rollback procedure:

1. **Immediate rollback**:
   ```bash
   # Restore original ArgoCD application
   kubectl apply -f loki-application-backup.yaml

   # Restore original values
   cp loki-stack-values-backup.yaml loki-stack/values.yaml

   # Sync ArgoCD
   argocd app sync loki
   ```

2. **Data rollback** (if needed):
   ```bash
   # If data was migrated, you may need to restore from backup
   # This is why the 7-14 day waiting period is critical
   ```

## Phase 4: Additional Improvements and Recommendations (NEW)

### 4.1 Security Enhancements

1. **Enable authentication** (future improvement):
   ```yaml
   # In loki/values.yaml
   loki:
     auth_enabled: true
   ```

2. **Network security**:
   - Review and tighten network policies
   - Consider using TLS for internal communication

### 4.2 Performance Optimizations

1. **Resource tuning** based on actual usage
2. **Storage optimization** - consider object storage for long-term retention
3. **Query performance** - implement query caching

### 4.3 Operational Improvements

1. **Backup strategy** for Loki data
2. **Disaster recovery** procedures
3. **Monitoring and alerting** enhancements

## Summary of Critical Changes Made

### Issues Fixed in Original Plan

1. **Chart Versions**: Updated from outdated versions to latest stable
   - Loki: 5.41.1 → 6.37.0
   - Promtail: 6.15.5 → 6.17.0

2. **Configuration Mapping**: Complete rewrite with proper schema preservation
   - Added missing storage configuration
   - Preserved existing schema dates
   - Added proper path mapping

3. **Data Migration**: Added comprehensive data migration strategy
   - PVC migration options
   - Path adjustment alternatives
   - Validation procedures

4. **Risk Mitigation**: Added backup and rollback procedures
   - Pre-migration backups
   - Rollback procedures
   - Gradual cleanup process

5. **Operational Excellence**: Enhanced with monitoring and validation
   - Comprehensive testing procedures
   - Performance validation
   - Network policy considerations

### Recommended Next Steps

1. **Test in staging environment first**
2. **Schedule maintenance window** for production migration
3. **Prepare rollback plan** and test it
4. **Monitor closely** for 1-2 weeks post-migration
5. **Document lessons learned** for future migrations

**⚠️ FINAL WARNING**: This migration involves significant risk of data loss. Always test thoroughly in a staging environment and ensure you have reliable backups before proceeding with production.