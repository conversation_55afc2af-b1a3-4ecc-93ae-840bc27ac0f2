apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: zigbee2mqtt
  labels:
    app.kubernetes.io/instance: zigbee2mqtt
    app.kubernetes.io/name: zigbee2mqtt
spec:
  revisionHistoryLimit: 3
  replicas: 1
  podManagementPolicy: OrderedReady
  updateStrategy:
    type: RollingUpdate
  selector:
    matchLabels:
      app.kubernetes.io/name: zigbee2mqtt
      app.kubernetes.io/instance: zigbee2mqtt
  serviceName: zigbee2mqtt
  template:
    metadata:
      labels:
        app.kubernetes.io/name: zigbee2mqtt
        app.kubernetes.io/instance: zigbee2mqtt
    spec:
      serviceAccountName: default
      automountServiceAccountToken: true
      dnsPolicy: ClusterFirst
      enableServiceLinks: true
      containers:
        - name: zigbee2mqtt
          image: ghcr.io/koenkk/zigbee2mqtt
          imagePullPolicy:
          env:
            - name: TZ
              value: Europe/Prague
            - name: ZIGBEE2MQTT_CONFIG_MQTT_BASE_TOPIC
              value: zigbee2mqttv2
            - name: ZIGBEE2MQTT_CONFIG_MQTT_CLIENT_ID
              value: home01-zigbee
            - name: ZIGBEE2MQTT_CONFIG_MQTT_PASSWORD
              value: admin
            - name: ZIGBEE2MQTT_CONFIG_MQTT_USER
              value: home01-zigbee
            - name: ZIGBEE2MQTT_DATA
              value: /data
          ports:
            - name: http
              containerPort: 8080
              protocol: TCP
          volumeMounts:
            - mountPath: /data
              name: data
          livenessProbe:
            failureThreshold: 3
            initialDelaySeconds: 0
            periodSeconds: 10
            tcpSocket:
              port: 8080
            timeoutSeconds: 1
          readinessProbe:
            failureThreshold: 3
            initialDelaySeconds: 0
            periodSeconds: 10
            tcpSocket:
              port: 8080
            timeoutSeconds: 1
          startupProbe:
            failureThreshold: 30
            initialDelaySeconds: 0
            periodSeconds: 5
            tcpSocket:
              port: 8080
            timeoutSeconds: 1
      nodeSelector:
        kubernetes.io/hostname: home03
        location: home
      volumes:
        - name: data
          persistentVolumeClaim:
            claimName: data-zigbee2mqtt-lh
