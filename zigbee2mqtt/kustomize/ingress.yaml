apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: zigbee2mqtt
  labels:
    app.kubernetes.io/instance: zigbee2mqtt
    app.kubernetes.io/name: zigbee2mqtt
  annotations:
    cert-manager.io/cluster-issuer: letsencrypt-cloudflare-sklenarovi
    nginx.ingress.kubernetes.io/auth-signin: https://oauth.k8s.sklenarovi.cz/oauth2/start?rd=$pass_access_scheme://$http_host$escaped_request_uri
    nginx.ingress.kubernetes.io/auth-url: https://oauth.k8s.sklenarovi.cz/oauth2/auth
    traefik.ingress.kubernetes.io/router.middlewares: traefik-oauth-auth-redirect@kubernetescrd
    traefik.ingress.kubernetes.io/router.tls: "true"
spec:
  tls:
    - hosts:
        - "zigbee2mqtt.k8s.sklenarovi.cz"
      secretName: "zigbee2mqtt-k8s-sklenarovi-cz-tls"
  rules:
    - host: "zigbee2mqtt.k8s.sklenarovi.cz"
      http:
        paths:
          - path: "/"
            pathType: Prefix
            backend:
              service:
                name: zigbee2mqtt
                port:
                  number: 8080
