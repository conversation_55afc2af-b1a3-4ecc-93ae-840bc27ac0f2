apiVersion: v1
kind: Service
metadata:
  name: zigbee2mqtt
  labels:
    app.kubernetes.io/service: zigbee2mqtt
    app.kubernetes.io/instance: zigbee2mqtt
    app.kubernetes.io/name: zigbee2mqtt
  annotations:
spec:
  type: ClusterIP
  ports:
    - port: 8080
      targetPort: http
      protocol: TCP
      name: http
  selector:
    app.kubernetes.io/instance: zigbee2mqtt
    app.kubernetes.io/name: zigbee2mqtt
