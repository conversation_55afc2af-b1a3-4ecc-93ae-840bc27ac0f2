#!/bin/bash
set -e

# Function to display usage information
function show_usage {
  echo "Usage: $0 --source-pvc SOURCE_PVC --namespace NAMESPACE --target-pvc TARGET_PVC --storage-class STORAGE_CLASS [--node-selector NODE_SELECTOR]"
  echo ""
  echo "  --source-pvc       Name of the source PVC to copy data from"
  echo "  --namespace        Kubernetes namespace where PVCs exist"
  echo "  --target-pvc       Name of the target PVC to create and copy data to"
  echo "  --storage-class    Storage class to use for the target PVC"
  echo "  --node-selector    Node selector for the migration job (default: location=home)"
  exit 1
}

# Parse command line arguments
while [[ $# -gt 0 ]]; do
  key="$1"
  case $key in
    --source-pvc)
      SOURCE_PVC="$2"
      shift 2
      ;;
    --namespace)
      NAMESPACE="$2"
      shift 2
      ;;
    --target-pvc)
      TARGET_PVC="$2"
      shift 2
      ;;
    --storage-class)
      STORAGE_CLASS="$2"
      shift 2
      ;;
    --node-selector)
      NODE_SELECTOR="$2"
      shift 2
      ;;
    --help)
      show_usage
      ;;
    *)
      echo "Unknown parameter: $1"
      show_usage
      ;;
  esac
done

# Set default node selector if not provided
if [[ -z "${NODE_SELECTOR}" ]]; then
  NODE_SELECTOR="location=home"
fi

# Validate required parameters
if [[ -z "${SOURCE_PVC}" || -z "${NAMESPACE}" || -z "${TARGET_PVC}" || -z "${STORAGE_CLASS}" ]]; then
  echo "Error: Missing required parameters"
  show_usage
fi

# Check if kubectl is available
if ! command -v kubectl &> /dev/null; then
  echo "Error: kubectl is required but not installed. Please install kubectl first."
  exit 1
fi

echo "Starting PVC migration:"
echo "Source PVC: ${SOURCE_PVC}"
echo "Target PVC: ${TARGET_PVC}"
echo "Namespace: ${NAMESPACE}"
echo "Storage Class: ${STORAGE_CLASS}"
echo "Node Selector: ${NODE_SELECTOR}"

# Check if the source PVC exists
if ! kubectl get pvc "${SOURCE_PVC}" -n "${NAMESPACE}" &> /dev/null; then
  echo "Error: Source PVC '${SOURCE_PVC}' does not exist in namespace '${NAMESPACE}'"
  exit 1
fi

# Check if the target PVC already exists
if kubectl get pvc "${TARGET_PVC}" -n "${NAMESPACE}" &> /dev/null; then
  echo "Target PVC '${TARGET_PVC}' already exists in namespace '${NAMESPACE}', skipping creation."
else
  # Get source PVC information
  echo "Retrieving source PVC information..."
  CAPACITY=$(kubectl get pvc "${SOURCE_PVC}" -n "${NAMESPACE}" -o jsonpath='{.spec.resources.requests.storage}')
  ACCESS_MODES=$(kubectl get pvc "${SOURCE_PVC}" -n "${NAMESPACE}" -o jsonpath='{.spec.accessModes[*]}')

  echo "Source PVC capacity: ${CAPACITY}"
  echo "Source PVC access modes: ${ACCESS_MODES}"

  # Create target PVC with same capacity and access modes
  echo "Creating target PVC '${TARGET_PVC}'..."

  # Create YAML for the new PVC
  cat <<EOF > target_pvc.yaml
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: ${TARGET_PVC}
  namespace: ${NAMESPACE}
spec:
  accessModes:
EOF

  # Add access modes from the source PVC
  for mode in ${ACCESS_MODES}; do
    echo "    - ${mode}" >> target_pvc.yaml
  done

  # Complete the YAML
  cat <<EOF >> target_pvc.yaml
  resources:
    requests:
      storage: ${CAPACITY}
  storageClassName: ${STORAGE_CLASS}
EOF

  # Create the target PVC
  kubectl apply -f target_pvc.yaml
  if [ $? -ne 0 ]; then
    echo "Error: Failed to create target PVC '${TARGET_PVC}'"
    rm target_pvc.yaml
    exit 1
  fi

  # Clean up the temporary YAML file
  rm target_pvc.yaml
fi

# Wait for the target PVC to be bound
echo "Waiting for target PVC to be bound..."
while true; do
  PVC_STATUS=$(kubectl get pvc "${TARGET_PVC}" -n "${NAMESPACE}" -o jsonpath='{.status.phase}')
  if [ "${PVC_STATUS}" == "Bound" ]; then
    echo "Target PVC is now bound."
    break
  elif [ "${PVC_STATUS}" == "Failed" ]; then
    echo "Error: Target PVC failed to bind."
    exit 1
  fi

  echo "Waiting for PVC to be bound... (status: ${PVC_STATUS})"
  sleep 5
done

# Create a data migration job - macOS compatible random suffix
RANDOM_SUFFIX=$(LC_ALL=C tr -dc 'a-z0-9' < /dev/urandom | fold -w 6 | head -n 1)
JOB_NAME="pvc-migration-to-${TARGET_PVC}-${RANDOM_SUFFIX}"

echo "Creating data migration job '${JOB_NAME}'..."
cat <<EOF | kubectl apply -f -
apiVersion: batch/v1
kind: Job
metadata:
  name: ${JOB_NAME}
  namespace: ${NAMESPACE}
spec:
  backoffLimit: 3
  template:
    metadata:
      labels:
        job: ${JOB_NAME}
    spec:
      nodeSelector:
$(for pair in $(echo "${NODE_SELECTOR}" | tr ',' ' '); do
  key=$(echo "$pair" | cut -d'=' -f1)
  value=$(echo "$pair" | cut -d'=' -f2-)
  echo "        $key: \"$value\""
done)
      restartPolicy: OnFailure
      containers:
        - name: data-migration
          image: alpine:latest
          command: ["/bin/sh"]
          args:
            - "-c"
            - |
              apk add --no-cache rsync
              echo "Starting data migration from ${SOURCE_PVC} to ${TARGET_PVC}..."
              rsync -av --delete /mnt/source/ /mnt/target/
              echo "Data migration completed successfully."
          volumeMounts:
            - name: source-vol
              mountPath: /mnt/source
            - name: target-vol
              mountPath: /mnt/target
      volumes:
        - name: source-vol
          persistentVolumeClaim:
            claimName: ${SOURCE_PVC}
        - name: target-vol
          persistentVolumeClaim:
            claimName: ${TARGET_PVC}
EOF

if [ $? -ne 0 ]; then
  echo "Error: Failed to create data migration job"
  exit 1
fi

echo "Data migration job created. Waiting for job to complete..."

# Monitor job until completion
while true; do
  JOB_STATUS=$(kubectl get job "${JOB_NAME}" -n "${NAMESPACE}" -o jsonpath='{.status.conditions[?(@.type=="Complete")].status}' 2>/dev/null)
  JOB_FAILED=$(kubectl get job "${JOB_NAME}" -n "${NAMESPACE}" -o jsonpath='{.status.conditions[?(@.type=="Failed")].status}' 2>/dev/null)

  if [ "${JOB_STATUS}" == "True" ]; then
    echo "Data migration completed successfully!"
    break
  elif [ "${JOB_FAILED}" == "True" ]; then
    echo "Data migration job failed."
    kubectl logs -n "${NAMESPACE}" -l "job=${JOB_NAME}" --tail=50
    exit 1
  fi

  echo "Migration in progress... (Ctrl+C to stop monitoring, migration will continue)"
  sleep 10
done

# Display logs from the job
echo "Migration job logs:"
kubectl logs -n "${NAMESPACE}" -l "job=${JOB_NAME}" --tail=20

echo "Cleaning up migration job..."
kubectl delete job "${JOB_NAME}" -n "${NAMESPACE}"

echo "PVC migration completed successfully."
echo "Source PVC: ${SOURCE_PVC}"
echo "Target PVC: ${TARGET_PVC}"
echo "You can now update your deployments to use the new PVC."
