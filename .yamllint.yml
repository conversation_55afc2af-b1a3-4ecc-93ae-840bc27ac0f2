extends: relaxed

rules:
  line-length:
    max: 150
    level: warning
  document-start: disable
  indentation:
    spaces: 2
    check-multi-line-strings: false
  comments-indentation: disable
  comments: disable
  truthy:
    allowed-values: ['true', 'false', 'yes', 'no', 'on', 'off']

ignore: |
  .git/
  node_modules/
  vendor/
  # Helm chart templates
  */chart/templates/
  */charts/*/templates/
  # Downloaded/vendor files
  */downloaded/
  **/.argocd-source-*.yaml
  # Test and example files
  */test/
  */examples/
  # SNMP configurations (vendor provided)
  **/snmp.yml
  # Files with known issues
  longhorn/values.yaml
  ser2net/base/ser2net-config.yaml
  # Mutating webhook (special formatting)
  mutating-webhook/
  # Files that are auto-generated or have special formats
  argocd/values.yaml
  wiki/kustomize/deployment.yaml
  acme-job/overlays/home/<USER>
  longhorn/test*.yaml
  network-policies/ns_mosquitto.yaml
  victoria-metrics/vmalerts-values.yaml
