# dnsmasq:
#   customDnsEntries:
#     - address=/nas/**************

#   customCnameEntries:
#     - cname=foo.nas,nas

# persistentVolumeClaim:
#   enabled: true

ingress:
  # -- Generate a Ingress resource
  enabled: false

  # -- Specify an ingressClassName
  # ingressClassName: nginx

  # -- Annotations for the ingress
  annotations:
    cert-manager.io/cluster-issuer: letsencrypt-cloudflare-sklenarovi
    # kubernetes.io/ingress.class: nginx
    # kubernetes.io/tls-acme: "true"
  path: /
  hosts:
    # virtualHost (default value is pi.hole) will be appended to the hosts
    - pihole.k8s.sklenarovi.cz
  tls:
    - secretName: pihole-k8s-sklenarovi-cz-tls
      hosts:
        # - virtualHost (default value is pi.hole) will be appended to the hosts
        - pihole.k8s.sklenarovi.cz

podAnnotations:
  prometheus.io/port: "9617"
  prometheus.io/scrape: "true"

podDisruptionBudget:
  # -- set to true to enable creating the PDB
  enabled: true
  minAvailable: 1

replicaCount: 2

dnsmasq:
#  customDnsEntries:
#    - address=/my.fancyurl.com/**************
  additionalHostsEntries:
    - *************    home05.local.sklenarovi.cz     home05

  customSettings:
    otherSettings: |
      except-interface=nonexisting

antiaff:
  # -- set to true to enable antiaffinity
  enabled: true
  avoidRelease: mojo2600

monitoring:
  # -- Sidecar configuration
  sidecar:
    # -- set this to true to enable podMonitor as sidecar
    enabled: true

nodeSelector:
  app.sklenarovi.cz: pihole

serviceDhcp:
  # -- Generate a Service resource for DHCP traffic
  enabled: false

podDnsConfig:
  enabled: true
  policy: "None"
  nameservers:
    - 127.0.0.1
    - ************

serviceWeb:
  loadBalancerIP: *************
  annotations:
    metallb.universe.tf/allow-shared-ip: pihole-svc
  type: LoadBalancer

serviceDns:
  loadBalancerIP: *************
  annotations:
    metallb.universe.tf/allow-shared-ip: pihole-svc
  type: LoadBalancer

adlists:
  - https://raw.githubusercontent.com/StevenBlack/hosts/master/hosts
  - https://v.firebog.net/hosts/AdguardDNS.txt
  - https://osint.digitalside.it/Threat-Intel/lists/latestdomains.txt
  - https://blocklistproject.github.io/Lists/ads.txt

whitelist:
  - www.googleadservices.com
  - crashlyticsreports-pa.googleapis.com
  - clickserve.dartsearch.net
  - ad.doubleclick.net
