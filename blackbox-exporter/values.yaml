nodeSelector:
  location: home

config:
  modules:
    http_2xx:
      prober: http
      timeout: 5s
      http:
        valid_http_versions: ["HTTP/1.1", "HTTP/2.0"]
        follow_redirects: true
        preferred_ip_protocol: "ip4"
    # check if https is enabled and returns 401 (unauthorized)
    https_40x:
      prober: http
      timeout: 5s
      http:
        method: GET
        fail_if_ssl: false
        fail_if_not_ssl: true
        valid_http_versions: ["HTTP/1.1", "HTTP/2.0"]
        valid_status_codes: [401, 403]
        follow_redirects: false
        preferred_ip_protocol: "ip4"

    # Check if https is enabled
    https_2xx:
      prober: http
      timeout: 5s
      http:
        method: GET
        fail_if_ssl: false
        fail_if_not_ssl: true
        valid_http_versions: ["HTTP/1.1", "HTTP/2.0"]
        follow_redirects: true
        preferred_ip_protocol: "ip4"
    # Check open port
    tcp_connect:
      prober: tcp
      timeout: 5s
    ping:
      prober: icmp
      timeout: 5s
      icmp:
        preferred_ip_protocol: "ip4"
    dns_udp_google:
      prober: dns
      timeout: 5s
      dns:
        query_name: "google.com"
        query_type: "A"
        preferred_ip_protocol: "ip4"
