config:
  # OAuth client ID
  # clientID: "56c9f354-75c3-4b20-921a-748867f3fbb1"
  clientID: "471880561917-9d98qf9jcjkp9i1uc716h656raamntc1.apps.googleusercontent.com"
  # OAuth client secret
  clientSecret: "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"
  # clientSecret: "****************************************"

  cookieSecret: "********************************************"

  # configFile: |-
  #   # pass_access_token = "true"
  #   # pass_authorization_header = "true"
  #   set_authorization_header = "true"
  #   set_xauthrequest = "true"

  configFile: |-
    email_domains = [ "*" ]
    upstreams = [ "file:///dev/null" ]
    reverse_proxy = true

extraArgs:
  provider: google
  # azure-tenant: "01f7e0e8-c680-4293-8068-d572231a88f4"
  # azure-tenant: "269a1b55-b8e0-4721-9d49-ae9f28544118"
  # oidc-issuer-url: "https://login.microsoftonline.com/269a1b55-b8e0-4721-9d49-ae9f28544118/v2.0"
  # oidc-issuer-url: "https://login.microsoftonline.com/01f7e0e8-c680-4293-8068-d572231a88f4/v2.0"
  whitelist-domain: .k8s.sklenarovi.cz,.login.microsoftonline.com,.home.sklenarovi.cz
  # scope: "openid email"
  # scope: "openid email profile"
  # oidc-email-claim: preferred_username
  # cookie-domain: k8s.sklenarovi.cz
  # cookie-name: "__Host-"
  # cookie-csrf-per-request: "true"
  # cookie-csrf-expire: 5m
  # proxy-prefix: /oauth2
  skip-provider-button: "false"
  # session-cookie-minimal: "true"
  # http-address: 0.0.0.0:4180
  # cookie-httponly: "false"
  scope: "openid email"
  # scope: "openid email profile"
  # oidc-email-claim: preferred_username
  cookie-domain: .sklenarovi.cz
  cookie-name: "oauth2_proxy_home"
  reverse-proxy: true
  # upstream: static://202
  # cookie-secure: "false"
  # silence-ping-logging: true
  # redirect-url: https://login.{{clusterName}}.esc.esetrs.cz/oauth2/callback
  # email-domain: "*"
  # skip-provider-button: "true"
  silence-ping-logging: true
  # redirect-url: https://login.{{clusterName}}.esc.esetrs.cz/oauth2/callback
  email-domain: "*"
  set-xauthrequest: "true"
  # session-cookie-minimal: "true"

ingress:
  enabled: true
  path: /
  hosts:
    - oauth.k8s.sklenarovi.cz
  annotations:
    cert-manager.io/cluster-issuer: letsencrypt-cloudflare-sklenarovi
  #   nginx.ingress.kubernetes.io/enable-cors: "true"
  tls:
    - secretName: oauth-k8s-sklenarovi-cz-tls
      hosts:
        - oauth.k8s.sklenarovi.cz

nodeSelector:
  location: oracle
