
# Script to enable ufw on contabo2

More info:

* https://docs.k3s.io/installation/requirements#inbound-rules-for-k3s-server-nodes
* https://blog.rtsp.us/ufw-uncomplicated-firewall-cheat-sheet-a9fe61933330
* https://docs.k3s.io/advanced

```bash
sudo apt -y install ufw
sudo systemctl enable ufw
sudo ufw allow from ************* to any port 22
# ufw allow from ***********/24 to any port 22
# ufw allow from ***********/24 80/tcp
# ufw allow from ***********/24 443/tcp
# https://docs.k3s.io/installation/requirements#inbound-rules-for-k3s-server-nodes  
sudo ufw allow from ***********/24
# ufw allow from ***********/24 6443/tcp #apiserver from nebula
# ufw allow from ***********/24 2379/tcp #Required only for HA with embedded etcd
# ufw allow from ***********/24 2380/tcp #Required only for HA with embedded etcd
sudo ufw allow from *********/16 to any #k3s pods
sudo ufw allow from *********/16 to any #k3s services
sudo ufw default reject incoming
sudo ufw default allow outgoing
sudo ufw default deny routed
sudo ufw show added
sudo ufw show listening
sudo ufw enable
sudo ufw status verbose

sudo ufw allow 4242/udp
```


# Setup nebula watcher   

```bash
#ExecStart=/usr/bin/bash /usr/local/bin/nebula-restart-watcher.sh --sleep 60 --restart-service k3s --watch-service nebula-start
sudo nano /etc/systemd/system/nebula-restart-watcher.service

sudo systemctl daemon-reload
sudo nano /usr/local/bin/nebula-restart-watcher.sh
sudo chmod +x /usr/local/bin/nebula-restart-watcher.sh
sudo systemctl enable nebula-restart-watcher
sudo systemctl start nebula-restart-watcher
sudo systemctl status nebula-restart-watcher
```
