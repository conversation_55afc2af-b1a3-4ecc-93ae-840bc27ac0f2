apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: snmp-exporter-mikrotik
  annotations:
    argocd-image-updater.argoproj.io/image-list: prom/snmp-exporter:^v0.21.0
    # argocd-image-updater.argoproj.io/write-back-method: git
    argocd-image-updater.argoproj.io/git-branch: main
  namespace: argocd
spec:
  destination:
    server: https://kubernetes.default.svc
    namespace: snmp-exporter-mikrotik
  project: default
  source:
    repoURL: '**************:pajikos/home-iaac.git'
    path: helm-chart-library/charts/other/app-template
    targetRevision: HEAD
    helm:
      valueFiles:
      - ../../../../snmp-exporter-mikrotik/values.yaml
  syncPolicy:
    syncOptions:
    - CreateNamespace=true
