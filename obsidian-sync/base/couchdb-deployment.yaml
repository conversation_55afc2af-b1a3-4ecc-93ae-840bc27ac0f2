apiVersion: apps/v1
kind: Deployment
metadata:
  name: couchdb
spec:
  replicas: 1
  selector:
    matchLabels:
      app: couchdb
  template:
    metadata:
      labels:
        app: couchdb
    spec:
      nodeSelector:
        location: oracle
      containers:
        - name: couchdb
          image: couchdb:3.3.3
          securityContext:
            runAsUser: 1000
            runAsGroup: 1000
          volumeMounts:
            - name: couchdb-config-volume
              mountPath: /opt/couchdb/etc/local.ini
              subPath: local.ini
            - name: couchdb-data-volume
              mountPath: /opt/couchdb/data
          env:
            - name: COUCHDB_USER
              value: "pajikos"
            - name: COUCHDB_PASSWORD
              value: "Ajnpd,mjmr.2015"
      volumes:
        - name: couchdb-config-volume
          configMap:
            name: couchdb-config
            items:
              - key: local.ini
                path: local.ini
        - name: couchdb-data-volume
          persistentVolumeClaim:
            claimName: couchdb-pvc
