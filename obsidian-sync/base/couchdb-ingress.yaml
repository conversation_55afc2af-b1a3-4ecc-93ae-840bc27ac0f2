apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: couchdb-ingress
  annotations:
    nginx.ingress.kubernetes.io/rewrite-target: /
    nginx.ingress.kubernetes.io/proxy-body-size: "100m"
spec:
  tls:
    - hosts:
        - obsidian.couchdb.k8s.sklenarovi.cz
      secretName: star-couchdb-k8s-sklenarovi-cz-tls
  rules:
    - host: "obsidian.couchdb.k8s.sklenarovi.cz"
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: couchdb
                port:
                  number: 5984
