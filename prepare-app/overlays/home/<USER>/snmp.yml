# WARNING: This file was auto-generated using snmp_exporter generator, manual changes will be lost.
synology:
  walk:
  - *******.2.1.2
  - *******.********
  - *******.********.1
  - *******.4.1.2021.10.1.2
  - *******.4.1.2021.10.1.5
  - *******.4.1.2021.4
  - *******.4.1.6574.1
  - *******.4.1.6574.101
  - *******.4.1.6574.102
  - *******.4.1.6574.104
  - *******.4.1.6574.2
  - *******.4.1.6574.3
  - *******.4.1.6574.4
  - *******.4.1.6574.5
  - *******.4.1.6574.6
  get:
  - *******.2.1.1.3.0
  - *******.4.1.2021.11.10.0
  - *******.4.1.2021.11.11.0
  - *******.4.1.2021.11.9.0
  metrics:
  - name: sysUpTime
    oid: *******.2.1.1.3
    type: gauge
    help: The time (in hundredths of a second) since the network management portion
      of the system was last re-initialized. - *******.2.1.1.3
  - name: ifNumber
    oid: *******.2.1.2.1
    type: gauge
    help: The number of network interfaces (regardless of their current state) present
      on this system. - *******.2.1.2.1
  - name: ifIndex
    oid: *******.*******.1.1
    type: gauge
    help: A unique value, greater than zero, for each interface - *******.*******.1.1
    indexes:
    - labelname: ifIndex
      type: gauge
    lookups:
    - labels:
      - ifIndex
      labelname: ifName
      oid: *******.********.1.1.1
      type: DisplayString
    - labels: []
      labelname: ifIndex
  - name: ifDescr
    oid: *******.*******.1.2
    type: DisplayString
    help: A textual string containing information about the interface - *******.*******.1.2
    indexes:
    - labelname: ifIndex
      type: gauge
    lookups:
    - labels:
      - ifIndex
      labelname: ifName
      oid: *******.********.1.1.1
      type: DisplayString
    - labels: []
      labelname: ifIndex
  - name: ifType
    oid: *******.*******.1.3
    type: EnumAsInfo
    help: The type of interface - *******.*******.1.3
    indexes:
    - labelname: ifIndex
      type: gauge
    lookups:
    - labels:
      - ifIndex
      labelname: ifName
      oid: *******.********.1.1.1
      type: DisplayString
    - labels: []
      labelname: ifIndex
    enum_values:
      1: other
      2: regular1822
      3: hdh1822
      4: ddnX25
      5: rfc877x25
      6: ethernetCsmacd
      7: iso88023Csmacd
      8: iso88024TokenBus
      9: iso88025TokenRing
      10: iso88026Man
      11: starLan
      12: proteon10Mbit
      13: proteon80Mbit
      14: hyperchannel
      15: fddi
      16: lapb
      17: sdlc
      18: ds1
      19: e1
      20: basicISDN
      21: primaryISDN
      22: propPointToPointSerial
      23: ppp
      24: softwareLoopback
      25: eon
      26: ethernet3Mbit
      27: nsip
      28: slip
      29: ultra
      30: ds3
      31: sip
      32: frameRelay
      33: rs232
      34: para
      35: arcnet
      36: arcnetPlus
      37: atm
      38: miox25
      39: sonet
      40: x25ple
      41: iso88022llc
      42: localTalk
      43: smdsDxi
      44: frameRelayService
      45: v35
      46: hssi
      47: hippi
      48: modem
      49: aal5
      50: sonetPath
      51: sonetVT
      52: smdsIcip
      53: propVirtual
      54: propMultiplexor
      55: ieee80212
      56: fibreChannel
      57: hippiInterface
      58: frameRelayInterconnect
      59: aflane8023
      60: aflane8025
      61: cctEmul
      62: fastEther
      63: isdn
      64: v11
      65: v36
      66: g703at64k
      67: g703at2mb
      68: qllc
      69: fastEtherFX
      70: channel
      71: ieee80211
      72: ibm370parChan
      73: escon
      74: dlsw
      75: isdns
      76: isdnu
      77: lapd
      78: ipSwitch
      79: rsrb
      80: atmLogical
      81: ds0
      82: ds0Bundle
      83: bsc
      84: async
      85: cnr
      86: iso88025Dtr
      87: eplrs
      88: arap
      89: propCnls
      90: hostPad
      91: termPad
      92: frameRelayMPI
      93: x213
      94: adsl
      95: radsl
      96: sdsl
      97: vdsl
      98: iso88025CRFPInt
      99: myrinet
      100: voiceEM
      101: voiceFXO
      102: voiceFXS
      103: voiceEncap
      104: voiceOverIp
      105: atmDxi
      106: atmFuni
      107: atmIma
      108: pppMultilinkBundle
      109: ipOverCdlc
      110: ipOverClaw
      111: stackToStack
      112: virtualIpAddress
      113: mpc
      114: ipOverAtm
      115: iso88025Fiber
      116: tdlc
      117: gigabitEthernet
      118: hdlc
      119: lapf
      120: v37
      121: x25mlp
      122: x25huntGroup
      123: transpHdlc
      124: interleave
      125: fast
      126: ip
      127: docsCableMaclayer
      128: docsCableDownstream
      129: docsCableUpstream
      130: a12MppSwitch
      131: tunnel
      132: coffee
      133: ces
      134: atmSubInterface
      135: l2vlan
      136: l3ipvlan
      137: l3ipxvlan
      138: digitalPowerline
      139: mediaMailOverIp
      140: dtm
      141: dcn
      142: ipForward
      143: msdsl
      144: ieee1394
      145: if-gsn
      146: dvbRccMacLayer
      147: dvbRccDownstream
      148: dvbRccUpstream
      149: atmVirtual
      150: mplsTunnel
      151: srp
      152: voiceOverAtm
      153: voiceOverFrameRelay
      154: idsl
      155: compositeLink
      156: ss7SigLink
      157: propWirelessP2P
      158: frForward
      159: rfc1483
      160: usb
      161: ieee8023adLag
      162: bgppolicyaccounting
      163: frf16MfrBundle
      164: h323Gatekeeper
      165: h323Proxy
      166: mpls
      167: mfSigLink
      168: hdsl2
      169: shdsl
      170: ds1FDL
      171: pos
      172: dvbAsiIn
      173: dvbAsiOut
      174: plc
      175: nfas
      176: tr008
      177: gr303RDT
      178: gr303IDT
      179: isup
      180: propDocsWirelessMaclayer
      181: propDocsWirelessDownstream
      182: propDocsWirelessUpstream
      183: hiperlan2
      184: propBWAp2Mp
      185: sonetOverheadChannel
      186: digitalWrapperOverheadChannel
      187: aal2
      188: radioMAC
      189: atmRadio
      190: imt
      191: mvl
      192: reachDSL
      193: frDlciEndPt
      194: atmVciEndPt
      195: opticalChannel
      196: opticalTransport
      197: propAtm
      198: voiceOverCable
      199: infiniband
      200: teLink
      201: q2931
      202: virtualTg
      203: sipTg
      204: sipSig
      205: docsCableUpstreamChannel
      206: econet
      207: pon155
      208: pon622
      209: bridge
      210: linegroup
      211: voiceEMFGD
      212: voiceFGDEANA
      213: voiceDID
      214: mpegTransport
      215: sixToFour
      216: gtp
      217: pdnEtherLoop1
      218: pdnEtherLoop2
      219: opticalChannelGroup
      220: homepna
      221: gfp
      222: ciscoISLvlan
      223: actelisMetaLOOP
      224: fcipLink
      225: rpr
      226: qam
      227: lmp
      228: cblVectaStar
      229: docsCableMCmtsDownstream
      230: adsl2
      231: macSecControlledIF
      232: macSecUncontrolledIF
      233: aviciOpticalEther
      234: atmbond
      235: voiceFGDOS
      236: mocaVersion1
      237: ieee80216WMAN
      238: adsl2plus
      239: dvbRcsMacLayer
      240: dvbTdm
      241: dvbRcsTdma
      242: x86Laps
      243: wwanPP
      244: wwanPP2
      245: voiceEBS
      246: ifPwType
      247: ilan
      248: pip
      249: aluELP
      250: gpon
      251: vdsl2
      252: capwapDot11Profile
      253: capwapDot11Bss
      254: capwapWtpVirtualRadio
      255: bits
      256: docsCableUpstreamRfPort
      257: cableDownstreamRfPort
      258: vmwareVirtualNic
      259: ieee802154
      260: otnOdu
      261: otnOtu
      262: ifVfiType
      263: g9981
      264: g9982
      265: g9983
      266: aluEpon
      267: aluEponOnu
      268: aluEponPhysicalUni
      269: aluEponLogicalLink
      270: aluGponOnu
      271: aluGponPhysicalUni
      272: vmwareNicTeam
      277: docsOfdmDownstream
      278: docsOfdmaUpstream
      279: gfast
      280: sdci
      281: xboxWireless
      282: fastdsl
      283: docsCableScte55d1FwdOob
      284: docsCableScte55d1RetOob
      285: docsCableScte55d2DsOob
      286: docsCableScte55d2UsOob
      287: docsCableNdf
      288: docsCableNdr
      289: ptm
      290: ghn
      291: otnOtsi
      292: otnOtuc
      293: otnOduc
      294: otnOtsig
      295: microwaveCarrierTermination
      296: microwaveRadioLinkTerminal
      297: ieee8021axDrni
      298: ax25
      299: ieee19061nanocom
      300: cpri
      301: omni
      302: roe
      303: p2pOverLan
  - name: ifMtu
    oid: *******.*******.1.4
    type: gauge
    help: The size of the largest packet which can be sent/received on the interface,
      specified in octets - *******.*******.1.4
    indexes:
    - labelname: ifIndex
      type: gauge
    lookups:
    - labels:
      - ifIndex
      labelname: ifName
      oid: *******.********.1.1.1
      type: DisplayString
    - labels: []
      labelname: ifIndex
  - name: ifSpeed
    oid: *******.*******.1.5
    type: gauge
    help: An estimate of the interface's current bandwidth in bits per second - *******.*******.1.5
    indexes:
    - labelname: ifIndex
      type: gauge
    lookups:
    - labels:
      - ifIndex
      labelname: ifName
      oid: *******.********.1.1.1
      type: DisplayString
    - labels: []
      labelname: ifIndex
  - name: ifPhysAddress
    oid: *******.*******.1.6
    type: PhysAddress48
    help: The interface's address at its protocol sub-layer - *******.*******.1.6
    indexes:
    - labelname: ifIndex
      type: gauge
    lookups:
    - labels:
      - ifIndex
      labelname: ifName
      oid: *******.********.1.1.1
      type: DisplayString
    - labels: []
      labelname: ifIndex
  - name: ifAdminStatus
    oid: *******.*******.1.7
    type: gauge
    help: The desired state of the interface - *******.*******.1.7
    indexes:
    - labelname: ifIndex
      type: gauge
    lookups:
    - labels:
      - ifIndex
      labelname: ifName
      oid: *******.********.1.1.1
      type: DisplayString
    - labels: []
      labelname: ifIndex
    enum_values:
      1: up
      2: down
      3: testing
  - name: ifOperStatus
    oid: *******.*******.1.8
    type: gauge
    help: The current operational state of the interface - *******.*******.1.8
    indexes:
    - labelname: ifIndex
      type: gauge
    lookups:
    - labels:
      - ifIndex
      labelname: ifName
      oid: *******.********.1.1.1
      type: DisplayString
    - labels: []
      labelname: ifIndex
    enum_values:
      1: up
      2: down
      3: testing
      4: unknown
      5: dormant
      6: notPresent
      7: lowerLayerDown
  - name: ifLastChange
    oid: *******.*******.1.9
    type: gauge
    help: The value of sysUpTime at the time the interface entered its current operational
      state - *******.*******.1.9
    indexes:
    - labelname: ifIndex
      type: gauge
    lookups:
    - labels:
      - ifIndex
      labelname: ifName
      oid: *******.********.1.1.1
      type: DisplayString
    - labels: []
      labelname: ifIndex
  - name: ifInOctets
    oid: *******.*******.1.10
    type: counter
    help: The total number of octets received on the interface, including framing
      characters - *******.*******.1.10
    indexes:
    - labelname: ifIndex
      type: gauge
    lookups:
    - labels:
      - ifIndex
      labelname: ifName
      oid: *******.********.1.1.1
      type: DisplayString
    - labels: []
      labelname: ifIndex
  - name: ifInUcastPkts
    oid: *******.*******.1.11
    type: counter
    help: The number of packets, delivered by this sub-layer to a higher (sub-)layer,
      which were not addressed to a multicast or broadcast address at this sub-layer
      - *******.*******.1.11
    indexes:
    - labelname: ifIndex
      type: gauge
    lookups:
    - labels:
      - ifIndex
      labelname: ifName
      oid: *******.********.1.1.1
      type: DisplayString
    - labels: []
      labelname: ifIndex
  - name: ifInNUcastPkts
    oid: *******.*******.1.12
    type: counter
    help: The number of packets, delivered by this sub-layer to a higher (sub-)layer,
      which were addressed to a multicast or broadcast address at this sub-layer -
      *******.*******.1.12
    indexes:
    - labelname: ifIndex
      type: gauge
    lookups:
    - labels:
      - ifIndex
      labelname: ifName
      oid: *******.********.1.1.1
      type: DisplayString
    - labels: []
      labelname: ifIndex
  - name: ifInDiscards
    oid: *******.*******.1.13
    type: counter
    help: The number of inbound packets which were chosen to be discarded even though
      no errors had been detected to prevent their being deliverable to a higher-layer
      protocol - *******.*******.1.13
    indexes:
    - labelname: ifIndex
      type: gauge
    lookups:
    - labels:
      - ifIndex
      labelname: ifName
      oid: *******.********.1.1.1
      type: DisplayString
    - labels: []
      labelname: ifIndex
  - name: ifInErrors
    oid: *******.*******.1.14
    type: counter
    help: For packet-oriented interfaces, the number of inbound packets that contained
      errors preventing them from being deliverable to a higher-layer protocol - *******.*******.1.14
    indexes:
    - labelname: ifIndex
      type: gauge
    lookups:
    - labels:
      - ifIndex
      labelname: ifName
      oid: *******.********.1.1.1
      type: DisplayString
    - labels: []
      labelname: ifIndex
  - name: ifInUnknownProtos
    oid: *******.*******.1.15
    type: counter
    help: For packet-oriented interfaces, the number of packets received via the interface
      which were discarded because of an unknown or unsupported protocol - *******.*******.1.15
    indexes:
    - labelname: ifIndex
      type: gauge
    lookups:
    - labels:
      - ifIndex
      labelname: ifName
      oid: *******.********.1.1.1
      type: DisplayString
    - labels: []
      labelname: ifIndex
  - name: ifOutOctets
    oid: *******.*******.1.16
    type: counter
    help: The total number of octets transmitted out of the interface, including framing
      characters - *******.*******.1.16
    indexes:
    - labelname: ifIndex
      type: gauge
    lookups:
    - labels:
      - ifIndex
      labelname: ifName
      oid: *******.********.1.1.1
      type: DisplayString
    - labels: []
      labelname: ifIndex
  - name: ifOutUcastPkts
    oid: *******.*******.1.17
    type: counter
    help: The total number of packets that higher-level protocols requested be transmitted,
      and which were not addressed to a multicast or broadcast address at this sub-layer,
      including those that were discarded or not sent - *******.*******.1.17
    indexes:
    - labelname: ifIndex
      type: gauge
    lookups:
    - labels:
      - ifIndex
      labelname: ifName
      oid: *******.********.1.1.1
      type: DisplayString
    - labels: []
      labelname: ifIndex
  - name: ifOutNUcastPkts
    oid: *******.*******.1.18
    type: counter
    help: The total number of packets that higher-level protocols requested be transmitted,
      and which were addressed to a multicast or broadcast address at this sub-layer,
      including those that were discarded or not sent - *******.*******.1.18
    indexes:
    - labelname: ifIndex
      type: gauge
    lookups:
    - labels:
      - ifIndex
      labelname: ifName
      oid: *******.********.1.1.1
      type: DisplayString
    - labels: []
      labelname: ifIndex
  - name: ifOutDiscards
    oid: *******.*******.1.19
    type: counter
    help: The number of outbound packets which were chosen to be discarded even though
      no errors had been detected to prevent their being transmitted - *******.*******.1.19
    indexes:
    - labelname: ifIndex
      type: gauge
    lookups:
    - labels:
      - ifIndex
      labelname: ifName
      oid: *******.********.1.1.1
      type: DisplayString
    - labels: []
      labelname: ifIndex
  - name: ifOutErrors
    oid: *******.*******.1.20
    type: counter
    help: For packet-oriented interfaces, the number of outbound packets that could
      not be transmitted because of errors - *******.*******.1.20
    indexes:
    - labelname: ifIndex
      type: gauge
    lookups:
    - labels:
      - ifIndex
      labelname: ifName
      oid: *******.********.1.1.1
      type: DisplayString
    - labels: []
      labelname: ifIndex
  - name: ifOutQLen
    oid: *******.*******.1.21
    type: gauge
    help: The length of the output packet queue (in packets). - *******.*******.1.21
    indexes:
    - labelname: ifIndex
      type: gauge
    lookups:
    - labels:
      - ifIndex
      labelname: ifName
      oid: *******.********.1.1.1
      type: DisplayString
    - labels: []
      labelname: ifIndex
  - name: ifSpecific
    oid: *******.*******.1.22
    type: OctetString
    help: A reference to MIB definitions specific to the particular media being used
      to realize the interface - *******.*******.1.22
    indexes:
    - labelname: ifIndex
      type: gauge
    lookups:
    - labels:
      - ifIndex
      labelname: ifName
      oid: *******.********.1.1.1
      type: DisplayString
    - labels: []
      labelname: ifIndex
  - name: hrMemorySize
    oid: *******.********.2
    type: gauge
    help: The amount of physical read-write main memory, typically RAM, contained
      by the host. - *******.********.2
  - name: hrStorageIndex
    oid: *******.********.3.1.1
    type: gauge
    help: A unique value for each logical storage area contained by the host. - *******.********.3.1.1
    indexes:
    - labelname: hrStorageIndex
      type: gauge
    lookups:
    - labels:
      - hrStorageIndex
      labelname: hrStorageDescr
      oid: *******.********.3.1.3
      type: DisplayString
    - labels: []
      labelname: hrStorageIndex
  - name: hrStorageType
    oid: *******.********.3.1.2
    type: OctetString
    help: The type of storage represented by this entry. - *******.********.3.1.2
    indexes:
    - labelname: hrStorageIndex
      type: gauge
    lookups:
    - labels:
      - hrStorageIndex
      labelname: hrStorageDescr
      oid: *******.********.3.1.3
      type: DisplayString
    - labels: []
      labelname: hrStorageIndex
  - name: hrStorageDescr
    oid: *******.********.3.1.3
    type: DisplayString
    help: A description of the type and instance of the storage described by this
      entry. - *******.********.3.1.3
    indexes:
    - labelname: hrStorageIndex
      type: gauge
    lookups:
    - labels:
      - hrStorageIndex
      labelname: hrStorageDescr
      oid: *******.********.3.1.3
      type: DisplayString
    - labels: []
      labelname: hrStorageIndex
  - name: hrStorageAllocationUnits
    oid: *******.********.3.1.4
    type: gauge
    help: The size, in bytes, of the data objects allocated from this pool - *******.********.3.1.4
    indexes:
    - labelname: hrStorageIndex
      type: gauge
    lookups:
    - labels:
      - hrStorageIndex
      labelname: hrStorageDescr
      oid: *******.********.3.1.3
      type: DisplayString
    - labels: []
      labelname: hrStorageIndex
  - name: hrStorageSize
    oid: *******.********.3.1.5
    type: gauge
    help: The size of the storage represented by this entry, in units of hrStorageAllocationUnits
      - *******.********.3.1.5
    indexes:
    - labelname: hrStorageIndex
      type: gauge
    lookups:
    - labels:
      - hrStorageIndex
      labelname: hrStorageDescr
      oid: *******.********.3.1.3
      type: DisplayString
    - labels: []
      labelname: hrStorageIndex
  - name: hrStorageUsed
    oid: *******.********.3.1.6
    type: gauge
    help: The amount of the storage represented by this entry that is allocated, in
      units of hrStorageAllocationUnits. - *******.********.3.1.6
    indexes:
    - labelname: hrStorageIndex
      type: gauge
    lookups:
    - labels:
      - hrStorageIndex
      labelname: hrStorageDescr
      oid: *******.********.3.1.3
      type: DisplayString
    - labels: []
      labelname: hrStorageIndex
  - name: hrStorageAllocationFailures
    oid: *******.********.3.1.7
    type: counter
    help: The number of requests for storage represented by this entry that could
      not be honored due to not enough storage - *******.********.3.1.7
    indexes:
    - labelname: hrStorageIndex
      type: gauge
    lookups:
    - labels:
      - hrStorageIndex
      labelname: hrStorageDescr
      oid: *******.********.3.1.3
      type: DisplayString
    - labels: []
      labelname: hrStorageIndex
  - name: ifName
    oid: *******.********.1.1.1
    type: DisplayString
    help: The textual name of the interface - *******.********.1.1.1
    indexes:
    - labelname: ifIndex
      type: gauge
    lookups:
    - labels:
      - ifIndex
      labelname: ifName
      oid: *******.********.1.1.1
      type: DisplayString
    - labels: []
      labelname: ifIndex
  - name: ifInMulticastPkts
    oid: *******.********.1.1.2
    type: counter
    help: The number of packets, delivered by this sub-layer to a higher (sub-)layer,
      which were addressed to a multicast address at this sub-layer - *******.********.1.1.2
    indexes:
    - labelname: ifIndex
      type: gauge
    lookups:
    - labels:
      - ifIndex
      labelname: ifName
      oid: *******.********.1.1.1
      type: DisplayString
    - labels: []
      labelname: ifIndex
  - name: ifInBroadcastPkts
    oid: *******.********.1.1.3
    type: counter
    help: The number of packets, delivered by this sub-layer to a higher (sub-)layer,
      which were addressed to a broadcast address at this sub-layer - *******.********.1.1.3
    indexes:
    - labelname: ifIndex
      type: gauge
    lookups:
    - labels:
      - ifIndex
      labelname: ifName
      oid: *******.********.1.1.1
      type: DisplayString
    - labels: []
      labelname: ifIndex
  - name: ifOutMulticastPkts
    oid: *******.********.1.1.4
    type: counter
    help: The total number of packets that higher-level protocols requested be transmitted,
      and which were addressed to a multicast address at this sub-layer, including
      those that were discarded or not sent - *******.********.1.1.4
    indexes:
    - labelname: ifIndex
      type: gauge
    lookups:
    - labels:
      - ifIndex
      labelname: ifName
      oid: *******.********.1.1.1
      type: DisplayString
    - labels: []
      labelname: ifIndex
  - name: ifOutBroadcastPkts
    oid: *******.********.1.1.5
    type: counter
    help: The total number of packets that higher-level protocols requested be transmitted,
      and which were addressed to a broadcast address at this sub-layer, including
      those that were discarded or not sent - *******.********.1.1.5
    indexes:
    - labelname: ifIndex
      type: gauge
    lookups:
    - labels:
      - ifIndex
      labelname: ifName
      oid: *******.********.1.1.1
      type: DisplayString
    - labels: []
      labelname: ifIndex
  - name: ifHCInOctets
    oid: *******.********.1.1.6
    type: counter
    help: The total number of octets received on the interface, including framing
      characters - *******.********.1.1.6
    indexes:
    - labelname: ifIndex
      type: gauge
    lookups:
    - labels:
      - ifIndex
      labelname: ifName
      oid: *******.********.1.1.1
      type: DisplayString
    - labels: []
      labelname: ifIndex
  - name: ifHCInUcastPkts
    oid: *******.********.1.1.7
    type: counter
    help: The number of packets, delivered by this sub-layer to a higher (sub-)layer,
      which were not addressed to a multicast or broadcast address at this sub-layer
      - *******.********.1.1.7
    indexes:
    - labelname: ifIndex
      type: gauge
    lookups:
    - labels:
      - ifIndex
      labelname: ifName
      oid: *******.********.1.1.1
      type: DisplayString
    - labels: []
      labelname: ifIndex
  - name: ifHCInMulticastPkts
    oid: *******.********.1.1.8
    type: counter
    help: The number of packets, delivered by this sub-layer to a higher (sub-)layer,
      which were addressed to a multicast address at this sub-layer - *******.********.1.1.8
    indexes:
    - labelname: ifIndex
      type: gauge
    lookups:
    - labels:
      - ifIndex
      labelname: ifName
      oid: *******.********.1.1.1
      type: DisplayString
    - labels: []
      labelname: ifIndex
  - name: ifHCInBroadcastPkts
    oid: *******.********.1.1.9
    type: counter
    help: The number of packets, delivered by this sub-layer to a higher (sub-)layer,
      which were addressed to a broadcast address at this sub-layer - *******.********.1.1.9
    indexes:
    - labelname: ifIndex
      type: gauge
    lookups:
    - labels:
      - ifIndex
      labelname: ifName
      oid: *******.********.1.1.1
      type: DisplayString
    - labels: []
      labelname: ifIndex
  - name: ifHCOutOctets
    oid: *******.********.1.1.10
    type: counter
    help: The total number of octets transmitted out of the interface, including framing
      characters - *******.********.1.1.10
    indexes:
    - labelname: ifIndex
      type: gauge
    lookups:
    - labels:
      - ifIndex
      labelname: ifName
      oid: *******.********.1.1.1
      type: DisplayString
    - labels: []
      labelname: ifIndex
  - name: ifHCOutUcastPkts
    oid: *******.********.1.1.11
    type: counter
    help: The total number of packets that higher-level protocols requested be transmitted,
      and which were not addressed to a multicast or broadcast address at this sub-layer,
      including those that were discarded or not sent - *******.********.1.1.11
    indexes:
    - labelname: ifIndex
      type: gauge
    lookups:
    - labels:
      - ifIndex
      labelname: ifName
      oid: *******.********.1.1.1
      type: DisplayString
    - labels: []
      labelname: ifIndex
  - name: ifHCOutMulticastPkts
    oid: *******.********.1.1.12
    type: counter
    help: The total number of packets that higher-level protocols requested be transmitted,
      and which were addressed to a multicast address at this sub-layer, including
      those that were discarded or not sent - *******.********.1.1.12
    indexes:
    - labelname: ifIndex
      type: gauge
    lookups:
    - labels:
      - ifIndex
      labelname: ifName
      oid: *******.********.1.1.1
      type: DisplayString
    - labels: []
      labelname: ifIndex
  - name: ifHCOutBroadcastPkts
    oid: *******.********.1.1.13
    type: counter
    help: The total number of packets that higher-level protocols requested be transmitted,
      and which were addressed to a broadcast address at this sub-layer, including
      those that were discarded or not sent - *******.********.1.1.13
    indexes:
    - labelname: ifIndex
      type: gauge
    lookups:
    - labels:
      - ifIndex
      labelname: ifName
      oid: *******.********.1.1.1
      type: DisplayString
    - labels: []
      labelname: ifIndex
  - name: ifLinkUpDownTrapEnable
    oid: *******.********.1.1.14
    type: gauge
    help: Indicates whether linkUp/linkDown traps should be generated for this interface
      - *******.********.1.1.14
    indexes:
    - labelname: ifIndex
      type: gauge
    lookups:
    - labels:
      - ifIndex
      labelname: ifName
      oid: *******.********.1.1.1
      type: DisplayString
    - labels: []
      labelname: ifIndex
    enum_values:
      1: enabled
      2: disabled
  - name: ifHighSpeed
    oid: *******.********.1.1.15
    type: gauge
    help: An estimate of the interface's current bandwidth in units of 1,000,000 bits
      per second - *******.********.1.1.15
    indexes:
    - labelname: ifIndex
      type: gauge
    lookups:
    - labels:
      - ifIndex
      labelname: ifName
      oid: *******.********.1.1.1
      type: DisplayString
    - labels: []
      labelname: ifIndex
  - name: ifPromiscuousMode
    oid: *******.********.1.1.16
    type: gauge
    help: This object has a value of false(2) if this interface only accepts packets/frames
      that are addressed to this station - *******.********.1.1.16
    indexes:
    - labelname: ifIndex
      type: gauge
    lookups:
    - labels:
      - ifIndex
      labelname: ifName
      oid: *******.********.1.1.1
      type: DisplayString
    - labels: []
      labelname: ifIndex
    enum_values:
      1: "true"
      2: "false"
  - name: ifConnectorPresent
    oid: *******.********.1.1.17
    type: gauge
    help: This object has the value 'true(1)' if the interface sublayer has a physical
      connector and the value 'false(2)' otherwise. - *******.********.1.1.17
    indexes:
    - labelname: ifIndex
      type: gauge
    lookups:
    - labels:
      - ifIndex
      labelname: ifName
      oid: *******.********.1.1.1
      type: DisplayString
    - labels: []
      labelname: ifIndex
    enum_values:
      1: "true"
      2: "false"
  - name: ifAlias
    oid: *******.********.1.1.18
    type: DisplayString
    help: This object is an 'alias' name for the interface as specified by a network
      manager, and provides a non-volatile 'handle' for the interface - *******.********.1.1.18
    indexes:
    - labelname: ifIndex
      type: gauge
    lookups:
    - labels:
      - ifIndex
      labelname: ifName
      oid: *******.********.1.1.1
      type: DisplayString
    - labels: []
      labelname: ifIndex
  - name: ifCounterDiscontinuityTime
    oid: *******.********.1.1.19
    type: gauge
    help: The value of sysUpTime on the most recent occasion at which any one or more
      of this interface's counters suffered a discontinuity - *******.********.1.1.19
    indexes:
    - labelname: ifIndex
      type: gauge
    lookups:
    - labels:
      - ifIndex
      labelname: ifName
      oid: *******.********.1.1.1
      type: DisplayString
    - labels: []
      labelname: ifIndex
  - name: laNames
    oid: *******.4.1.2021.10.1.2
    type: DisplayString
    help: The list of loadave names we're watching. - *******.4.1.2021.10.1.2
    indexes:
    - labelname: laIndex
      type: gauge
    lookups:
    - labels:
      - laIndex
      labelname: laNames
      oid: *******.4.1.2021.10.1.2
      type: DisplayString
    - labels: []
      labelname: laIndex
  - name: laLoadInt
    oid: *******.4.1.2021.10.1.5
    type: gauge
    help: The 1,5 and 15 minute load averages as an integer - *******.4.1.2021.10.1.5
    indexes:
    - labelname: laIndex
      type: gauge
    lookups:
    - labels:
      - laIndex
      labelname: laNames
      oid: *******.4.1.2021.10.1.2
      type: DisplayString
    - labels: []
      labelname: laIndex
  - name: ssCpuSystem
    oid: *******.4.1.2021.11.10
    type: gauge
    help: The percentage of CPU time spent processing system-level code, calculated
      over the last minute - *******.4.1.2021.11.10
  - name: ssCpuIdle
    oid: *******.4.1.2021.11.11
    type: gauge
    help: The percentage of processor time spent idle, calculated over the last minute
      - *******.4.1.2021.11.11
  - name: ssCpuUser
    oid: *******.4.1.2021.11.9
    type: gauge
    help: The percentage of CPU time spent processing user-level code, calculated
      over the last minute - *******.4.1.2021.11.9
  - name: memIndex
    oid: *******.4.1.2021.4.1
    type: gauge
    help: Bogus Index - *******.4.1.2021.4.1
  - name: memErrorName
    oid: *******.4.1.2021.4.2
    type: DisplayString
    help: Bogus Name - *******.4.1.2021.4.2
  - name: memTotalSwap
    oid: *******.4.1.2021.4.3
    type: gauge
    help: The total amount of swap space configured for this host. - *******.4.1.2021.4.3
  - name: memAvailSwap
    oid: *******.4.1.2021.4.4
    type: gauge
    help: The amount of swap space currently unused or available. - *******.4.1.2021.4.4
  - name: memTotalReal
    oid: *******.4.1.2021.4.5
    type: gauge
    help: The total amount of real/physical memory installed on this host. - *******.4.1.2021.4.5
  - name: memAvailReal
    oid: *******.4.1.2021.4.6
    type: gauge
    help: The amount of real/physical memory currently unused or available. - *******.4.1.2021.4.6
  - name: memTotalSwapTXT
    oid: *******.4.1.2021.4.7
    type: gauge
    help: The total amount of swap space or virtual memory allocated for text pages
      on this host - *******.4.1.2021.4.7
  - name: memAvailSwapTXT
    oid: *******.4.1.2021.4.8
    type: gauge
    help: The amount of swap space or virtual memory currently being used by text
      pages on this host - *******.4.1.2021.4.8
  - name: memTotalRealTXT
    oid: *******.4.1.2021.4.9
    type: gauge
    help: The total amount of real/physical memory allocated for text pages on this
      host - *******.4.1.2021.4.9
  - name: memAvailRealTXT
    oid: *******.4.1.2021.4.10
    type: gauge
    help: The amount of real/physical memory currently being used by text pages on
      this host - *******.4.1.2021.4.10
  - name: memTotalFree
    oid: *******.4.1.2021.4.11
    type: gauge
    help: The total amount of memory free or available for use on this host - *******.4.1.2021.4.11
  - name: memMinimumSwap
    oid: *******.4.1.2021.4.12
    type: gauge
    help: The minimum amount of swap space expected to be kept free or available during
      normal operation of this host - *******.4.1.2021.4.12
  - name: memShared
    oid: *******.4.1.2021.4.13
    type: gauge
    help: The total amount of real or virtual memory currently allocated for use as
      shared memory - *******.4.1.2021.4.13
  - name: memBuffer
    oid: *******.4.1.2021.4.14
    type: gauge
    help: The total amount of real or virtual memory currently allocated for use as
      memory buffers - *******.4.1.2021.4.14
  - name: memCached
    oid: *******.4.1.2021.4.15
    type: gauge
    help: The total amount of real or virtual memory currently allocated for use as
      cached memory - *******.4.1.2021.4.15
  - name: memUsedSwapTXT
    oid: *******.4.1.2021.4.16
    type: gauge
    help: The amount of swap space or virtual memory currently being used by text
      pages on this host - *******.4.1.2021.4.16
  - name: memUsedRealTXT
    oid: *******.4.1.2021.4.17
    type: gauge
    help: The amount of real/physical memory currently being used by text pages on
      this host - *******.4.1.2021.4.17
  - name: memTotalSwapX
    oid: *******.4.1.2021.4.18
    type: counter
    help: The total amount of swap space configured for this host. - *******.4.1.2021.4.18
  - name: memAvailSwapX
    oid: *******.4.1.2021.4.19
    type: counter
    help: The amount of swap space currently unused or available. - *******.4.1.2021.4.19
  - name: memTotalRealX
    oid: *******.4.1.2021.4.20
    type: counter
    help: The total amount of real/physical memory installed on this host. - *******.4.1.2021.4.20
  - name: memAvailRealX
    oid: *******.4.1.2021.4.21
    type: counter
    help: The amount of real/physical memory currently unused or available. - *******.4.1.2021.4.21
  - name: memTotalFreeX
    oid: *******.4.1.2021.4.22
    type: counter
    help: The total amount of memory free or available for use on this host - *******.4.1.2021.4.22
  - name: memMinimumSwapX
    oid: *******.4.1.2021.4.23
    type: counter
    help: The minimum amount of swap space expected to be kept free or available during
      normal operation of this host - *******.4.1.2021.4.23
  - name: memSharedX
    oid: *******.4.1.2021.4.24
    type: counter
    help: The total amount of real or virtual memory currently allocated for use as
      shared memory - *******.4.1.2021.4.24
  - name: memBufferX
    oid: *******.4.1.2021.4.25
    type: counter
    help: The total amount of real or virtual memory currently allocated for use as
      memory buffers - *******.4.1.2021.4.25
  - name: memCachedX
    oid: *******.4.1.2021.4.26
    type: counter
    help: The total amount of real or virtual memory currently allocated for use as
      cached memory - *******.4.1.2021.4.26
  - name: memSwapError
    oid: *******.4.1.2021.4.100
    type: gauge
    help: Indicates whether the amount of available swap space (as reported by 'memAvailSwap(4)'),
      is less than the desired minimum (specified by 'memMinimumSwap(12)'). - *******.4.1.2021.4.100
    enum_values:
      0: noError
      1: error
  - name: memSwapErrorMsg
    oid: *******.4.1.2021.4.101
    type: DisplayString
    help: Describes whether the amount of available swap space (as reported by 'memAvailSwap(4)'),
      is less than the desired minimum (specified by 'memMinimumSwap(12)'). - *******.4.1.2021.4.101
  - name: systemStatus
    oid: *******.4.1.6574.1.1
    type: gauge
    help: Synology system status Each meanings of status represented describe below
      - *******.4.1.6574.1.1
  - name: temperature
    oid: *******.4.1.6574.1.2
    type: gauge
    help: Synology system temperature The temperature of Disk Station uses Celsius
      degree. - *******.4.1.6574.1.2
  - name: powerStatus
    oid: *******.4.1.6574.1.3
    type: gauge
    help: Synology power status Each meanings of status represented describe below
      - *******.4.1.6574.1.3
  - name: systemFanStatus
    oid: *******.4.1.6574.1.4.1
    type: gauge
    help: Synology system fan status Each meanings of status represented describe
      below - *******.4.1.6574.1.4.1
  - name: cpuFanStatus
    oid: *******.4.1.6574.1.4.2
    type: gauge
    help: Synology cpu fan status Each meanings of status represented describe below
      - *******.4.1.6574.1.4.2
  - name: modelName
    oid: *******.4.1.6574.1.5.1
    type: DisplayString
    help: The Model name of this NAS - *******.4.1.6574.1.5.1
  - name: serialNumber
    oid: *******.4.1.6574.1.5.2
    type: OctetString
    help: The serial number of this NAS - *******.4.1.6574.1.5.2
  - name: version
    oid: *******.4.1.6574.1.5.3
    type: OctetString
    help: The version of this DSM - *******.4.1.6574.1.5.3
  - name: upgradeAvailable
    oid: *******.4.1.6574.1.5.4
    type: gauge
    help: This oid is for checking whether there is a latest DSM can be upgraded -
      *******.4.1.6574.1.5.4
  - name: controllerNumber
    oid: *******.4.1.6574.1.6
    type: gauge
    help: Synology system controller number Controller A(0) Controller B(1) - *******.4.1.6574.1.6
  - name: storageIOIndex
    oid: *******.4.1.6574.101.1.1.1
    type: gauge
    help: Reference index for each observed device. - *******.4.1.6574.101.1.1.1
    indexes:
    - labelname: storageIOIndex
      type: gauge
    lookups:
    - labels:
      - storageIOIndex
      labelname: storageIODevice
      oid: *******.4.1.6574.101.1.1.2
      type: DisplayString
    - labels: []
      labelname: storageIOIndex
  - name: storageIODevice
    oid: *******.4.1.6574.101.1.1.2
    type: DisplayString
    help: The name of the device we are counting/checking. - *******.4.1.6574.101.1.1.2
    indexes:
    - labelname: storageIOIndex
      type: gauge
    lookups:
    - labels:
      - storageIOIndex
      labelname: storageIODevice
      oid: *******.4.1.6574.101.1.1.2
      type: DisplayString
    - labels: []
      labelname: storageIOIndex
  - name: storageIONRead
    oid: *******.4.1.6574.101.1.1.3
    type: counter
    help: The number of bytes read from this device since boot. - *******.4.1.6574.101.1.1.3
    indexes:
    - labelname: storageIOIndex
      type: gauge
    lookups:
    - labels:
      - storageIOIndex
      labelname: storageIODevice
      oid: *******.4.1.6574.101.1.1.2
      type: DisplayString
    - labels: []
      labelname: storageIOIndex
  - name: storageIONWritten
    oid: *******.4.1.6574.101.1.1.4
    type: counter
    help: The number of bytes written to this device since boot. - *******.4.1.6574.101.1.1.4
    indexes:
    - labelname: storageIOIndex
      type: gauge
    lookups:
    - labels:
      - storageIOIndex
      labelname: storageIODevice
      oid: *******.4.1.6574.101.1.1.2
      type: DisplayString
    - labels: []
      labelname: storageIOIndex
  - name: storageIOReads
    oid: *******.4.1.6574.101.1.1.5
    type: counter
    help: The number of read accesses from this device since boot. - *******.4.1.6574.101.1.1.5
    indexes:
    - labelname: storageIOIndex
      type: gauge
    lookups:
    - labels:
      - storageIOIndex
      labelname: storageIODevice
      oid: *******.4.1.6574.101.1.1.2
      type: DisplayString
    - labels: []
      labelname: storageIOIndex
  - name: storageIOWrites
    oid: *******.4.1.6574.101.1.1.6
    type: counter
    help: The number of write accesses to this device since boot. - *******.4.1.6574.101.1.1.6
    indexes:
    - labelname: storageIOIndex
      type: gauge
    lookups:
    - labels:
      - storageIOIndex
      labelname: storageIODevice
      oid: *******.4.1.6574.101.1.1.2
      type: DisplayString
    - labels: []
      labelname: storageIOIndex
  - name: storageIOLA
    oid: *******.4.1.6574.101.1.1.8
    type: gauge
    help: The load of disk (%) - *******.4.1.6574.101.1.1.8
    indexes:
    - labelname: storageIOIndex
      type: gauge
    lookups:
    - labels:
      - storageIOIndex
      labelname: storageIODevice
      oid: *******.4.1.6574.101.1.1.2
      type: DisplayString
    - labels: []
      labelname: storageIOIndex
  - name: storageIOLA1
    oid: *******.4.1.6574.101.1.1.9
    type: gauge
    help: The 1 minute average load of disk (%) - *******.4.1.6574.101.1.1.9
    indexes:
    - labelname: storageIOIndex
      type: gauge
    lookups:
    - labels:
      - storageIOIndex
      labelname: storageIODevice
      oid: *******.4.1.6574.101.1.1.2
      type: DisplayString
    - labels: []
      labelname: storageIOIndex
  - name: storageIOLA5
    oid: *******.4.1.6574.101.1.1.10
    type: gauge
    help: The 5 minute average load of disk (%) - *******.4.1.6574.101.1.1.10
    indexes:
    - labelname: storageIOIndex
      type: gauge
    lookups:
    - labels:
      - storageIOIndex
      labelname: storageIODevice
      oid: *******.4.1.6574.101.1.1.2
      type: DisplayString
    - labels: []
      labelname: storageIOIndex
  - name: storageIOLA15
    oid: *******.4.1.6574.101.1.1.11
    type: gauge
    help: The 15 minute average load of disk (%) - *******.4.1.6574.101.1.1.11
    indexes:
    - labelname: storageIOIndex
      type: gauge
    lookups:
    - labels:
      - storageIOIndex
      labelname: storageIODevice
      oid: *******.4.1.6574.101.1.1.2
      type: DisplayString
    - labels: []
      labelname: storageIOIndex
  - name: storageIONReadX
    oid: *******.4.1.6574.101.1.1.12
    type: counter
    help: The number of bytes read from this device since boot. - *******.4.1.6574.101.1.1.12
    indexes:
    - labelname: storageIOIndex
      type: gauge
    lookups:
    - labels:
      - storageIOIndex
      labelname: storageIODevice
      oid: *******.4.1.6574.101.1.1.2
      type: DisplayString
    - labels: []
      labelname: storageIOIndex
  - name: storageIONWrittenX
    oid: *******.4.1.6574.101.1.1.13
    type: counter
    help: The number of bytes written to this device since boot. - *******.4.1.6574.101.1.1.13
    indexes:
    - labelname: storageIOIndex
      type: gauge
    lookups:
    - labels:
      - storageIOIndex
      labelname: storageIODevice
      oid: *******.4.1.6574.101.1.1.2
      type: DisplayString
    - labels: []
      labelname: storageIOIndex
  - name: storageIODeviceSerial
    oid: *******.4.1.6574.101.1.1.14
    type: DisplayString
    help: The name of the device we are counting/checking. - *******.4.1.6574.101.1.1.14
    indexes:
    - labelname: storageIOIndex
      type: gauge
    lookups:
    - labels:
      - storageIOIndex
      labelname: storageIODevice
      oid: *******.4.1.6574.101.1.1.2
      type: DisplayString
    - labels: []
      labelname: storageIOIndex
  - name: spaceIOIndex
    oid: *******.4.1.6574.102.1.1.1
    type: gauge
    help: Reference index for each observed device. - *******.4.1.6574.102.1.1.1
    indexes:
    - labelname: spaceIOIndex
      type: gauge
    lookups:
    - labels:
      - spaceIOIndex
      labelname: spaceIODevice
      oid: *******.4.1.6574.102.1.1.2
      type: DisplayString
    - labels: []
      labelname: spaceIOIndex
  - name: spaceIODevice
    oid: *******.4.1.6574.102.1.1.2
    type: DisplayString
    help: The name of the device we are counting/checking. - *******.4.1.6574.102.1.1.2
    indexes:
    - labelname: spaceIOIndex
      type: gauge
    lookups:
    - labels:
      - spaceIOIndex
      labelname: spaceIODevice
      oid: *******.4.1.6574.102.1.1.2
      type: DisplayString
    - labels: []
      labelname: spaceIOIndex
  - name: spaceIONRead
    oid: *******.4.1.6574.102.1.1.3
    type: counter
    help: The number of bytes read from this device since boot. - *******.4.1.6574.102.1.1.3
    indexes:
    - labelname: spaceIOIndex
      type: gauge
    lookups:
    - labels:
      - spaceIOIndex
      labelname: spaceIODevice
      oid: *******.4.1.6574.102.1.1.2
      type: DisplayString
    - labels: []
      labelname: spaceIOIndex
  - name: spaceIONWritten
    oid: *******.4.1.6574.102.1.1.4
    type: counter
    help: The number of bytes written to this device since boot. - *******.4.1.6574.102.1.1.4
    indexes:
    - labelname: spaceIOIndex
      type: gauge
    lookups:
    - labels:
      - spaceIOIndex
      labelname: spaceIODevice
      oid: *******.4.1.6574.102.1.1.2
      type: DisplayString
    - labels: []
      labelname: spaceIOIndex
  - name: spaceIOReads
    oid: *******.4.1.6574.102.1.1.5
    type: counter
    help: The number of read accesses from this device since boot. - *******.4.1.6574.102.1.1.5
    indexes:
    - labelname: spaceIOIndex
      type: gauge
    lookups:
    - labels:
      - spaceIOIndex
      labelname: spaceIODevice
      oid: *******.4.1.6574.102.1.1.2
      type: DisplayString
    - labels: []
      labelname: spaceIOIndex
  - name: spaceIOWrites
    oid: *******.4.1.6574.102.1.1.6
    type: counter
    help: The number of write accesses to this device since boot. - *******.4.1.6574.102.1.1.6
    indexes:
    - labelname: spaceIOIndex
      type: gauge
    lookups:
    - labels:
      - spaceIOIndex
      labelname: spaceIODevice
      oid: *******.4.1.6574.102.1.1.2
      type: DisplayString
    - labels: []
      labelname: spaceIOIndex
  - name: spaceIOLA
    oid: *******.4.1.6574.102.1.1.8
    type: gauge
    help: The load of disk (%) - *******.4.1.6574.102.1.1.8
    indexes:
    - labelname: spaceIOIndex
      type: gauge
    lookups:
    - labels:
      - spaceIOIndex
      labelname: spaceIODevice
      oid: *******.4.1.6574.102.1.1.2
      type: DisplayString
    - labels: []
      labelname: spaceIOIndex
  - name: spaceIOLA1
    oid: *******.4.1.6574.102.1.1.9
    type: gauge
    help: The 1 minute average load of disk (%) - *******.4.1.6574.102.1.1.9
    indexes:
    - labelname: spaceIOIndex
      type: gauge
    lookups:
    - labels:
      - spaceIOIndex
      labelname: spaceIODevice
      oid: *******.4.1.6574.102.1.1.2
      type: DisplayString
    - labels: []
      labelname: spaceIOIndex
  - name: spaceIOLA5
    oid: *******.4.1.6574.102.1.1.10
    type: gauge
    help: The 5 minute average load of disk (%) - *******.4.1.6574.102.1.1.10
    indexes:
    - labelname: spaceIOIndex
      type: gauge
    lookups:
    - labels:
      - spaceIOIndex
      labelname: spaceIODevice
      oid: *******.4.1.6574.102.1.1.2
      type: DisplayString
    - labels: []
      labelname: spaceIOIndex
  - name: spaceIOLA15
    oid: *******.4.1.6574.102.1.1.11
    type: gauge
    help: The 15 minute average load of disk (%) - *******.4.1.6574.102.1.1.11
    indexes:
    - labelname: spaceIOIndex
      type: gauge
    lookups:
    - labels:
      - spaceIOIndex
      labelname: spaceIODevice
      oid: *******.4.1.6574.102.1.1.2
      type: DisplayString
    - labels: []
      labelname: spaceIOIndex
  - name: spaceIONReadX
    oid: *******.4.1.6574.102.1.1.12
    type: counter
    help: The number of bytes read from this device since boot. - *******.4.1.6574.102.1.1.12
    indexes:
    - labelname: spaceIOIndex
      type: gauge
    lookups:
    - labels:
      - spaceIOIndex
      labelname: spaceIODevice
      oid: *******.4.1.6574.102.1.1.2
      type: DisplayString
    - labels: []
      labelname: spaceIOIndex
  - name: spaceIONWrittenX
    oid: *******.4.1.6574.102.1.1.13
    type: counter
    help: The number of bytes written to this device since boot. - *******.4.1.6574.102.1.1.13
    indexes:
    - labelname: spaceIOIndex
      type: gauge
    lookups:
    - labels:
      - spaceIOIndex
      labelname: spaceIODevice
      oid: *******.4.1.6574.102.1.1.2
      type: DisplayString
    - labels: []
      labelname: spaceIOIndex
  - name: spaceUUID
    oid: *******.4.1.6574.102.1.1.14
    type: DisplayString
    help: The uuid of space. - *******.4.1.6574.102.1.1.14
    indexes:
    - labelname: spaceIOIndex
      type: gauge
    lookups:
    - labels:
      - spaceIOIndex
      labelname: spaceIODevice
      oid: *******.4.1.6574.102.1.1.2
      type: DisplayString
    - labels: []
      labelname: spaceIOIndex
  - name: iSCSILUNInfoIndex
    oid: *******.4.1.6574.104.1.1.1
    type: gauge
    help: LUN info index - *******.4.1.6574.104.1.1.1
    indexes:
    - labelname: iSCSILUNInfoIndex
      type: gauge
  - name: iSCSILUNUUID
    oid: *******.4.1.6574.104.1.1.2
    type: OctetString
    help: LUN uuid - *******.4.1.6574.104.1.1.2
    indexes:
    - labelname: iSCSILUNInfoIndex
      type: gauge
  - name: iSCSILUNName
    oid: *******.4.1.6574.104.1.1.3
    type: OctetString
    help: LUN name - *******.4.1.6574.104.1.1.3
    indexes:
    - labelname: iSCSILUNInfoIndex
      type: gauge
  - name: iSCSILUNThroughputReadHigh
    oid: *******.4.1.6574.104.1.1.4
    type: gauge
    help: LUN read throughput over 32 bits part - *******.4.1.6574.104.1.1.4
    indexes:
    - labelname: iSCSILUNInfoIndex
      type: gauge
  - name: iSCSILUNThroughputReadLow
    oid: *******.4.1.6574.104.1.1.5
    type: gauge
    help: LUN read throughput in unsigned 32 bit - *******.4.1.6574.104.1.1.5
    indexes:
    - labelname: iSCSILUNInfoIndex
      type: gauge
  - name: iSCSILUNThroughputWriteHigh
    oid: *******.4.1.6574.104.1.1.6
    type: gauge
    help: LUN write throughput over 32 bits part - *******.4.1.6574.104.1.1.6
    indexes:
    - labelname: iSCSILUNInfoIndex
      type: gauge
  - name: iSCSILUNThroughputWriteLow
    oid: *******.4.1.6574.104.1.1.7
    type: gauge
    help: LUN write throughput in unsigned 32 bit - *******.4.1.6574.104.1.1.7
    indexes:
    - labelname: iSCSILUNInfoIndex
      type: gauge
  - name: iSCSILUNIopsRead
    oid: *******.4.1.6574.104.1.1.8
    type: gauge
    help: LUN read iops - *******.4.1.6574.104.1.1.8
    indexes:
    - labelname: iSCSILUNInfoIndex
      type: gauge
  - name: iSCSILUNIopsWrite
    oid: *******.4.1.6574.104.1.1.9
    type: gauge
    help: LUN write iops - *******.4.1.6574.104.1.1.9
    indexes:
    - labelname: iSCSILUNInfoIndex
      type: gauge
  - name: iSCSILUNDiskLatencyRead
    oid: *******.4.1.6574.104.1.1.10
    type: gauge
    help: LUN disk latency when reading - *******.4.1.6574.104.1.1.10
    indexes:
    - labelname: iSCSILUNInfoIndex
      type: gauge
  - name: iSCSILUNDiskLatencyWrite
    oid: *******.4.1.6574.104.1.1.11
    type: gauge
    help: LUN disk latency when writing - *******.4.1.6574.104.1.1.11
    indexes:
    - labelname: iSCSILUNInfoIndex
      type: gauge
  - name: iSCSILUNNetworkLatencyTx
    oid: *******.4.1.6574.104.1.1.12
    type: gauge
    help: LUN transfer data network latency - *******.4.1.6574.104.1.1.12
    indexes:
    - labelname: iSCSILUNInfoIndex
      type: gauge
  - name: iSCSILUNNetworkLatencyRx
    oid: *******.4.1.6574.104.1.1.13
    type: gauge
    help: LUN receive data network latency - *******.4.1.6574.104.1.1.13
    indexes:
    - labelname: iSCSILUNInfoIndex
      type: gauge
  - name: iSCSILUNIoSizeRead
    oid: *******.4.1.6574.104.1.1.14
    type: gauge
    help: LUN average io size when reading - *******.4.1.6574.104.1.1.14
    indexes:
    - labelname: iSCSILUNInfoIndex
      type: gauge
  - name: iSCSILUNIoSizeWrite
    oid: *******.4.1.6574.104.1.1.15
    type: gauge
    help: LUN average io size when writing - *******.4.1.6574.104.1.1.15
    indexes:
    - labelname: iSCSILUNInfoIndex
      type: gauge
  - name: iSCSILUNQueueDepth
    oid: *******.4.1.6574.104.1.1.16
    type: gauge
    help: Num of iSCSI commands in LUN queue - *******.4.1.6574.104.1.1.16
    indexes:
    - labelname: iSCSILUNInfoIndex
      type: gauge
  - name: iSCSILUNType
    oid: *******.4.1.6574.104.1.1.17
    type: OctetString
    help: LUN type - *******.4.1.6574.104.1.1.17
    indexes:
    - labelname: iSCSILUNInfoIndex
      type: gauge
  - name: iSCSILUNDiskLatencyAvg
    oid: *******.4.1.6574.104.1.1.18
    type: gauge
    help: Average latency of LUN disk - *******.4.1.6574.104.1.1.18
    indexes:
    - labelname: iSCSILUNInfoIndex
      type: gauge
  - name: iSCSILUNThinProvisionVolFreeMBs
    oid: *******.4.1.6574.104.1.1.19
    type: gauge
    help: Free space(MB) of thin provisioning lun's volume - *******.4.1.6574.104.1.1.19
    indexes:
    - labelname: iSCSILUNInfoIndex
      type: gauge
  - name: diskIndex
    oid: *******.4.1.6574.2.1.1.1
    type: gauge
    help: The index of disk table - *******.4.1.6574.2.1.1.1
    indexes:
    - labelname: diskIndex
      type: gauge
    lookups:
    - labels:
      - diskIndex
      labelname: diskID
      oid: *******.4.1.6574.2.1.1.2
      type: OctetString
    - labels: []
      labelname: diskIndex
  - name: diskID
    oid: *******.4.1.6574.2.1.1.2
    type: OctetString
    help: Synology disk ID The ID of disk is assigned by disk Station. - *******.4.1.6574.2.1.1.2
    indexes:
    - labelname: diskIndex
      type: gauge
    lookups:
    - labels:
      - diskIndex
      labelname: diskID
      oid: *******.4.1.6574.2.1.1.2
      type: OctetString
    - labels: []
      labelname: diskIndex
  - name: diskModel
    oid: *******.4.1.6574.2.1.1.3
    type: DisplayString
    help: Synology disk model name The disk model name will be showed here. - *******.4.1.6574.2.1.1.3
    indexes:
    - labelname: diskIndex
      type: gauge
    lookups:
    - labels:
      - diskIndex
      labelname: diskID
      oid: *******.4.1.6574.2.1.1.2
      type: OctetString
    - labels: []
      labelname: diskIndex
  - name: diskType
    oid: *******.4.1.6574.2.1.1.4
    type: DisplayString
    help: Synology disk type The type of disk will be showed here, including SATA,
      SSD and so on. - *******.4.1.6574.2.1.1.4
    indexes:
    - labelname: diskIndex
      type: gauge
    lookups:
    - labels:
      - diskIndex
      labelname: diskID
      oid: *******.4.1.6574.2.1.1.2
      type: OctetString
    - labels: []
      labelname: diskIndex
  - name: diskStatus
    oid: *******.4.1.6574.2.1.1.5
    type: gauge
    help: Synology disk status Each meanings of status represented describe below
      - *******.4.1.6574.2.1.1.5
    indexes:
    - labelname: diskIndex
      type: gauge
    lookups:
    - labels:
      - diskIndex
      labelname: diskID
      oid: *******.4.1.6574.2.1.1.2
      type: OctetString
    - labels: []
      labelname: diskIndex
  - name: diskTemperature
    oid: *******.4.1.6574.2.1.1.6
    type: gauge
    help: Synology disk temperature The temperature of each disk uses Celsius degree.
      - *******.4.1.6574.2.1.1.6
    indexes:
    - labelname: diskIndex
      type: gauge
    lookups:
    - labels:
      - diskIndex
      labelname: diskID
      oid: *******.4.1.6574.2.1.1.2
      type: OctetString
    - labels: []
      labelname: diskIndex
  - name: diskRole
    oid: *******.4.1.6574.2.1.1.7
    type: OctetString
    help: Synology disk role The role of the disk in system - *******.4.1.6574.2.1.1.7
    indexes:
    - labelname: diskIndex
      type: gauge
    lookups:
    - labels:
      - diskIndex
      labelname: diskID
      oid: *******.4.1.6574.2.1.1.2
      type: OctetString
    - labels: []
      labelname: diskIndex
  - name: diskRetry
    oid: *******.4.1.6574.2.1.1.8
    type: gauge
    help: Synology disk retry count The count of each disk connection retries. - *******.4.1.6574.2.1.1.8
    indexes:
    - labelname: diskIndex
      type: gauge
    lookups:
    - labels:
      - diskIndex
      labelname: diskID
      oid: *******.4.1.6574.2.1.1.2
      type: OctetString
    - labels: []
      labelname: diskIndex
  - name: diskBadSector
    oid: *******.4.1.6574.2.1.1.9
    type: gauge
    help: Synology disk bad sector count The count of each disk I/O bad sector. -
      *******.4.1.6574.2.1.1.9
    indexes:
    - labelname: diskIndex
      type: gauge
    lookups:
    - labels:
      - diskIndex
      labelname: diskID
      oid: *******.4.1.6574.2.1.1.2
      type: OctetString
    - labels: []
      labelname: diskIndex
  - name: diskIdentifyFail
    oid: *******.4.1.6574.2.1.1.10
    type: gauge
    help: Synology disk identify fail count The count of each disk identify fails.
      - *******.4.1.6574.2.1.1.10
    indexes:
    - labelname: diskIndex
      type: gauge
    lookups:
    - labels:
      - diskIndex
      labelname: diskID
      oid: *******.4.1.6574.2.1.1.2
      type: OctetString
    - labels: []
      labelname: diskIndex
  - name: diskRemainLife
    oid: *******.4.1.6574.2.1.1.11
    type: gauge
    help: Synology disk remainLife The estimate remain life of each disk. - *******.4.1.6574.2.1.1.11
    indexes:
    - labelname: diskIndex
      type: gauge
    lookups:
    - labels:
      - diskIndex
      labelname: diskID
      oid: *******.4.1.6574.2.1.1.2
      type: OctetString
    - labels: []
      labelname: diskIndex
  - name: diskName
    oid: *******.4.1.6574.2.1.1.12
    type: OctetString
    help: Synology disk name The name of disk which align to storage manager. - *******.4.1.6574.2.1.1.12
    indexes:
    - labelname: diskIndex
      type: gauge
    lookups:
    - labels:
      - diskIndex
      labelname: diskID
      oid: *******.4.1.6574.2.1.1.2
      type: OctetString
    - labels: []
      labelname: diskIndex
  - name: diskHealthStatus
    oid: *******.4.1.6574.2.1.1.13
    type: gauge
    help: Synology disk health status Each meanings of status represented describe
      below - *******.4.1.6574.2.1.1.13
    indexes:
    - labelname: diskIndex
      type: gauge
    lookups:
    - labels:
      - diskIndex
      labelname: diskID
      oid: *******.4.1.6574.2.1.1.2
      type: OctetString
    - labels: []
      labelname: diskIndex
  - name: raidIndex
    oid: *******.4.1.6574.3.1.1.1
    type: gauge
    help: The index of raid table - *******.4.1.6574.3.1.1.1
    indexes:
    - labelname: raidIndex
      type: gauge
    lookups:
    - labels:
      - raidIndex
      labelname: raidName
      oid: *******.4.1.6574.3.1.1.2
      type: DisplayString
    - labels: []
      labelname: raidIndex
  - name: raidName
    oid: *******.4.1.6574.3.1.1.2
    type: DisplayString
    help: Synology raid name The name of each raid will be showed here. - *******.4.1.6574.3.1.1.2
    indexes:
    - labelname: raidIndex
      type: gauge
    lookups:
    - labels:
      - raidIndex
      labelname: raidName
      oid: *******.4.1.6574.3.1.1.2
      type: DisplayString
    - labels: []
      labelname: raidIndex
  - name: raidStatus
    oid: *******.4.1.6574.3.1.1.3
    type: gauge
    help: Synology Raid status Each meanings of status represented describe below
      - *******.4.1.6574.3.1.1.3
    indexes:
    - labelname: raidIndex
      type: gauge
    lookups:
    - labels:
      - raidIndex
      labelname: raidName
      oid: *******.4.1.6574.3.1.1.2
      type: DisplayString
    - labels: []
      labelname: raidIndex
  - name: raidFreeSize
    oid: *******.4.1.6574.3.1.1.4
    type: gauge
    help: Synology raid freesize Free space in bytes. - *******.4.1.6574.3.1.1.4
    indexes:
    - labelname: raidIndex
      type: gauge
    lookups:
    - labels:
      - raidIndex
      labelname: raidName
      oid: *******.4.1.6574.3.1.1.2
      type: DisplayString
    - labels: []
      labelname: raidIndex
  - name: raidTotalSize
    oid: *******.4.1.6574.3.1.1.5
    type: gauge
    help: Synology raid totalsize Total space in bytes. - *******.4.1.6574.3.1.1.5
    indexes:
    - labelname: raidIndex
      type: gauge
    lookups:
    - labels:
      - raidIndex
      labelname: raidName
      oid: *******.4.1.6574.3.1.1.2
      type: DisplayString
    - labels: []
      labelname: raidIndex
  - name: raidHotspareCnt
    oid: *******.4.1.6574.3.1.1.6
    type: gauge
    help: Synology raid hotspare Total hotspare disks count - *******.4.1.6574.3.1.1.6
    indexes:
    - labelname: raidIndex
      type: gauge
    lookups:
    - labels:
      - raidIndex
      labelname: raidName
      oid: *******.4.1.6574.3.1.1.2
      type: DisplayString
    - labels: []
      labelname: raidIndex
  - name: upsDeviceModel
    oid: *******.4.1.6574.4.1.1
    type: DisplayString
    help: Device model - *******.4.1.6574.4.1.1
  - name: upsDeviceManufacturer
    oid: *******.4.1.6574.4.1.2
    type: DisplayString
    help: Device manufacturer - *******.4.1.6574.4.1.2
  - name: upsDeviceSerial
    oid: *******.4.1.6574.4.1.3
    type: DisplayString
    help: Device serial number. - *******.4.1.6574.4.1.3
  - name: upsDeviceType
    oid: *******.4.1.6574.4.1.4
    type: DisplayString
    help: Device type (ups, pdu, scd, psu) - *******.4.1.6574.4.1.4
  - name: upsDeviceDescription
    oid: *******.4.1.6574.4.1.5
    type: DisplayString
    help: Device description. - *******.4.1.6574.4.1.5
  - name: upsDeviceContact
    oid: *******.4.1.6574.4.1.6
    type: DisplayString
    help: Device administrator name. - *******.4.1.6574.4.1.6
  - name: upsDeviceLocation
    oid: *******.4.1.6574.4.1.7
    type: DisplayString
    help: Device physical location. - *******.4.1.6574.4.1.7
  - name: upsDevicePart
    oid: *******.4.1.6574.4.1.8
    type: DisplayString
    help: Device part number. - *******.4.1.6574.4.1.8
  - name: upsDeviceMACAddr
    oid: *******.4.1.6574.4.1.9
    type: DisplayString
    help: Physical network address of the device. - *******.4.1.6574.4.1.9
  - name: upsInfoStatus
    oid: *******.4.1.6574.4.2.1
    type: DisplayString
    help: UPS status. - *******.4.1.6574.4.2.1
  - name: upsInfoAlarm
    oid: *******.4.1.6574.4.2.2
    type: DisplayString
    help: UPS alarms - *******.4.1.6574.4.2.2
  - name: upsInfoTime
    oid: *******.4.1.6574.4.2.3
    type: DisplayString
    help: Internal UPS clock time - *******.4.1.6574.4.2.3
  - name: upsInfoDate
    oid: *******.4.1.6574.4.2.4
    type: DisplayString
    help: Internal UPS clock date - *******.4.1.6574.4.2.4
  - name: upsInfoModel
    oid: *******.4.1.6574.4.2.5
    type: DisplayString
    help: UPS model - *******.4.1.6574.4.2.5
  - name: upsInfoMfrName
    oid: *******.4.1.6574.*******
    type: DisplayString
    help: UPS manufacturer - *******.4.1.6574.*******
  - name: upsInfoMfrDate
    oid: *******.4.1.6574.*******
    type: DisplayString
    help: UPS manufacturing date - *******.4.1.6574.*******
  - name: upsInfoSerial
    oid: *******.4.1.6574.4.2.7
    type: DisplayString
    help: UPS serial number - *******.4.1.6574.4.2.7
  - name: upsInfoVendorID
    oid: *******.4.1.6574.4.2.8
    type: DisplayString
    help: Vendor ID for USB devices - *******.4.1.6574.4.2.8
  - name: upsInfoProductID
    oid: *******.4.1.6574.4.2.9
    type: DisplayString
    help: Product ID for USB devices - *******.4.1.6574.4.2.9
  - name: upsInfoFirmwareName
    oid: *******.4.1.6574.4.2.10.1
    type: DisplayString
    help: UPS firmware - *******.4.1.6574.4.2.10.1
  - name: upsInfoFirmwareAux
    oid: *******.4.1.6574.4.2.10.2
    type: DisplayString
    help: Auxiliary device firmware - *******.4.1.6574.4.2.10.2
  - name: upsInfoTemperature
    oid: *******.4.1.6574.4.2.11
    type: Float
    help: UPS temperature (degrees C) - *******.4.1.6574.4.2.11
  - name: upsInfoLoadValue
    oid: *******.4.1.6574.4.2.12.1
    type: Float
    help: Load on UPS (percent) - *******.4.1.6574.4.2.12.1
  - name: upsInfoLoadHigh
    oid: *******.4.1.6574.4.2.12.2
    type: Float
    help: Load when UPS switches to overload condition (OVER) (percent) - *******.4.1.6574.4.2.12.2
  - name: upsInfoID
    oid: *******.4.1.6574.4.2.13
    type: DisplayString
    help: UPS system identifier - *******.4.1.6574.4.2.13
  - name: upsInfoDelayStart
    oid: *******.4.1.6574.4.2.14.1
    type: gauge
    help: Interval to wait before restarting the load (seconds) - *******.4.1.6574.4.2.14.1
  - name: upsInfoDelayReboot
    oid: *******.4.1.6574.4.2.14.2
    type: gauge
    help: Interval to wait before rebooting the UPS (seconds) - *******.4.1.6574.4.2.14.2
  - name: upsInfoDelayShutdown
    oid: *******.4.1.6574.4.2.14.3
    type: gauge
    help: Interval to wait after shutdown with delay command (seconds) - *******.4.1.6574.4.2.14.3
  - name: upsInfoTimerStart
    oid: *******.4.1.6574.4.2.15.1
    type: gauge
    help: Time before the load will be started (seconds) - *******.4.1.6574.4.2.15.1
  - name: upsInfoTimerReboot
    oid: *******.4.1.6574.4.2.15.2
    type: gauge
    help: Time before the load will be rebooted (seconds) - *******.4.1.6574.4.2.15.2
  - name: upsInfoTimerShutdown
    oid: *******.4.1.6574.4.2.15.3
    type: gauge
    help: Time before the load will be shutdown (seconds) - *******.4.1.6574.4.2.15.3
  - name: upsInfoTestInterval
    oid: *******.4.1.6574.4.2.16.1
    type: gauge
    help: Interval between self tests - *******.4.1.6574.4.2.16.1
  - name: upsInfoTestResult
    oid: *******.4.1.6574.4.2.16.2
    type: DisplayString
    help: Results of last self test - *******.4.1.6574.4.2.16.2
  - name: upsInfoDisplayLanguage
    oid: *******.4.1.6574.4.2.17
    type: DisplayString
    help: Language to use on front panel - *******.4.1.6574.4.2.17
  - name: upsInfoContacts
    oid: *******.4.1.6574.4.2.18
    type: DisplayString
    help: UPS external contact sensors - *******.4.1.6574.4.2.18
  - name: upsInfoEffciency
    oid: *******.4.1.6574.4.2.19
    type: gauge
    help: Efficiency of the UPS (ratio of the output current on the input current)
      (percent) - *******.4.1.6574.4.2.19
  - name: upsInfoPowerValue
    oid: *******.4.1.6574.4.2.20.1
    type: Float
    help: Current value of apparent power (Volt-Amps) - *******.4.1.6574.4.2.20.1
  - name: upsInfoPowerNominal
    oid: *******.4.1.6574.4.2.20.2
    type: Float
    help: Nominal value of apparent power (Volt-Amps) - *******.4.1.6574.4.2.20.2
  - name: upsInfoRealPowerValue
    oid: *******.4.1.6574.4.2.21.1
    type: Float
    help: Current value of real power (Watts) - *******.4.1.6574.4.2.21.1
  - name: upsInfoRealPowerNominal
    oid: *******.4.1.6574.4.2.21.2
    type: Float
    help: Nominal value of real power (Watts) - *******.4.1.6574.4.2.21.2
  - name: upsInfoBeeperStatus
    oid: *******.4.1.6574.4.2.22
    type: DisplayString
    help: UPS beeper status (enabled, disabled or muted) - *******.4.1.6574.4.2.22
  - name: upsInfoType
    oid: *******.4.1.6574.4.2.23
    type: DisplayString
    help: UPS type - *******.4.1.6574.4.2.23
  - name: upsInfoWatchdogStatus
    oid: *******.4.1.6574.4.2.24
    type: DisplayString
    help: UPS watchdog status (enabled or disabled) - *******.4.1.6574.4.2.24
  - name: upsInfoStartAuto
    oid: *******.4.1.6574.4.2.25.1
    type: DisplayString
    help: UPS starts when mains is (re)applied - *******.4.1.6574.4.2.25.1
  - name: upsInfoStartBattery
    oid: *******.4.1.6574.4.2.25.2
    type: DisplayString
    help: Allow to start UPS from battery - *******.4.1.6574.4.2.25.2
  - name: upsInfoStartReboot
    oid: *******.4.1.6574.4.2.25.3
    type: DisplayString
    help: UPS coldstarts from battery (enabled or disabled) - *******.4.1.6574.4.2.25.3
  - name: upsBatteryChargeValue
    oid: *******.4.1.6574.4.3.1.1
    type: Float
    help: Battery charge - *******.4.1.6574.4.3.1.1
  - name: upsBatteryChargeLow
    oid: *******.4.1.6574.4.3.1.2
    type: Float
    help: Remaining battery level when UPS switches to LB (percent) - *******.4.1.6574.4.3.1.2
  - name: upsBatteryChargeRestart
    oid: *******.4.1.6574.4.3.1.3
    type: Float
    help: Minimum battery level for UPS restart after power-off - *******.4.1.6574.4.3.1.3
  - name: upsBatteryChargeWarning
    oid: *******.4.1.6574.4.3.1.4
    type: Float
    help: Battery level when UPS switches to Warning state (percent) - *******.4.1.6574.4.3.1.4
  - name: upsBatteryVoltageValue
    oid: *******.4.1.6574.4.3.2.1
    type: Float
    help: The magnitude of the present battery voltage. - *******.4.1.6574.4.3.2.1
  - name: upsBatteryVoltageNominal
    oid: *******.4.1.6574.4.3.2.2
    type: Float
    help: Nominal battery voltage. - *******.4.1.6574.4.3.2.2
  - name: upsBatteryVoltageLow
    oid: *******.4.1.6574.4.3.2.3
    type: Float
    help: Minimum battery voltage, that triggers FSD status . - *******.4.1.6574.4.3.2.3
  - name: upsBatteryVoltageHigh
    oid: *******.4.1.6574.4.3.2.4
    type: Float
    help: Maximum battery voltage (Ie battery.charge = 100). - *******.4.1.6574.4.3.2.4
  - name: upsBatteryCapacity
    oid: *******.4.1.6574.4.3.3
    type: Float
    help: Battery capacity (Ah) - *******.4.1.6574.4.3.3
  - name: upsBatteryCurrent
    oid: *******.4.1.6574.4.3.4
    type: Float
    help: The present battery current. - *******.4.1.6574.4.3.4
  - name: upsBatteryTemperature
    oid: *******.4.1.6574.4.3.5
    type: Float
    help: The ambient temperature at or near the UPS Battery casing. - *******.4.1.6574.4.3.5
  - name: upsBatteryRuntimeValue
    oid: *******.4.1.6574.4.3.6.1
    type: gauge
    help: Battery runtime (seconds) - *******.4.1.6574.4.3.6.1
  - name: upsBatteryRuntimeLow
    oid: *******.4.1.6574.4.3.6.2
    type: gauge
    help: Remaining battery runtime when UPS switches to LB (seconds) - *******.4.1.6574.4.3.6.2
  - name: upsBatteryRuntimeRestart
    oid: *******.4.1.6574.4.3.6.3
    type: gauge
    help: Minimum battery runtime for UPS restart after power-off (seconds) - *******.4.1.6574.4.3.6.3
  - name: upsBatteryAlarmThreshold
    oid: *******.4.1.6574.4.3.7
    type: DisplayString
    help: Battery alarm threshold - *******.4.1.6574.4.3.7
  - name: upsBatteryDate
    oid: *******.4.1.6574.4.3.8
    type: DisplayString
    help: Battery change date - *******.4.1.6574.4.3.8
  - name: upsBatteryMfrDate
    oid: *******.4.1.6574.4.3.9
    type: DisplayString
    help: Battery manufacturing date - *******.4.1.6574.4.3.9
  - name: upsBatteryPacks
    oid: *******.4.1.6574.4.3.10
    type: gauge
    help: Number of battery packs - *******.4.1.6574.4.3.10
  - name: upsBatteryPacksBad
    oid: *******.4.1.6574.4.3.11
    type: gauge
    help: Number of bad battery packs - *******.4.1.6574.4.3.11
  - name: upsBatteryType
    oid: *******.4.1.6574.4.3.12
    type: DisplayString
    help: Battery chemistry - *******.4.1.6574.4.3.12
  - name: upsBatteryProtection
    oid: *******.4.1.6574.4.3.13
    type: DisplayString
    help: Prevent deep discharge of battery - *******.4.1.6574.4.3.13
  - name: upsBatteryEnergySave
    oid: *******.4.1.6574.4.3.14
    type: DisplayString
    help: Switch off when running on battery and no/low load - *******.4.1.6574.4.3.14
  - name: upsInputVoltageValue
    oid: *******.4.1.6574.4.4.1.1
    type: Float
    help: Input voltage - *******.4.1.6574.4.4.1.1
  - name: upsInputVoltageMax
    oid: *******.4.1.6574.4.4.1.2
    type: Float
    help: Maximum incoming voltage seen - *******.4.1.6574.4.4.1.2
  - name: upsInputVoltageMin
    oid: *******.4.1.6574.4.4.1.3
    type: Float
    help: Minimum incoming voltage seen - *******.4.1.6574.4.4.1.3
  - name: upsInputVoltageNominal
    oid: *******.4.1.6574.4.4.1.4
    type: Float
    help: Nominal input voltage - *******.4.1.6574.4.4.1.4
  - name: upsInputVoltageExtend
    oid: *******.4.1.6574.4.4.1.5
    type: DisplayString
    help: Extended input voltage range - *******.4.1.6574.4.4.1.5
  - name: upsInputVoltageFault
    oid: *******.4.1.6574.4.4.1.6
    type: Float
    help: Input voltage Fault - *******.4.1.6574.4.4.1.6
  - name: upsInputTransferReason
    oid: *******.4.1.6574.4.4.2.1
    type: DisplayString
    help: Reason for last transfer to battery - *******.4.1.6574.4.4.2.1
  - name: upsInputTransferLow
    oid: *******.4.1.6574.4.4.2.2
    type: Float
    help: Low voltage transfer point - *******.4.1.6574.4.4.2.2
  - name: upsInputTransferHigh
    oid: *******.4.1.6574.4.4.2.3
    type: Float
    help: High voltage transfer point - *******.4.1.6574.4.4.2.3
  - name: upsInputTransferLowMin
    oid: *******.4.1.6574.4.4.2.4
    type: Float
    help: smallest settable low voltage transfer point - *******.4.1.6574.4.4.2.4
  - name: upsInputTransferLowMax
    oid: *******.4.1.6574.4.4.2.5
    type: Float
    help: greatest settable low voltage transfer point - *******.4.1.6574.4.4.2.5
  - name: upsInputTransferHighMin
    oid: *******.4.1.6574.4.4.2.6
    type: Float
    help: smallest settable high voltage transfer point - *******.4.1.6574.4.4.2.6
  - name: upsInputTransferHighMax
    oid: *******.4.1.6574.4.4.2.7
    type: Float
    help: greatest settable high voltage transfer point - *******.4.1.6574.4.4.2.7
  - name: upsInputTransferBoostLow
    oid: *******.4.1.6574.4.4.2.8
    type: Float
    help: Low voltage boosting transfer point - *******.4.1.6574.4.4.2.8
  - name: upsInputTransferBoostHigh
    oid: *******.4.1.6574.4.4.2.9
    type: Float
    help: High voltage boosting transfer point - *******.4.1.6574.4.4.2.9
  - name: upsInputTransferTrimLow
    oid: *******.4.1.6574.4.4.2.10
    type: Float
    help: Low voltage trimming transfer point - *******.4.1.6574.4.4.2.10
  - name: upsInputTransferTrimHigh
    oid: *******.4.1.6574.4.4.2.11
    type: Float
    help: High voltage trimming transfer point - *******.4.1.6574.4.4.2.11
  - name: upsInputSensitivity
    oid: *******.4.1.6574.4.4.3
    type: DisplayString
    help: Input power sensitivity - *******.4.1.6574.4.4.3
  - name: upsInputQuality
    oid: *******.4.1.6574.4.4.4
    type: DisplayString
    help: Input power quality - *******.4.1.6574.4.4.4
  - name: upsInputCurrentValue
    oid: *******.4.1.6574.4.4.5.1
    type: Float
    help: Input current (A) - *******.4.1.6574.4.4.5.1
  - name: upsInputCurrentNominal
    oid: *******.4.1.6574.4.4.5.2
    type: Float
    help: Nominal input current (A) - *******.4.1.6574.4.4.5.2
  - name: upsInputFrequencyValue
    oid: *******.4.1.6574.4.4.6.1
    type: Float
    help: Input line frequency (Hz) - *******.4.1.6574.4.4.6.1
  - name: upsInputFrequencyNominal
    oid: *******.4.1.6574.4.4.6.2
    type: Float
    help: Nominal input line frequency (Hz) - *******.4.1.6574.4.4.6.2
  - name: upsInputFrequencyLow
    oid: *******.4.1.6574.4.4.6.3
    type: Float
    help: Input line frequency low (Hz) - *******.4.1.6574.4.4.6.3
  - name: upsInputFrequencyHigh
    oid: *******.4.1.6574.4.4.6.4
    type: Float
    help: Input line frequency high (Hz) - *******.4.1.6574.4.4.6.4
  - name: upsInputFrequencyExtend
    oid: *******.4.1.6574.4.4.6.5
    type: DisplayString
    help: Extended input frequency range - *******.4.1.6574.4.4.6.5
  - name: upsOutputVoltageValue
    oid: *******.4.1.6574.4.5.1.1
    type: Float
    help: Output voltage (V) - *******.4.1.6574.4.5.1.1
  - name: upsOutputVoltageNominal
    oid: *******.4.1.6574.4.5.1.2
    type: Float
    help: Nominal output voltage (V) - *******.4.1.6574.4.5.1.2
  - name: upsOutputFrequencyValue
    oid: *******.4.1.6574.4.5.2.1
    type: Float
    help: Output frequency (Hz) - *******.4.1.6574.4.5.2.1
  - name: upsOutputFrequencyNominal
    oid: *******.4.1.6574.4.5.2.2
    type: Float
    help: Nominal output frequency (Hz) - *******.4.1.6574.4.5.2.2
  - name: upsOutputCurrentValue
    oid: *******.4.1.6574.4.5.3.1
    type: Float
    help: Output current (A) - *******.4.1.6574.4.5.3.1
  - name: upsOutputCurrentNominal
    oid: *******.4.1.6574.4.5.3.2
    type: Float
    help: Nominal output current (A) - *******.4.1.6574.4.5.3.2
  - name: upsAmbientTemperatureValue
    oid: *******.4.1.6574.4.6.1.1
    type: Float
    help: Ambient temperature (degrees C) - *******.4.1.6574.4.6.1.1
  - name: upsAmbientTemperatureAlarm
    oid: *******.4.1.6574.4.6.1.2
    type: DisplayString
    help: Temperature alarm (enabled/disabled) - *******.4.1.6574.4.6.1.2
  - name: upsAmbientTemperatureHigh
    oid: *******.4.1.6574.4.6.1.3
    type: Float
    help: Temperature threshold high (degrees C) - *******.4.1.6574.4.6.1.3
  - name: upsAmbientTemperatureLow
    oid: *******.4.1.6574.4.6.1.4
    type: Float
    help: Temperature threshold low (degrees C) - *******.4.1.6574.4.6.1.4
  - name: upsAmbientTemperatureMax
    oid: *******.4.1.6574.4.6.1.5
    type: Float
    help: Maximum temperature seen (degrees C) - *******.4.1.6574.4.6.1.5
  - name: upsAmbientTemperatureMin
    oid: *******.4.1.6574.4.6.1.6
    type: Float
    help: Minimum temperature seen (degrees C) - *******.4.1.6574.4.6.1.6
  - name: upsAmbientHumidityValue
    oid: *******.4.1.6574.4.6.2.1
    type: Float
    help: Ambient relative humidity (percent) - *******.4.1.6574.4.6.2.1
  - name: upsAmbientHumidityAlarm
    oid: *******.4.1.6574.4.6.2.2
    type: DisplayString
    help: Relative humidity alarm (enabled/disabled) - *******.4.1.6574.4.6.2.2
  - name: upsAmbientHumidityHigh
    oid: *******.4.1.6574.4.6.2.3
    type: Float
    help: Relative humidity threshold high (percent) - *******.4.1.6574.4.6.2.3
  - name: upsAmbientHumidityLow
    oid: *******.4.1.6574.4.6.2.4
    type: Float
    help: Relative humidity threshold low (percent) - *******.4.1.6574.4.6.2.4
  - name: upsAmbientHumidityMax
    oid: *******.4.1.6574.4.6.2.5
    type: Float
    help: Maximum relative humidity seen (percent) - *******.4.1.6574.4.6.2.5
  - name: upsAmbientHumidityMin
    oid: *******.4.1.6574.4.6.2.6
    type: Float
    help: Minimum relative humidity seen (percent) - *******.4.1.6574.4.6.2.6
  - name: upsDriverName
    oid: *******.4.1.6574.4.7.1
    type: DisplayString
    help: Driver name - *******.4.1.6574.4.7.1
  - name: upsDriverVersion
    oid: *******.4.1.6574.4.7.2
    type: DisplayString
    help: Driver version (NUT release) - *******.4.1.6574.4.7.2
  - name: upsDriverVersionData
    oid: *******.4.1.6574.4.7.3
    type: DisplayString
    help: Driver version data - *******.4.1.6574.4.7.3
  - name: upsDriverVersionInternal
    oid: *******.4.1.6574.4.7.4
    type: DisplayString
    help: Internal driver version (if tracked separately) - *******.4.1.6574.4.7.4
  - name: upsDriverPollInterval
    oid: *******.4.1.6574.4.7.5
    type: gauge
    help: Poll interval setup in configuration file - *******.4.1.6574.4.7.5
  - name: upsDriverPort
    oid: *******.4.1.6574.4.7.6
    type: DisplayString
    help: Port setup in configuration file - *******.4.1.6574.4.7.6
  - name: upsDriverPollFrequency
    oid: *******.4.1.6574.4.7.7
    type: gauge
    help: Poll frequency - *******.4.1.6574.4.7.7
  - name: upsDriverProductID
    oid: *******.4.1.6574.4.7.8
    type: DisplayString
    help: Product ID - *******.4.1.6574.4.7.8
  - name: upsDriverSnmpVersion
    oid: *******.4.1.6574.4.7.9
    type: DisplayString
    help: Snmp version - *******.4.1.6574.4.7.9
  - name: upsServerInfo
    oid: *******.4.1.6574.4.8.1
    type: DisplayString
    help: Server information - *******.4.1.6574.4.8.1
  - name: upsServerVersion
    oid: *******.4.1.6574.4.8.2
    type: DisplayString
    help: Server version - *******.4.1.6574.4.8.2
  - name: diskSMARTInfoIndex
    oid: *******.4.1.6574.5.1.1.1
    type: gauge
    help: SMART info index - *******.4.1.6574.5.1.1.1
    indexes:
    - labelname: diskSMARTInfoIndex
      type: gauge
  - name: diskSMARTInfoDevName
    oid: *******.4.1.6574.5.1.1.2
    type: DisplayString
    help: SMART info device name - *******.4.1.6574.5.1.1.2
    indexes:
    - labelname: diskSMARTInfoIndex
      type: gauge
  - name: diskSMARTAttrName
    oid: *******.4.1.6574.5.1.1.3
    type: DisplayString
    help: SMART attribute name - *******.4.1.6574.5.1.1.3
    indexes:
    - labelname: diskSMARTInfoIndex
      type: gauge
  - name: diskSMARTAttrId
    oid: *******.4.1.6574.5.1.1.4
    type: gauge
    help: SMART attribute ID - *******.4.1.6574.5.1.1.4
    indexes:
    - labelname: diskSMARTInfoIndex
      type: gauge
  - name: diskSMARTAttrCurrent
    oid: *******.4.1.6574.5.1.1.5
    type: gauge
    help: SMART attribute current value - *******.4.1.6574.5.1.1.5
    indexes:
    - labelname: diskSMARTInfoIndex
      type: gauge
  - name: diskSMARTAttrWorst
    oid: *******.4.1.6574.5.1.1.6
    type: gauge
    help: SMART attribute worst value - *******.4.1.6574.5.1.1.6
    indexes:
    - labelname: diskSMARTInfoIndex
      type: gauge
  - name: diskSMARTAttrThreshold
    oid: *******.4.1.6574.5.1.1.7
    type: gauge
    help: SMART attribute threshold value - *******.4.1.6574.5.1.1.7
    indexes:
    - labelname: diskSMARTInfoIndex
      type: gauge
  - name: diskSMARTAttrRaw
    oid: *******.4.1.6574.5.1.1.8
    type: gauge
    help: SMART attribute raw value - *******.4.1.6574.5.1.1.8
    indexes:
    - labelname: diskSMARTInfoIndex
      type: gauge
  - name: diskSMARTAttrStatus
    oid: *******.4.1.6574.5.1.1.9
    type: DisplayString
    help: SMART attribute status - *******.4.1.6574.5.1.1.9
    indexes:
    - labelname: diskSMARTInfoIndex
      type: gauge
  - name: serviceInfoIndex
    oid: *******.4.1.6574.6.1.1.1
    type: gauge
    help: Service info index - *******.4.1.6574.6.1.1.1
    indexes:
    - labelname: serviceInfoIndex
      type: gauge
    lookups:
    - labels:
      - serviceInfoIndex
      labelname: serviceName
      oid: *******.4.1.6574.6.1.1.2
      type: DisplayString
    - labels: []
      labelname: serviceInfoIndex
  - name: serviceName
    oid: *******.4.1.6574.6.1.1.2
    type: DisplayString
    help: Service name - *******.4.1.6574.6.1.1.2
    indexes:
    - labelname: serviceInfoIndex
      type: gauge
    lookups:
    - labels:
      - serviceInfoIndex
      labelname: serviceName
      oid: *******.4.1.6574.6.1.1.2
      type: DisplayString
    - labels: []
      labelname: serviceInfoIndex
  - name: serviceUsers
    oid: *******.4.1.6574.6.1.1.3
    type: gauge
    help: Number of users using this service - *******.4.1.6574.6.1.1.3
    indexes:
    - labelname: serviceInfoIndex
      type: gauge
    lookups:
    - labels:
      - serviceInfoIndex
      labelname: serviceName
      oid: *******.4.1.6574.6.1.1.2
      type: DisplayString
    - labels: []
      labelname: serviceInfoIndex
  auth:
    community: synology
