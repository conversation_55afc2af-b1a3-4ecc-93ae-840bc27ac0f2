apiVersion: v1
kind: Namespace
metadata:
  name: snmp-exporter
---
apiVersion: v1
data:
  snmp.yml: |
    # WARNING: This file was auto-generated using snmp_exporter generator, manual changes will be lost.
    mikrotik:
      walk:
      - *******.2.1.2
      - *******.2.1.25
      - *******.2.1.31
      - *******.4.1.14988
      - *******.4.1.2021.10.1.1
      - *******.4.1.2021.10.1.2
      - *******.*******.0
      get:
      - *******.*******.0
      - *******.*******.0
      metrics:
      - name: sysDescr
        oid: *******.*******
        type: DisplayString
        help: A textual description of the entity - *******.*******
      - name: sysIdentity
        oid: *******.*******.0
        type: DisplayString
        help: A textual description of the system identity - *******.*******.0
      - name: sysUpTime
        oid: *******.*******
        type: gauge
        help: The time (in hundredths of a second) since the network management portion
          of the system was last re-initialized. - *******.*******
      - name: ifNumber
        oid: *******.*******
        type: gauge
        help: The number of network interfaces (regardless of their current state) present
          on this system. - *******.*******
      - name: ifIndex
        oid: *******.*******.1.1
        type: gauge
        help: A unique value, greater than zero, for each interface - *******.*******.1.1
        indexes:
        - labelname: ifIndex
          type: gauge
        lookups:
        - labels:
          - ifIndex
          labelname: ifName
          oid: *******.********.1.1.1
          type: DisplayString
      - name: ifDescr
        oid: *******.*******.1.2
        type: DisplayString
        help: A textual string containing information about the interface - *******.*******.1.2
        indexes:
        - labelname: ifIndex
          type: gauge
        lookups:
        - labels:
          - ifIndex
          labelname: ifName
          oid: *******.********.1.1.1
          type: DisplayString
      - name: ifType
        oid: *******.*******.1.3
        type: EnumAsInfo
        help: The type of interface - *******.*******.1.3
        indexes:
        - labelname: ifIndex
          type: gauge
        lookups:
        - labels:
          - ifIndex
          labelname: ifName
          oid: *******.********.1.1.1
          type: DisplayString
        enum_values:
          1: other
          2: regular1822
          3: hdh1822
          4: ddnX25
          5: rfc877x25
          6: ethernetCsmacd
          7: iso88023Csmacd
          8: iso88024TokenBus
          9: iso88025TokenRing
          10: iso88026Man
          11: starLan
          12: proteon10Mbit
          13: proteon80Mbit
          14: hyperchannel
          15: fddi
          16: lapb
          17: sdlc
          18: ds1
          19: e1
          20: basicISDN
          21: primaryISDN
          22: propPointToPointSerial
          23: ppp
          24: softwareLoopback
          25: eon
          26: ethernet3Mbit
          27: nsip
          28: slip
          29: ultra
          30: ds3
          31: sip
          32: frameRelay
          33: rs232
          34: para
          35: arcnet
          36: arcnetPlus
          37: atm
          38: miox25
          39: sonet
          40: x25ple
          41: iso88022llc
          42: localTalk
          43: smdsDxi
          44: frameRelayService
          45: v35
          46: hssi
          47: hippi
          48: modem
          49: aal5
          50: sonetPath
          51: sonetVT
          52: smdsIcip
          53: propVirtual
          54: propMultiplexor
          55: ieee80212
          56: fibreChannel
          57: hippiInterface
          58: frameRelayInterconnect
          59: aflane8023
          60: aflane8025
          61: cctEmul
          62: fastEther
          63: isdn
          64: v11
          65: v36
          66: g703at64k
          67: g703at2mb
          68: qllc
          69: fastEtherFX
          70: channel
          71: ieee80211
          72: ibm370parChan
          73: escon
          74: dlsw
          75: isdns
          76: isdnu
          77: lapd
          78: ipSwitch
          79: rsrb
          80: atmLogical
          81: ds0
          82: ds0Bundle
          83: bsc
          84: async
          85: cnr
          86: iso88025Dtr
          87: eplrs
          88: arap
          89: propCnls
          90: hostPad
          91: termPad
          92: frameRelayMPI
          93: x213
          94: adsl
          95: radsl
          96: sdsl
          97: vdsl
          98: iso88025CRFPInt
          99: myrinet
          100: voiceEM
          101: voiceFXO
          102: voiceFXS
          103: voiceEncap
          104: voiceOverIp
          105: atmDxi
          106: atmFuni
          107: atmIma
          108: pppMultilinkBundle
          109: ipOverCdlc
          110: ipOverClaw
          111: stackToStack
          112: virtualIpAddress
          113: mpc
          114: ipOverAtm
          115: iso88025Fiber
          116: tdlc
          117: gigabitEthernet
          118: hdlc
          119: lapf
          120: v37
          121: x25mlp
          122: x25huntGroup
          123: transpHdlc
          124: interleave
          125: fast
          126: ip
          127: docsCableMaclayer
          128: docsCableDownstream
          129: docsCableUpstream
          130: a12MppSwitch
          131: tunnel
          132: coffee
          133: ces
          134: atmSubInterface
          135: l2vlan
          136: l3ipvlan
          137: l3ipxvlan
          138: digitalPowerline
          139: mediaMailOverIp
          140: dtm
          141: dcn
          142: ipForward
          143: msdsl
          144: ieee1394
          145: if-gsn
          146: dvbRccMacLayer
          147: dvbRccDownstream
          148: dvbRccUpstream
          149: atmVirtual
          150: mplsTunnel
          151: srp
          152: voiceOverAtm
          153: voiceOverFrameRelay
          154: idsl
          155: compositeLink
          156: ss7SigLink
          157: propWirelessP2P
          158: frForward
          159: rfc1483
          160: usb
          161: ieee8023adLag
          162: bgppolicyaccounting
          163: frf16MfrBundle
          164: h323Gatekeeper
          165: h323Proxy
          166: mpls
          167: mfSigLink
          168: hdsl2
          169: shdsl
          170: ds1FDL
          171: pos
          172: dvbAsiIn
          173: dvbAsiOut
          174: plc
          175: nfas
          176: tr008
          177: gr303RDT
          178: gr303IDT
          179: isup
          180: propDocsWirelessMaclayer
          181: propDocsWirelessDownstream
          182: propDocsWirelessUpstream
          183: hiperlan2
          184: propBWAp2Mp
          185: sonetOverheadChannel
          186: digitalWrapperOverheadChannel
          187: aal2
          188: radioMAC
          189: atmRadio
          190: imt
          191: mvl
          192: reachDSL
          193: frDlciEndPt
          194: atmVciEndPt
          195: opticalChannel
          196: opticalTransport
          197: propAtm
          198: voiceOverCable
          199: infiniband
          200: teLink
          201: q2931
          202: virtualTg
          203: sipTg
          204: sipSig
          205: docsCableUpstreamChannel
          206: econet
          207: pon155
          208: pon622
          209: bridge
          210: linegroup
          211: voiceEMFGD
          212: voiceFGDEANA
          213: voiceDID
          214: mpegTransport
          215: sixToFour
          216: gtp
          217: pdnEtherLoop1
          218: pdnEtherLoop2
          219: opticalChannelGroup
          220: homepna
          221: gfp
          222: ciscoISLvlan
          223: actelisMetaLOOP
          224: fcipLink
          225: rpr
          226: qam
          227: lmp
          228: cblVectaStar
          229: docsCableMCmtsDownstream
          230: adsl2
          231: macSecControlledIF
          232: macSecUncontrolledIF
          233: aviciOpticalEther
          234: atmbond
          235: voiceFGDOS
          236: mocaVersion1
          237: ieee80216WMAN
          238: adsl2plus
          239: dvbRcsMacLayer
          240: dvbTdm
          241: dvbRcsTdma
          242: x86Laps
          243: wwanPP
          244: wwanPP2
          245: voiceEBS
          246: ifPwType
          247: ilan
          248: pip
          249: aluELP
          250: gpon
          251: vdsl2
          252: capwapDot11Profile
          253: capwapDot11Bss
          254: capwapWtpVirtualRadio
          255: bits
          256: docsCableUpstreamRfPort
          257: cableDownstreamRfPort
          258: vmwareVirtualNic
          259: ieee802154
          260: otnOdu
          261: otnOtu
          262: ifVfiType
          263: g9981
          264: g9982
          265: g9983
          266: aluEpon
          267: aluEponOnu
          268: aluEponPhysicalUni
          269: aluEponLogicalLink
          270: aluGponOnu
          271: aluGponPhysicalUni
          272: vmwareNicTeam
          277: docsOfdmDownstream
          278: docsOfdmaUpstream
          279: gfast
          280: sdci
          281: xboxWireless
          282: fastdsl
          283: docsCableScte55d1FwdOob
          284: docsCableScte55d1RetOob
          285: docsCableScte55d2DsOob
          286: docsCableScte55d2UsOob
          287: docsCableNdf
          288: docsCableNdr
          289: ptm
          290: ghn
          291: otnOtsi
          292: otnOtuc
          293: otnOduc
          294: otnOtsig
          295: microwaveCarrierTermination
          296: microwaveRadioLinkTerminal
          297: ieee8021axDrni
          298: ax25
          299: ieee19061nanocom
          300: cpri
          301: omni
          302: roe
      - name: ifMtu
        oid: *******.*******.1.4
        type: gauge
        help: The size of the largest packet which can be sent/received on the interface,
          specified in octets - *******.*******.1.4
        indexes:
        - labelname: ifIndex
          type: gauge
        lookups:
        - labels:
          - ifIndex
          labelname: ifName
          oid: *******.********.1.1.1
          type: DisplayString
      - name: ifSpeed
        oid: *******.*******.1.5
        type: gauge
        help: An estimate of the interface's current bandwidth in bits per second - *******.*******.1.5
        indexes:
        - labelname: ifIndex
          type: gauge
        lookups:
        - labels:
          - ifIndex
          labelname: ifName
          oid: *******.********.1.1.1
          type: DisplayString
      - name: ifPhysAddress
        oid: *******.*******.1.6
        type: PhysAddress48
        help: The interface's address at its protocol sub-layer - *******.*******.1.6
        indexes:
        - labelname: ifIndex
          type: gauge
        lookups:
        - labels:
          - ifIndex
          labelname: ifName
          oid: *******.********.1.1.1
          type: DisplayString
      - name: ifAdminStatus
        oid: *******.*******.1.7
        type: gauge
        help: The desired state of the interface - *******.*******.1.7
        indexes:
        - labelname: ifIndex
          type: gauge
        lookups:
        - labels:
          - ifIndex
          labelname: ifName
          oid: *******.********.1.1.1
          type: DisplayString
        enum_values:
          1: up
          2: down
          3: testing
      - name: ifOperStatus
        oid: *******.*******.1.8
        type: gauge
        help: The current operational state of the interface - *******.*******.1.8
        indexes:
        - labelname: ifIndex
          type: gauge
        lookups:
        - labels:
          - ifIndex
          labelname: ifName
          oid: *******.********.1.1.1
          type: DisplayString
        enum_values:
          1: up
          2: down
          3: testing
          4: unknown
          5: dormant
          6: notPresent
          7: lowerLayerDown
      - name: ifLastChange
        oid: *******.*******.1.9
        type: gauge
        help: The value of sysUpTime at the time the interface entered its current operational
          state - *******.*******.1.9
        indexes:
        - labelname: ifIndex
          type: gauge
        lookups:
        - labels:
          - ifIndex
          labelname: ifName
          oid: *******.********.1.1.1
          type: DisplayString
      - name: ifInOctets
        oid: *******.*******.1.10
        type: counter
        help: The total number of octets received on the interface, including framing
          characters - *******.*******.1.10
        indexes:
        - labelname: ifIndex
          type: gauge
        lookups:
        - labels:
          - ifIndex
          labelname: ifName
          oid: *******.********.1.1.1
          type: DisplayString
      - name: ifInUcastPkts
        oid: *******.*******.1.11
        type: counter
        help: The number of packets, delivered by this sub-layer to a higher (sub-)layer,
          which were not addressed to a multicast or broadcast address at this sub-layer
          - *******.*******.1.11
        indexes:
        - labelname: ifIndex
          type: gauge
        lookups:
        - labels:
          - ifIndex
          labelname: ifName
          oid: *******.********.1.1.1
          type: DisplayString
      - name: ifInNUcastPkts
        oid: *******.*******.1.12
        type: counter
        help: The number of packets, delivered by this sub-layer to a higher (sub-)layer,
          which were addressed to a multicast or broadcast address at this sub-layer -
          *******.*******.1.12
        indexes:
        - labelname: ifIndex
          type: gauge
        lookups:
        - labels:
          - ifIndex
          labelname: ifName
          oid: *******.********.1.1.1
          type: DisplayString
      - name: ifInDiscards
        oid: *******.*******.1.13
        type: counter
        help: The number of inbound packets which were chosen to be discarded even though
          no errors had been detected to prevent their being deliverable to a higher-layer
          protocol - *******.*******.1.13
        indexes:
        - labelname: ifIndex
          type: gauge
        lookups:
        - labels:
          - ifIndex
          labelname: ifName
          oid: *******.********.1.1.1
          type: DisplayString
      - name: ifInErrors
        oid: *******.*******.1.14
        type: counter
        help: For packet-oriented interfaces, the number of inbound packets that contained
          errors preventing them from being deliverable to a higher-layer protocol - *******.*******.1.14
        indexes:
        - labelname: ifIndex
          type: gauge
        lookups:
        - labels:
          - ifIndex
          labelname: ifName
          oid: *******.********.1.1.1
          type: DisplayString
      - name: ifInUnknownProtos
        oid: *******.*******.1.15
        type: counter
        help: For packet-oriented interfaces, the number of packets received via the interface
          which were discarded because of an unknown or unsupported protocol - *******.*******.1.15
        indexes:
        - labelname: ifIndex
          type: gauge
        lookups:
        - labels:
          - ifIndex
          labelname: ifName
          oid: *******.********.1.1.1
          type: DisplayString
      - name: ifOutOctets
        oid: *******.*******.1.16
        type: counter
        help: The total number of octets transmitted out of the interface, including framing
          characters - *******.*******.1.16
        indexes:
        - labelname: ifIndex
          type: gauge
        lookups:
        - labels:
          - ifIndex
          labelname: ifName
          oid: *******.********.1.1.1
          type: DisplayString
      - name: ifOutUcastPkts
        oid: *******.*******.1.17
        type: counter
        help: The total number of packets that higher-level protocols requested be transmitted,
          and which were not addressed to a multicast or broadcast address at this sub-layer,
          including those that were discarded or not sent - *******.*******.1.17
        indexes:
        - labelname: ifIndex
          type: gauge
        lookups:
        - labels:
          - ifIndex
          labelname: ifName
          oid: *******.********.1.1.1
          type: DisplayString
      - name: ifOutNUcastPkts
        oid: *******.*******.1.18
        type: counter
        help: The total number of packets that higher-level protocols requested be transmitted,
          and which were addressed to a multicast or broadcast address at this sub-layer,
          including those that were discarded or not sent - *******.*******.1.18
        indexes:
        - labelname: ifIndex
          type: gauge
        lookups:
        - labels:
          - ifIndex
          labelname: ifName
          oid: *******.********.1.1.1
          type: DisplayString
      - name: ifOutDiscards
        oid: *******.*******.1.19
        type: counter
        help: The number of outbound packets which were chosen to be discarded even though
          no errors had been detected to prevent their being transmitted - *******.*******.1.19
        indexes:
        - labelname: ifIndex
          type: gauge
        lookups:
        - labels:
          - ifIndex
          labelname: ifName
          oid: *******.********.1.1.1
          type: DisplayString
      - name: ifOutErrors
        oid: *******.*******.1.20
        type: counter
        help: For packet-oriented interfaces, the number of outbound packets that could
          not be transmitted because of errors - *******.*******.1.20
        indexes:
        - labelname: ifIndex
          type: gauge
        lookups:
        - labels:
          - ifIndex
          labelname: ifName
          oid: *******.********.1.1.1
          type: DisplayString
      - name: ifOutQLen
        oid: *******.*******.1.21
        type: gauge
        help: The length of the output packet queue (in packets). - *******.*******.1.21
        indexes:
        - labelname: ifIndex
          type: gauge
        lookups:
        - labels:
          - ifIndex
          labelname: ifName
          oid: *******.********.1.1.1
          type: DisplayString
      - name: ifSpecific
        oid: *******.*******.1.22
        type: OctetString
        help: A reference to MIB definitions specific to the particular media being used
          to realize the interface - *******.*******.1.22
        indexes:
        - labelname: ifIndex
          type: gauge
        lookups:
        - labels:
          - ifIndex
          labelname: ifName
          oid: *******.********.1.1.1
          type: DisplayString
      - name: hrSystemUptime
        oid: *******.********.1
        type: gauge
        help: The amount of time since this host was last initialized - *******.********.1
      - name: hrSystemDate
        oid: *******.********.2
        type: DateAndTime
        help: The host's notion of the local date and time of day. - *******.********.2
      - name: hrSystemInitialLoadDevice
        oid: *******.********.3
        type: gauge
        help: The index of the hrDeviceEntry for the device from which this host is configured
          to load its initial operating system configuration (i.e., which operating system
          code and/or boot parameters) - *******.********.3
      - name: hrSystemInitialLoadParameters
        oid: *******.********.4
        type: OctetString
        help: This object contains the parameters (e.g - *******.********.4
      - name: hrSystemNumUsers
        oid: *******.********.5
        type: gauge
        help: The number of user sessions for which this host is storing state information
          - *******.********.5
      - name: hrSystemProcesses
        oid: *******.********.6
        type: gauge
        help: The number of process contexts currently loaded or running on this system.
          - *******.********.6
      - name: hrSystemMaxProcesses
        oid: *******.********.7
        type: gauge
        help: The maximum number of process contexts this system can support - *******.********.7
      - name: hrMemorySize
        oid: *******.********.2
        type: gauge
        help: The amount of physical read-write main memory, typically RAM, contained
          by the host. - *******.********.2
      - name: hrStorageIndex
        oid: *******.********.3.1.1
        type: gauge
        help: A unique value for each logical storage area contained by the host. - *******.********.3.1.1
        indexes:
        - labelname: hrStorageIndex
          type: gauge
        lookups:
        - labels:
          - hrStorageIndex
          labelname: hrStorageDescr
          oid: *******.********.3.1.3
          type: DisplayString
      - name: hrStorageType
        oid: *******.********.3.1.2
        type: OctetString
        help: The type of storage represented by this entry. - *******.********.3.1.2
        indexes:
        - labelname: hrStorageIndex
          type: gauge
        lookups:
        - labels:
          - hrStorageIndex
          labelname: hrStorageDescr
          oid: *******.********.3.1.3
          type: DisplayString
      - name: hrStorageDescr
        oid: *******.********.3.1.3
        type: DisplayString
        help: A description of the type and instance of the storage described by this
          entry. - *******.********.3.1.3
        indexes:
        - labelname: hrStorageIndex
          type: gauge
        lookups:
        - labels:
          - hrStorageIndex
          labelname: hrStorageDescr
          oid: *******.********.3.1.3
          type: DisplayString
      - name: hrStorageAllocationUnits
        oid: *******.********.3.1.4
        type: gauge
        help: The size, in bytes, of the data objects allocated from this pool - *******.********.3.1.4
        indexes:
        - labelname: hrStorageIndex
          type: gauge
        lookups:
        - labels:
          - hrStorageIndex
          labelname: hrStorageDescr
          oid: *******.********.3.1.3
          type: DisplayString
      - name: hrStorageSize
        oid: *******.********.3.1.5
        type: gauge
        help: The size of the storage represented by this entry, in units of hrStorageAllocationUnits
          - *******.********.3.1.5
        indexes:
        - labelname: hrStorageIndex
          type: gauge
        lookups:
        - labels:
          - hrStorageIndex
          labelname: hrStorageDescr
          oid: *******.********.3.1.3
          type: DisplayString
      - name: hrStorageUsed
        oid: *******.********.3.1.6
        type: gauge
        help: The amount of the storage represented by this entry that is allocated, in
          units of hrStorageAllocationUnits. - *******.********.3.1.6
        indexes:
        - labelname: hrStorageIndex
          type: gauge
        lookups:
        - labels:
          - hrStorageIndex
          labelname: hrStorageDescr
          oid: *******.********.3.1.3
          type: DisplayString
      - name: hrStorageAllocationFailures
        oid: *******.********.3.1.7
        type: counter
        help: The number of requests for storage represented by this entry that could
          not be honored due to not enough storage - *******.********.3.1.7
        indexes:
        - labelname: hrStorageIndex
          type: gauge
        lookups:
        - labels:
          - hrStorageIndex
          labelname: hrStorageDescr
          oid: *******.********.3.1.3
          type: DisplayString
      - name: hrDeviceIndex
        oid: *******.********.2.1.1
        type: gauge
        help: A unique value for each device contained by the host - *******.********.2.1.1
        indexes:
        - labelname: hrDeviceIndex
          type: gauge
      - name: hrDeviceType
        oid: *******.********.2.1.2
        type: OctetString
        help: An indication of the type of device - *******.********.2.1.2
        indexes:
        - labelname: hrDeviceIndex
          type: gauge
      - name: hrDeviceDescr
        oid: *******.********.2.1.3
        type: DisplayString
        help: A textual description of this device, including the device's manufacturer
          and revision, and optionally, its serial number. - *******.********.2.1.3
        indexes:
        - labelname: hrDeviceIndex
          type: gauge
      - name: hrDeviceID
        oid: *******.********.2.1.4
        type: OctetString
        help: The product ID for this device. - *******.********.2.1.4
        indexes:
        - labelname: hrDeviceIndex
          type: gauge
      - name: hrDeviceStatus
        oid: *******.********.2.1.5
        type: gauge
        help: The current operational state of the device described by this row of the
          table - *******.********.2.1.5
        indexes:
        - labelname: hrDeviceIndex
          type: gauge
        enum_values:
          1: unknown
          2: running
          3: warning
          4: testing
          5: down
      - name: hrDeviceErrors
        oid: *******.********.2.1.6
        type: counter
        help: The number of errors detected on this device - *******.********.2.1.6
        indexes:
        - labelname: hrDeviceIndex
          type: gauge
      - name: hrProcessorFrwID
        oid: *******.********.3.1.1
        type: OctetString
        help: The product ID of the firmware associated with the processor. - *******.********.3.1.1
        indexes:
        - labelname: hrDeviceIndex
          type: gauge
      - name: hrProcessorLoad
        oid: *******.********.3.1.2
        type: gauge
        help: The average, over the last minute, of the percentage of time that this processor
          was not idle - *******.********.3.1.2
        indexes:
        - labelname: hrDeviceIndex
          type: gauge
      - name: hrNetworkIfIndex
        oid: *******.********.4.1.1
        type: gauge
        help: The value of ifIndex which corresponds to this network device - *******.********.4.1.1
        indexes:
        - labelname: hrDeviceIndex
          type: gauge
      - name: hrPrinterStatus
        oid: *******.********.5.1.1
        type: gauge
        help: The current status of this printer device. - *******.********.5.1.1
        indexes:
        - labelname: hrDeviceIndex
          type: gauge
        enum_values:
          1: other
          2: unknown
          3: idle
          4: printing
          5: warmup
      - name: hrPrinterDetectedErrorState
        oid: *******.********.5.1.2
        type: OctetString
        help: This object represents any error conditions detected by the printer - *******.********.5.1.2
        indexes:
        - labelname: hrDeviceIndex
          type: gauge
      - name: hrDiskStorageAccess
        oid: *******.********.6.1.1
        type: gauge
        help: An indication if this long-term storage device is readable and writable
          or only readable - *******.********.6.1.1
        indexes:
        - labelname: hrDeviceIndex
          type: gauge
        enum_values:
          1: readWrite
          2: readOnly
      - name: hrDiskStorageMedia
        oid: *******.2.1.25.*******
        type: gauge
        help: An indication of the type of media used in this long- term storage device.
          - *******.2.1.25.*******
        indexes:
        - labelname: hrDeviceIndex
          type: gauge
        enum_values:
          1: other
          2: unknown
          3: hardDisk
          4: floppyDisk
          5: opticalDiskROM
          6: opticalDiskWORM
          7: opticalDiskRW
          8: ramDisk
      - name: hrDiskStorageRemoveble
        oid: *******.********.6.1.3
        type: gauge
        help: Denotes whether or not the disk media may be removed from the drive. - *******.********.6.1.3
        indexes:
        - labelname: hrDeviceIndex
          type: gauge
        enum_values:
          1: "true"
          2: "false"
      - name: hrDiskStorageCapacity
        oid: *******.********.6.1.4
        type: gauge
        help: The total size for this long-term storage device - *******.********.6.1.4
        indexes:
        - labelname: hrDeviceIndex
          type: gauge
      - name: hrPartitionIndex
        oid: *******.********.7.1.1
        type: gauge
        help: A unique value for each partition on this long-term storage device - *******.********.7.1.1
        indexes:
        - labelname: hrDeviceIndex
          type: gauge
        - labelname: hrPartitionIndex
          type: gauge
      - name: hrPartitionLabel
        oid: *******.********.7.1.2
        type: OctetString
        help: A textual description of this partition. - *******.********.7.1.2
        indexes:
        - labelname: hrDeviceIndex
          type: gauge
        - labelname: hrPartitionIndex
          type: gauge
      - name: hrPartitionID
        oid: *******.********.7.1.3
        type: OctetString
        help: A descriptor which uniquely represents this partition to the responsible
          operating system - *******.********.7.1.3
        indexes:
        - labelname: hrDeviceIndex
          type: gauge
        - labelname: hrPartitionIndex
          type: gauge
      - name: hrPartitionSize
        oid: *******.********.7.1.4
        type: gauge
        help: The size of this partition. - *******.********.7.1.4
        indexes:
        - labelname: hrDeviceIndex
          type: gauge
        - labelname: hrPartitionIndex
          type: gauge
      - name: hrPartitionFSIndex
        oid: *******.********.7.1.5
        type: gauge
        help: The index of the file system mounted on this partition - *******.********.7.1.5
        indexes:
        - labelname: hrDeviceIndex
          type: gauge
        - labelname: hrPartitionIndex
          type: gauge
      - name: hrFSIndex
        oid: *******.********.8.1.1
        type: gauge
        help: A unique value for each file system local to this host - *******.********.8.1.1
        indexes:
        - labelname: hrFSIndex
          type: gauge
      - name: hrFSMountPoint
        oid: *******.********.8.1.2
        type: OctetString
        help: The path name of the root of this file system. - *******.********.8.1.2
        indexes:
        - labelname: hrFSIndex
          type: gauge
      - name: hrFSRemoteMountPoint
        oid: *******.********.8.1.3
        type: OctetString
        help: A description of the name and/or address of the server that this file system
          is mounted from - *******.********.8.1.3
        indexes:
        - labelname: hrFSIndex
          type: gauge
      - name: hrFSType
        oid: *******.********.8.1.4
        type: OctetString
        help: The value of this object identifies the type of this file system. - *******.********.8.1.4
        indexes:
        - labelname: hrFSIndex
          type: gauge
      - name: hrFSAccess
        oid: *******.********.8.1.5
        type: gauge
        help: An indication if this file system is logically configured by the operating
          system to be readable and writable or only readable - *******.********.8.1.5
        indexes:
        - labelname: hrFSIndex
          type: gauge
        enum_values:
          1: readWrite
          2: readOnly
      - name: hrFSBootable
        oid: *******.********.8.1.6
        type: gauge
        help: A flag indicating whether this file system is bootable. - *******.********.8.1.6
        indexes:
        - labelname: hrFSIndex
          type: gauge
        enum_values:
          1: "true"
          2: "false"
      - name: hrFSStorageIndex
        oid: *******.********.8.1.7
        type: gauge
        help: The index of the hrStorageEntry that represents information about this file
          system - *******.********.8.1.7
        indexes:
        - labelname: hrFSIndex
          type: gauge
      - name: hrFSLastFullBackupDate
        oid: *******.********.8.1.8
        type: DateAndTime
        help: The last date at which this complete file system was copied to another storage
          device for backup - *******.********.8.1.8
        indexes:
        - labelname: hrFSIndex
          type: gauge
      - name: hrFSLastPartialBackupDate
        oid: *******.********.8.1.9
        type: DateAndTime
        help: The last date at which a portion of this file system was copied to another
          storage device for backup - *******.********.8.1.9
        indexes:
        - labelname: hrFSIndex
          type: gauge
      - name: hrSWOSIndex
        oid: *******.********.1
        type: gauge
        help: The value of the hrSWRunIndex for the hrSWRunEntry that represents the primary
          operating system running on this host - *******.********.1
      - name: hrSWRunIndex
        oid: *******.********.2.1.1
        type: gauge
        help: A unique value for each piece of software running on the host - *******.********.2.1.1
        indexes:
        - labelname: hrSWRunIndex
          type: gauge
      - name: hrSWRunName
        oid: *******.********.2.1.2
        type: OctetString
        help: A textual description of this running piece of software, including the manufacturer,
          revision, and the name by which it is commonly known - *******.********.2.1.2
        indexes:
        - labelname: hrSWRunIndex
          type: gauge
      - name: hrSWRunID
        oid: *******.********.2.1.3
        type: OctetString
        help: The product ID of this running piece of software. - *******.********.2.1.3
        indexes:
        - labelname: hrSWRunIndex
          type: gauge
      - name: hrSWRunPath
        oid: *******.********.2.1.4
        type: OctetString
        help: A description of the location on long-term storage (e.g - *******.********.2.1.4
        indexes:
        - labelname: hrSWRunIndex
          type: gauge
      - name: hrSWRunParameters
        oid: *******.********.2.1.5
        type: OctetString
        help: A description of the parameters supplied to this software when it was initially
          loaded. - *******.********.2.1.5
        indexes:
        - labelname: hrSWRunIndex
          type: gauge
      - name: hrSWRunType
        oid: *******.********.2.1.6
        type: gauge
        help: The type of this software. - *******.********.2.1.6
        indexes:
        - labelname: hrSWRunIndex
          type: gauge
        enum_values:
          1: unknown
          2: operatingSystem
          3: deviceDriver
          4: application
      - name: hrSWRunStatus
        oid: *******.********.2.1.7
        type: gauge
        help: The status of this running piece of software - *******.********.2.1.7
        indexes:
        - labelname: hrSWRunIndex
          type: gauge
        enum_values:
          1: running
          2: runnable
          3: notRunnable
          4: invalid
      - name: hrSWRunPerfCPU
        oid: *******.********.1.1.1
        type: gauge
        help: The number of centi-seconds of the total system's CPU resources consumed
          by this process - *******.********.1.1.1
        indexes:
        - labelname: hrSWRunIndex
          type: gauge
      - name: hrSWRunPerfMem
        oid: *******.********.1.1.2
        type: gauge
        help: The total amount of real system memory allocated to this process. - *******.********.1.1.2
        indexes:
        - labelname: hrSWRunIndex
          type: gauge
      - name: hrSWInstalledLastChange
        oid: *******.********.1
        type: gauge
        help: The value of sysUpTime when an entry in the hrSWInstalledTable was last
          added, renamed, or deleted - *******.********.1
      - name: hrSWInstalledLastUpdateTime
        oid: *******.********.2
        type: gauge
        help: The value of sysUpTime when the hrSWInstalledTable was last completely updated
          - *******.********.2
      - name: hrSWInstalledIndex
        oid: *******.********.3.1.1
        type: gauge
        help: A unique value for each piece of software installed on the host - *******.********.3.1.1
        indexes:
        - labelname: hrSWInstalledIndex
          type: gauge
      - name: hrSWInstalledName
        oid: *******.********.3.1.2
        type: OctetString
        help: A textual description of this installed piece of software, including the
          manufacturer, revision, the name by which it is commonly known, and optionally,
          its serial number. - *******.********.3.1.2
        indexes:
        - labelname: hrSWInstalledIndex
          type: gauge
      - name: hrSWInstalledID
        oid: *******.********.3.1.3
        type: OctetString
        help: The product ID of this installed piece of software. - *******.********.3.1.3
        indexes:
        - labelname: hrSWInstalledIndex
          type: gauge
      - name: hrSWInstalledType
        oid: *******.********.3.1.4
        type: gauge
        help: The type of this software. - *******.********.3.1.4
        indexes:
        - labelname: hrSWInstalledIndex
          type: gauge
        enum_values:
          1: unknown
          2: operatingSystem
          3: deviceDriver
          4: application
      - name: hrSWInstalledDate
        oid: *******.********.3.1.5
        type: DateAndTime
        help: The last-modification date of this application as it would appear in a directory
          listing - *******.********.3.1.5
        indexes:
        - labelname: hrSWInstalledIndex
          type: gauge
      - name: ifInMulticastPkts
        oid: *******.********.1.1.2
        type: counter
        help: The number of packets, delivered by this sub-layer to a higher (sub-)layer,
          which were addressed to a multicast address at this sub-layer - *******.********.1.1.2
        indexes:
        - labelname: ifIndex
          type: gauge
        lookups:
        - labels:
          - ifIndex
          labelname: ifName
          oid: *******.********.1.1.1
          type: DisplayString
      - name: ifInBroadcastPkts
        oid: *******.********.1.1.3
        type: counter
        help: The number of packets, delivered by this sub-layer to a higher (sub-)layer,
          which were addressed to a broadcast address at this sub-layer - *******.********.1.1.3
        indexes:
        - labelname: ifIndex
          type: gauge
        lookups:
        - labels:
          - ifIndex
          labelname: ifName
          oid: *******.********.1.1.1
          type: DisplayString
      - name: ifOutMulticastPkts
        oid: *******.********.1.1.4
        type: counter
        help: The total number of packets that higher-level protocols requested be transmitted,
          and which were addressed to a multicast address at this sub-layer, including
          those that were discarded or not sent - *******.********.1.1.4
        indexes:
        - labelname: ifIndex
          type: gauge
        lookups:
        - labels:
          - ifIndex
          labelname: ifName
          oid: *******.********.1.1.1
          type: DisplayString
      - name: ifOutBroadcastPkts
        oid: *******.********.1.1.5
        type: counter
        help: The total number of packets that higher-level protocols requested be transmitted,
          and which were addressed to a broadcast address at this sub-layer, including
          those that were discarded or not sent - *******.********.1.1.5
        indexes:
        - labelname: ifIndex
          type: gauge
        lookups:
        - labels:
          - ifIndex
          labelname: ifName
          oid: *******.********.1.1.1
          type: DisplayString
      - name: ifHCInOctets
        oid: *******.********.1.1.6
        type: counter
        help: The total number of octets received on the interface, including framing
          characters - *******.********.1.1.6
        indexes:
        - labelname: ifIndex
          type: gauge
        lookups:
        - labels:
          - ifIndex
          labelname: ifName
          oid: *******.********.1.1.1
          type: DisplayString
      - name: ifHCInUcastPkts
        oid: *******.********.1.1.7
        type: counter
        help: The number of packets, delivered by this sub-layer to a higher (sub-)layer,
          which were not addressed to a multicast or broadcast address at this sub-layer
          - *******.********.1.1.7
        indexes:
        - labelname: ifIndex
          type: gauge
        lookups:
        - labels:
          - ifIndex
          labelname: ifName
          oid: *******.********.1.1.1
          type: DisplayString
      - name: ifHCInMulticastPkts
        oid: *******.********.1.1.8
        type: counter
        help: The number of packets, delivered by this sub-layer to a higher (sub-)layer,
          which were addressed to a multicast address at this sub-layer - *******.********.1.1.8
        indexes:
        - labelname: ifIndex
          type: gauge
        lookups:
        - labels:
          - ifIndex
          labelname: ifName
          oid: *******.********.1.1.1
          type: DisplayString
      - name: ifHCInBroadcastPkts
        oid: *******.********.1.1.9
        type: counter
        help: The number of packets, delivered by this sub-layer to a higher (sub-)layer,
          which were addressed to a broadcast address at this sub-layer - *******.********.1.1.9
        indexes:
        - labelname: ifIndex
          type: gauge
        lookups:
        - labels:
          - ifIndex
          labelname: ifName
          oid: *******.********.1.1.1
          type: DisplayString
      - name: ifHCOutOctets
        oid: *******.********.1.1.10
        type: counter
        help: The total number of octets transmitted out of the interface, including framing
          characters - *******.********.1.1.10
        indexes:
        - labelname: ifIndex
          type: gauge
        lookups:
        - labels:
          - ifIndex
          labelname: ifName
          oid: *******.********.1.1.1
          type: DisplayString
      - name: ifHCOutUcastPkts
        oid: *******.********.1.1.11
        type: counter
        help: The total number of packets that higher-level protocols requested be transmitted,
          and which were not addressed to a multicast or broadcast address at this sub-layer,
          including those that were discarded or not sent - *******.********.1.1.11
        indexes:
        - labelname: ifIndex
          type: gauge
        lookups:
        - labels:
          - ifIndex
          labelname: ifName
          oid: *******.********.1.1.1
          type: DisplayString
      - name: ifHCOutMulticastPkts
        oid: *******.********.1.1.12
        type: counter
        help: The total number of packets that higher-level protocols requested be transmitted,
          and which were addressed to a multicast address at this sub-layer, including
          those that were discarded or not sent - *******.********.1.1.12
        indexes:
        - labelname: ifIndex
          type: gauge
        lookups:
        - labels:
          - ifIndex
          labelname: ifName
          oid: *******.********.1.1.1
          type: DisplayString
      - name: ifHCOutBroadcastPkts
        oid: *******.********.1.1.13
        type: counter
        help: The total number of packets that higher-level protocols requested be transmitted,
          and which were addressed to a broadcast address at this sub-layer, including
          those that were discarded or not sent - *******.********.1.1.13
        indexes:
        - labelname: ifIndex
          type: gauge
        lookups:
        - labels:
          - ifIndex
          labelname: ifName
          oid: *******.********.1.1.1
          type: DisplayString
      - name: ifLinkUpDownTrapEnable
        oid: *******.********.1.1.14
        type: gauge
        help: Indicates whether linkUp/linkDown traps should be generated for this interface
          - *******.********.1.1.14
        indexes:
        - labelname: ifIndex
          type: gauge
        lookups:
        - labels:
          - ifIndex
          labelname: ifName
          oid: *******.********.1.1.1
          type: DisplayString
        enum_values:
          1: enabled
          2: disabled
      - name: ifHighSpeed
        oid: *******.********.1.1.15
        type: gauge
        help: An estimate of the interface's current bandwidth in units of 1,000,000 bits
          per second - *******.********.1.1.15
        indexes:
        - labelname: ifIndex
          type: gauge
        lookups:
        - labels:
          - ifIndex
          labelname: ifName
          oid: *******.********.1.1.1
          type: DisplayString
      - name: ifPromiscuousMode
        oid: *******.********.1.1.16
        type: gauge
        help: This object has a value of false(2) if this interface only accepts packets/frames
          that are addressed to this station - *******.********.1.1.16
        indexes:
        - labelname: ifIndex
          type: gauge
        lookups:
        - labels:
          - ifIndex
          labelname: ifName
          oid: *******.********.1.1.1
          type: DisplayString
        enum_values:
          1: "true"
          2: "false"
      - name: ifConnectorPresent
        oid: *******.********.1.1.17
        type: gauge
        help: This object has the value 'true(1)' if the interface sublayer has a physical
          connector and the value 'false(2)' otherwise. - *******.********.1.1.17
        indexes:
        - labelname: ifIndex
          type: gauge
        lookups:
        - labels:
          - ifIndex
          labelname: ifName
          oid: *******.********.1.1.1
          type: DisplayString
        enum_values:
          1: "true"
          2: "false"
      - name: ifAlias
        oid: *******.********.1.1.18
        type: DisplayString
        help: This object is an 'alias' name for the interface as specified by a network
          manager, and provides a non-volatile 'handle' for the interface - *******.********.1.1.18
        indexes:
        - labelname: ifIndex
          type: gauge
        lookups:
        - labels:
          - ifIndex
          labelname: ifName
          oid: *******.********.1.1.1
          type: DisplayString
      - name: ifCounterDiscontinuityTime
        oid: *******.********.1.1.19
        type: gauge
        help: The value of sysUpTime on the most recent occasion at which any one or more
          of this interface's counters suffered a discontinuity - *******.********.1.1.19
        indexes:
        - labelname: ifIndex
          type: gauge
        lookups:
        - labels:
          - ifIndex
          labelname: ifName
          oid: *******.********.1.1.1
          type: DisplayString
      - name: ifStackHigherLayer
        oid: *******.********.2.1.1
        type: gauge
        help: The value of ifIndex corresponding to the higher sub-layer of the relationship,
          i.e., the sub-layer which runs on 'top' of the sub-layer identified by the corresponding
          instance of ifStackLowerLayer - *******.********.2.1.1
        indexes:
        - labelname: ifStackHigherLayer
          type: gauge
        - labelname: ifStackLowerLayer
          type: gauge
      - name: ifStackLowerLayer
        oid: *******.********.2.1.2
        type: gauge
        help: The value of ifIndex corresponding to the lower sub-layer of the relationship,
          i.e., the sub-layer which runs 'below' the sub-layer identified by the corresponding
          instance of ifStackHigherLayer - *******.********.2.1.2
        indexes:
        - labelname: ifStackHigherLayer
          type: gauge
        - labelname: ifStackLowerLayer
          type: gauge
      - name: ifStackStatus
        oid: *******.********.2.1.3
        type: gauge
        help: The status of the relationship between two sub-layers - *******.********.2.1.3
        indexes:
        - labelname: ifStackHigherLayer
          type: gauge
        - labelname: ifStackLowerLayer
          type: gauge
        enum_values:
          1: active
          2: notInService
          3: notReady
          4: createAndGo
          5: createAndWait
          6: destroy
      - name: ifTestId
        oid: *******.********.3.1.1
        type: gauge
        help: This object identifies the current invocation of the interface's test. -
          *******.********.3.1.1
        indexes:
        - labelname: ifIndex
          type: gauge
        lookups:
        - labels:
          - ifIndex
          labelname: ifName
          oid: *******.********.1.1.1
          type: DisplayString
      - name: ifTestStatus
        oid: *******.********.3.1.2
        type: gauge
        help: This object indicates whether or not some manager currently has the necessary
          'ownership' required to invoke a test on this interface - *******.********.3.1.2
        indexes:
        - labelname: ifIndex
          type: gauge
        lookups:
        - labels:
          - ifIndex
          labelname: ifName
          oid: *******.********.1.1.1
          type: DisplayString
        enum_values:
          1: notInUse
          2: inUse
      - name: ifTestType
        oid: *******.********.3.1.3
        type: OctetString
        help: A control variable used to start and stop operator- initiated interface
          tests - *******.********.3.1.3
        indexes:
        - labelname: ifIndex
          type: gauge
        lookups:
        - labels:
          - ifIndex
          labelname: ifName
          oid: *******.********.1.1.1
          type: DisplayString
      - name: ifTestResult
        oid: *******.********.3.1.4
        type: gauge
        help: This object contains the result of the most recently requested test, or
          the value none(1) if no tests have been requested since the last reset - *******.********.3.1.4
        indexes:
        - labelname: ifIndex
          type: gauge
        lookups:
        - labels:
          - ifIndex
          labelname: ifName
          oid: *******.********.1.1.1
          type: DisplayString
        enum_values:
          1: none
          2: success
          3: inProgress
          4: notSupported
          5: unAbleToRun
          6: aborted
          7: failed
      - name: ifTestCode
        oid: *******.********.3.1.5
        type: OctetString
        help: This object contains a code which contains more specific information on
          the test result, for example an error-code after a failed test - *******.********.3.1.5
        indexes:
        - labelname: ifIndex
          type: gauge
        lookups:
        - labels:
          - ifIndex
          labelname: ifName
          oid: *******.********.1.1.1
          type: DisplayString
      - name: ifTestOwner
        oid: *******.********.3.1.6
        type: DisplayString
        help: The entity which currently has the 'ownership' required to invoke a test
          on this interface. - *******.********.3.1.6
        indexes:
        - labelname: ifIndex
          type: gauge
        lookups:
        - labels:
          - ifIndex
          labelname: ifName
          oid: *******.********.1.1.1
          type: DisplayString
      - name: ifRcvAddressAddress
        oid: *******.********.4.1.1
        type: PhysAddress48
        help: An address for which the system will accept packets/frames on this entry's
          interface. - *******.********.4.1.1
        indexes:
        - labelname: ifIndex
          type: gauge
        - labelname: ifRcvAddressAddress
          type: PhysAddress48
        lookups:
        - labels:
          - ifIndex
          labelname: ifName
          oid: *******.********.1.1.1
          type: DisplayString
      - name: ifRcvAddressStatus
        oid: *******.********.4.1.2
        type: gauge
        help: This object is used to create and delete rows in the ifRcvAddressTable.
          - *******.********.4.1.2
        indexes:
        - labelname: ifIndex
          type: gauge
        - labelname: ifRcvAddressAddress
          type: PhysAddress48
        lookups:
        - labels:
          - ifIndex
          labelname: ifName
          oid: *******.********.1.1.1
          type: DisplayString
        enum_values:
          1: active
          2: notInService
          3: notReady
          4: createAndGo
          5: createAndWait
          6: destroy
      - name: ifRcvAddressType
        oid: *******.********.4.1.3
        type: gauge
        help: This object has the value nonVolatile(3) for those entries in the table
          which are valid and will not be deleted by the next restart of the managed system
          - *******.********.4.1.3
        indexes:
        - labelname: ifIndex
          type: gauge
        - labelname: ifRcvAddressAddress
          type: PhysAddress48
        lookups:
        - labels:
          - ifIndex
          labelname: ifName
          oid: *******.********.1.1.1
          type: DisplayString
        enum_values:
          1: other
          2: volatile
          3: nonVolatile
      - name: ifTableLastChange
        oid: *******.********.5
        type: gauge
        help: The value of sysUpTime at the time of the last creation or deletion of an
          entry in the ifTable - *******.********.5
      - name: ifStackLastChange
        oid: *******.********.6
        type: gauge
        help: The value of sysUpTime at the time of the last change of the (whole) interface
          stack - *******.********.6
      - name: mtxrWlStatIndex
        oid: *******.4.1.14988.*******.1.1
        type: gauge
        help: ' - *******.4.1.14988.*******.1.1'
        indexes:
        - labelname: mtxrWlStatIndex
          type: gauge
      - name: mtxrWlStatTxRate
        oid: *******.4.1.14988.*******.1.2
        type: gauge
        help: bits per second - *******.4.1.14988.*******.1.2
        indexes:
        - labelname: mtxrWlStatIndex
          type: gauge
      - name: mtxrWlStatRxRate
        oid: *******.4.1.14988.*******.1.3
        type: gauge
        help: bits per second - *******.4.1.14988.*******.1.3
        indexes:
        - labelname: mtxrWlStatIndex
          type: gauge
      - name: mtxrWlStatStrength
        oid: *******.4.1.14988.*******.1.4
        type: gauge
        help: dBm - *******.4.1.14988.*******.1.4
        indexes:
        - labelname: mtxrWlStatIndex
          type: gauge
      - name: mtxrWlStatSsid
        oid: *******.4.1.14988.*******.1.5
        type: DisplayString
        help: ' - *******.4.1.14988.*******.1.5'
        indexes:
        - labelname: mtxrWlStatIndex
          type: gauge
      - name: mtxrWlStatBssid
        oid: *******.4.1.14988.*******.1.6
        type: PhysAddress48
        help: ' - *******.4.1.14988.*******.1.6'
        indexes:
        - labelname: mtxrWlStatIndex
          type: gauge
      - name: mtxrWlStatFreq
        oid: *******.4.1.14988.*******.1.7
        type: gauge
        help: megahertz - *******.4.1.14988.*******.1.7
        indexes:
        - labelname: mtxrWlStatIndex
          type: gauge
      - name: mtxrWlStatBand
        oid: *******.4.1.14988.*******.1.8
        type: DisplayString
        help: ' - *******.4.1.14988.*******.1.8'
        indexes:
        - labelname: mtxrWlStatIndex
          type: gauge
      - name: mtxrWlStatTxCCQ
        oid: *******.4.1.14988.1.1.*******
        type: counter
        help: ' - *******.4.1.14988.1.1.*******'
        indexes:
        - labelname: mtxrWlStatIndex
          type: gauge
      - name: mtxrWlStatRxCCQ
        oid: *******.4.1.14988.*******.1.10
        type: counter
        help: ' - *******.4.1.14988.*******.1.10'
        indexes:
        - labelname: mtxrWlStatIndex
          type: gauge
      - name: mtxrWlRtabAddr
        oid: *******.4.1.14988.1.*******.1
        type: PhysAddress48
        help: ' - *******.4.1.14988.1.*******.1'
        indexes:
        - labelname: mtxrWlRtabAddr
          type: PhysAddress48
          fixed_size: 6
        - labelname: mtxrWlRtabIface
          type: gauge
      - name: mtxrWlRtabIface
        oid: *******.4.1.14988.1.*******.2
        type: gauge
        help: ' - *******.4.1.14988.1.*******.2'
        indexes:
        - labelname: mtxrWlRtabAddr
          type: PhysAddress48
          fixed_size: 6
        - labelname: mtxrWlRtabIface
          type: gauge
      - name: mtxrWlRtabStrength
        oid: *******.4.1.14988.1.*******.3
        type: gauge
        help: dBm - *******.4.1.14988.1.*******.3
        indexes:
        - labelname: mtxrWlRtabAddr
          type: PhysAddress48
          fixed_size: 6
        - labelname: mtxrWlRtabIface
          type: gauge
      - name: mtxrWlRtabTxBytes
        oid: *******.4.1.14988.1.*******.4
        type: counter
        help: ' - *******.4.1.14988.1.*******.4'
        indexes:
        - labelname: mtxrWlRtabAddr
          type: PhysAddress48
          fixed_size: 6
        - labelname: mtxrWlRtabIface
          type: gauge
      - name: mtxrWlRtabRxBytes
        oid: *******.4.1.14988.1.*******.5
        type: counter
        help: ' - *******.4.1.14988.1.*******.5'
        indexes:
        - labelname: mtxrWlRtabAddr
          type: PhysAddress48
          fixed_size: 6
        - labelname: mtxrWlRtabIface
          type: gauge
      - name: mtxrWlRtabTxPackets
        oid: *******.4.1.14988.1.*******.6
        type: counter
        help: ' - *******.4.1.14988.1.*******.6'
        indexes:
        - labelname: mtxrWlRtabAddr
          type: PhysAddress48
          fixed_size: 6
        - labelname: mtxrWlRtabIface
          type: gauge
      - name: mtxrWlRtabRxPackets
        oid: *******.4.1.14988.1.*******.7
        type: counter
        help: ' - *******.4.1.14988.1.*******.7'
        indexes:
        - labelname: mtxrWlRtabAddr
          type: PhysAddress48
          fixed_size: 6
        - labelname: mtxrWlRtabIface
          type: gauge
      - name: mtxrWlRtabTxRate
        oid: *******.4.1.14988.1.*******.8
        type: gauge
        help: bits per second - *******.4.1.14988.1.*******.8
        indexes:
        - labelname: mtxrWlRtabAddr
          type: PhysAddress48
          fixed_size: 6
        - labelname: mtxrWlRtabIface
          type: gauge
      - name: mtxrWlRtabRxRate
        oid: *******.4.1.14988.1.*******.9
        type: gauge
        help: bits per second - *******.4.1.14988.1.*******.9
        indexes:
        - labelname: mtxrWlRtabAddr
          type: PhysAddress48
          fixed_size: 6
        - labelname: mtxrWlRtabIface
          type: gauge
      - name: mtxrWlRtabRouterOSVersion
        oid: *******.4.1.14988.1.*******.10
        type: DisplayString
        help: RouterOS version - *******.4.1.14988.1.*******.10
        indexes:
        - labelname: mtxrWlRtabAddr
          type: PhysAddress48
          fixed_size: 6
        - labelname: mtxrWlRtabIface
          type: gauge
      - name: mtxrWlRtabUptime
        oid: *******.4.1.14988.1.*******.11
        type: gauge
        help: uptime - *******.4.1.14988.1.*******.11
        indexes:
        - labelname: mtxrWlRtabAddr
          type: PhysAddress48
          fixed_size: 6
        - labelname: mtxrWlRtabIface
          type: gauge
      - name: mtxrWlRtabSignalToNoise
        oid: *******.4.1.14988.1.*******.12
        type: gauge
        help: Measured in dB, if value does not exist it is indicated with 0 - *******.4.1.14988.1.*******.12
        indexes:
        - labelname: mtxrWlRtabAddr
          type: PhysAddress48
          fixed_size: 6
        - labelname: mtxrWlRtabIface
          type: gauge
      - name: mtxrWlRtabTxStrengthCh0
        oid: *******.4.1.14988.1.*******.13
        type: gauge
        help: ' - *******.4.1.14988.1.*******.13'
        indexes:
        - labelname: mtxrWlRtabAddr
          type: PhysAddress48
          fixed_size: 6
        - labelname: mtxrWlRtabIface
          type: gauge
      - name: mtxrWlRtabRxStrengthCh0
        oid: *******.4.1.14988.1.*******.14
        type: gauge
        help: ' - *******.4.1.14988.1.*******.14'
        indexes:
        - labelname: mtxrWlRtabAddr
          type: PhysAddress48
          fixed_size: 6
        - labelname: mtxrWlRtabIface
          type: gauge
      - name: mtxrWlRtabTxStrengthCh1
        oid: *******.4.1.14988.1.*******.15
        type: gauge
        help: ' - *******.4.1.14988.1.*******.15'
        indexes:
        - labelname: mtxrWlRtabAddr
          type: PhysAddress48
          fixed_size: 6
        - labelname: mtxrWlRtabIface
          type: gauge
      - name: mtxrWlRtabRxStrengthCh1
        oid: *******.4.1.14988.1.*******.16
        type: gauge
        help: ' - *******.4.1.14988.1.*******.16'
        indexes:
        - labelname: mtxrWlRtabAddr
          type: PhysAddress48
          fixed_size: 6
        - labelname: mtxrWlRtabIface
          type: gauge
      - name: mtxrWlRtabTxStrengthCh2
        oid: *******.4.1.14988.1.*******.17
        type: gauge
        help: ' - *******.4.1.14988.1.*******.17'
        indexes:
        - labelname: mtxrWlRtabAddr
          type: PhysAddress48
          fixed_size: 6
        - labelname: mtxrWlRtabIface
          type: gauge
      - name: mtxrWlRtabRxStrengthCh2
        oid: *******.4.1.14988.1.*******.18
        type: gauge
        help: ' - *******.4.1.14988.1.*******.18'
        indexes:
        - labelname: mtxrWlRtabAddr
          type: PhysAddress48
          fixed_size: 6
        - labelname: mtxrWlRtabIface
          type: gauge
      - name: mtxrWlRtabTxStrength
        oid: *******.4.1.14988.1.*******.19
        type: gauge
        help: ' - *******.4.1.14988.1.*******.19'
        indexes:
        - labelname: mtxrWlRtabAddr
          type: PhysAddress48
          fixed_size: 6
        - labelname: mtxrWlRtabIface
          type: gauge
      - name: mtxrWlRtabRadioName
        oid: *******.4.1.14988.1.*******.20
        type: DisplayString
        help: ' - *******.4.1.14988.1.*******.20'
        indexes:
        - labelname: mtxrWlRtabAddr
          type: PhysAddress48
          fixed_size: 6
        - labelname: mtxrWlRtabIface
          type: gauge
      - name: mtxrWlApIndex
        oid: *******.4.1.14988.*******.1.1
        type: gauge
        help: ' - *******.4.1.14988.*******.1.1'
        indexes:
        - labelname: mtxrWlApIndex
          type: gauge
      - name: mtxrWlApTxRate
        oid: *******.4.1.14988.*******.1.2
        type: gauge
        help: bits per second - *******.4.1.14988.*******.1.2
        indexes:
        - labelname: mtxrWlApIndex
          type: gauge
      - name: mtxrWlApRxRate
        oid: *******.4.1.14988.*******.1.3
        type: gauge
        help: bits per second - *******.4.1.14988.*******.1.3
        indexes:
        - labelname: mtxrWlApIndex
          type: gauge
      - name: mtxrWlApSsid
        oid: *******.4.1.14988.*******.1.4
        type: DisplayString
        help: ' - *******.4.1.14988.*******.1.4'
        indexes:
        - labelname: mtxrWlApIndex
          type: gauge
      - name: mtxrWlApBssid
        oid: *******.4.1.14988.*******.1.5
        type: PhysAddress48
        help: ' - *******.4.1.14988.*******.1.5'
        indexes:
        - labelname: mtxrWlApIndex
          type: gauge
      - name: mtxrWlApClientCount
        oid: *******.4.1.14988.*******.1.6
        type: counter
        help: ' - *******.4.1.14988.*******.1.6'
        indexes:
        - labelname: mtxrWlApIndex
          type: gauge
      - name: mtxrWlApFreq
        oid: *******.4.1.14988.*******.1.7
        type: gauge
        help: megahertz - *******.4.1.14988.*******.1.7
        indexes:
        - labelname: mtxrWlApIndex
          type: gauge
      - name: mtxrWlApBand
        oid: *******.4.1.14988.*******.1.8
        type: DisplayString
        help: ' - *******.4.1.14988.*******.1.8'
        indexes:
        - labelname: mtxrWlApIndex
          type: gauge
      - name: mtxrWlApNoiseFloor
        oid: *******.4.1.14988.*******.1.9
        type: gauge
        help: ' - *******.4.1.14988.*******.1.9'
        indexes:
        - labelname: mtxrWlApIndex
          type: gauge
      - name: mtxrWlApOverallTxCCQ
        oid: *******.4.1.14988.*******.1.10
        type: counter
        help: ' - *******.4.1.14988.*******.1.10'
        indexes:
        - labelname: mtxrWlApIndex
          type: gauge
      - name: mtxrWlApAuthClientCount
        oid: *******.4.1.14988.*******.1.11
        type: counter
        help: ' - *******.4.1.14988.*******.1.11'
        indexes:
        - labelname: mtxrWlApIndex
          type: gauge
      - name: mtxrWlRtabEntryCount
        oid: *******.4.1.14988.*******
        type: gauge
        help: Wireless registration table entry count - *******.4.1.14988.*******
      - name: mtxrWlCMRtabAddr
        oid: *******.4.1.14988.1.*******.1
        type: PhysAddress48
        help: ' - *******.4.1.14988.1.*******.1'
        indexes:
        - labelname: mtxrWlCMRtabAddr
          type: PhysAddress48
          fixed_size: 6
        - labelname: mtxrWlCMRtabIface
          type: gauge
      - name: mtxrWlCMRtabIface
        oid: *******.4.1.14988.1.*******.2
        type: gauge
        help: ' - *******.4.1.14988.1.*******.2'
        indexes:
        - labelname: mtxrWlCMRtabAddr
          type: PhysAddress48
          fixed_size: 6
        - labelname: mtxrWlCMRtabIface
          type: gauge
      - name: mtxrWlCMRtabUptime
        oid: *******.4.1.14988.1.*******.3
        type: gauge
        help: uptime - *******.4.1.14988.1.*******.3
        indexes:
        - labelname: mtxrWlCMRtabAddr
          type: PhysAddress48
          fixed_size: 6
        - labelname: mtxrWlCMRtabIface
          type: gauge
      - name: mtxrWlCMRtabTxBytes
        oid: *******.4.1.14988.1.*******.4
        type: counter
        help: ' - *******.4.1.14988.1.*******.4'
        indexes:
        - labelname: mtxrWlCMRtabAddr
          type: PhysAddress48
          fixed_size: 6
        - labelname: mtxrWlCMRtabIface
          type: gauge
      - name: mtxrWlCMRtabRxBytes
        oid: *******.4.1.14988.1.*******.5
        type: counter
        help: ' - *******.4.1.14988.1.*******.5'
        indexes:
        - labelname: mtxrWlCMRtabAddr
          type: PhysAddress48
          fixed_size: 6
        - labelname: mtxrWlCMRtabIface
          type: gauge
      - name: mtxrWlCMRtabTxPackets
        oid: *******.4.1.14988.1.*******.6
        type: counter
        help: ' - *******.4.1.14988.1.*******.6'
        indexes:
        - labelname: mtxrWlCMRtabAddr
          type: PhysAddress48
          fixed_size: 6
        - labelname: mtxrWlCMRtabIface
          type: gauge
      - name: mtxrWlCMRtabRxPackets
        oid: *******.4.1.14988.1.*******.7
        type: counter
        help: ' - *******.4.1.14988.1.*******.7'
        indexes:
        - labelname: mtxrWlCMRtabAddr
          type: PhysAddress48
          fixed_size: 6
        - labelname: mtxrWlCMRtabIface
          type: gauge
      - name: mtxrWlCMRtabTxRate
        oid: *******.4.1.14988.1.*******.8
        type: gauge
        help: bits per second - *******.4.1.14988.1.*******.8
        indexes:
        - labelname: mtxrWlCMRtabAddr
          type: PhysAddress48
          fixed_size: 6
        - labelname: mtxrWlCMRtabIface
          type: gauge
      - name: mtxrWlCMRtabRxRate
        oid: *******.4.1.14988.1.*******.9
        type: gauge
        help: bits per second - *******.4.1.14988.1.*******.9
        indexes:
        - labelname: mtxrWlCMRtabAddr
          type: PhysAddress48
          fixed_size: 6
        - labelname: mtxrWlCMRtabIface
          type: gauge
      - name: mtxrWlCMRtabTxStrength
        oid: *******.4.1.14988.1.*******.10
        type: gauge
        help: ' - *******.4.1.14988.1.*******.10'
        indexes:
        - labelname: mtxrWlCMRtabAddr
          type: PhysAddress48
          fixed_size: 6
        - labelname: mtxrWlCMRtabIface
          type: gauge
      - name: mtxrWlCMRtabRxStrength
        oid: *******.4.1.14988.1.*******.11
        type: gauge
        help: ' - *******.4.1.14988.1.*******.11'
        indexes:
        - labelname: mtxrWlCMRtabAddr
          type: PhysAddress48
          fixed_size: 6
        - labelname: mtxrWlCMRtabIface
          type: gauge
      - name: mtxrWlCMRtabSsid
        oid: *******.4.1.14988.1.*******.12
        type: DisplayString
        help: ' - *******.4.1.14988.1.*******.12'
        indexes:
        - labelname: mtxrWlCMRtabAddr
          type: PhysAddress48
          fixed_size: 6
        - labelname: mtxrWlCMRtabIface
          type: gauge
      - name: mtxrWlCMRtabEapIdent
        oid: *******.4.1.14988.1.*******.13
        type: DisplayString
        help: ' - *******.4.1.14988.1.*******.13'
        indexes:
        - labelname: mtxrWlCMRtabAddr
          type: PhysAddress48
          fixed_size: 6
        - labelname: mtxrWlCMRtabIface
          type: gauge
      - name: mtxrWlCMRtabEntryCount
        oid: *******.4.1.14988.*******
        type: gauge
        help: Wireless CAPSMAN registration table entry count - *******.4.1.14988.*******
      - name: mtxrWlCMIndex
        oid: *******.4.1.14988.*******.1.1
        type: gauge
        help: ' - *******.4.1.14988.*******.1.1'
        indexes:
        - labelname: mtxrWlCMIndex
          type: gauge
      - name: mtxrWlCMRegClientCount
        oid: *******.4.1.14988.*******.1.2
        type: counter
        help: ' - *******.4.1.14988.*******.1.2'
        indexes:
        - labelname: mtxrWlCMIndex
          type: gauge
      - name: mtxrWlCMAuthClientCount
        oid: *******.4.1.14988.*******.1.3
        type: counter
        help: ' - *******.4.1.14988.*******.1.3'
        indexes:
        - labelname: mtxrWlCMIndex
          type: gauge
      - name: mtxrWlCMState
        oid: *******.4.1.14988.*******.1.4
        type: DisplayString
        help: ' - *******.4.1.14988.*******.1.4'
        indexes:
        - labelname: mtxrWlCMIndex
          type: gauge
      - name: mtxrWlCMChannel
        oid: *******.4.1.14988.*******.1.5
        type: DisplayString
        help: for master only - *******.4.1.14988.*******.1.5
        indexes:
        - labelname: mtxrWlCMIndex
          type: gauge
      - name: mtxrWl60GIndex
        oid: *******.4.1.14988.1.*******.1
        type: gauge
        help: ' - *******.4.1.14988.1.*******.1'
        indexes:
        - labelname: mtxrWl60GIndex
          type: gauge
      - name: mtxrWl60GMode
        oid: *******.4.1.14988.1.*******.2
        type: gauge
        help: ' - *******.4.1.14988.1.*******.2'
        indexes:
        - labelname: mtxrWl60GIndex
          type: gauge
        enum_values:
          0: apBridge
          1: stationBridge
          2: sniff
          3: bridge
      - name: mtxrWl60GSsid
        oid: *******.4.1.14988.1.*******.3
        type: DisplayString
        help: ' - *******.4.1.14988.1.*******.3'
        indexes:
        - labelname: mtxrWl60GIndex
          type: gauge
      - name: mtxrWl60GConnected
        oid: *******.4.1.14988.1.*******.4
        type: gauge
        help: ' - *******.4.1.14988.1.*******.4'
        indexes:
        - labelname: mtxrWl60GIndex
          type: gauge
        enum_values:
          0: "false"
          1: "true"
      - name: mtxrWl60GRemote
        oid: *******.4.1.14988.1.*******.5
        type: PhysAddress48
        help: ' - *******.4.1.14988.1.*******.5'
        indexes:
        - labelname: mtxrWl60GIndex
          type: gauge
      - name: mtxrWl60GFreq
        oid: *******.4.1.14988.1.*******.6
        type: gauge
        help: Mhz - *******.4.1.14988.1.*******.6
        indexes:
        - labelname: mtxrWl60GIndex
          type: gauge
      - name: mtxrWl60GMcs
        oid: *******.4.1.14988.1.*******.7
        type: gauge
        help: ' - *******.4.1.14988.1.*******.7'
        indexes:
        - labelname: mtxrWl60GIndex
          type: gauge
      - name: mtxrWl60GSignal
        oid: *******.4.1.14988.1.*******.8
        type: gauge
        help: ' - *******.4.1.14988.1.*******.8'
        indexes:
        - labelname: mtxrWl60GIndex
          type: gauge
      - name: mtxrWl60GTxSector
        oid: *******.4.1.14988.1.*******.9
        type: gauge
        help: ' - *******.4.1.14988.1.*******.9'
        indexes:
        - labelname: mtxrWl60GIndex
          type: gauge
      - name: mtxrWl60GTxSectorInfo
        oid: *******.4.1.14988.1.*******.11
        type: DisplayString
        help: ' - *******.4.1.14988.1.*******.11'
        indexes:
        - labelname: mtxrWl60GIndex
          type: gauge
      - name: mtxrWl60GRssi
        oid: *******.4.1.14988.1.*******.12
        type: gauge
        help: ' - *******.4.1.14988.1.*******.12'
        indexes:
        - labelname: mtxrWl60GIndex
          type: gauge
      - name: mtxrWl60GPhyRate
        oid: *******.4.1.14988.1.*******.13
        type: gauge
        help: ' - *******.4.1.14988.1.*******.13'
        indexes:
        - labelname: mtxrWl60GIndex
          type: gauge
      - name: mtxrWl60GStaIndex
        oid: *******.4.1.14988.*******.1.1
        type: gauge
        help: ' - *******.4.1.14988.*******.1.1'
        indexes:
        - labelname: mtxrWl60GStaIndex
          type: gauge
      - name: mtxrWl60GStaConnected
        oid: *******.4.1.14988.*******.1.2
        type: gauge
        help: ' - *******.4.1.14988.*******.1.2'
        indexes:
        - labelname: mtxrWl60GStaIndex
          type: gauge
        enum_values:
          0: "false"
          1: "true"
      - name: mtxrWl60GStaRemote
        oid: *******.4.1.14988.*******.1.3
        type: PhysAddress48
        help: ' - *******.4.1.14988.*******.1.3'
        indexes:
        - labelname: mtxrWl60GStaIndex
          type: gauge
      - name: mtxrWl60GStaMcs
        oid: *******.4.1.14988.*******.1.4
        type: gauge
        help: ' - *******.4.1.14988.*******.1.4'
        indexes:
        - labelname: mtxrWl60GStaIndex
          type: gauge
      - name: mtxrWl60GStaSignal
        oid: *******.4.1.14988.*******.1.5
        type: gauge
        help: ' - *******.4.1.14988.*******.1.5'
        indexes:
        - labelname: mtxrWl60GStaIndex
          type: gauge
      - name: mtxrWl60GStaTxSector
        oid: *******.4.1.14988.*******.1.6
        type: gauge
        help: ' - *******.4.1.14988.*******.1.6'
        indexes:
        - labelname: mtxrWl60GStaIndex
          type: gauge
      - name: mtxrWl60GStaPhyRate
        oid: *******.4.1.14988.*******.1.8
        type: gauge
        help: Mbits per second - *******.4.1.14988.*******.1.8
        indexes:
        - labelname: mtxrWl60GStaIndex
          type: gauge
      - name: mtxrWl60GStaRssi
        oid: *******.4.1.14988.*******.1.9
        type: gauge
        help: ' - *******.4.1.14988.*******.1.9'
        indexes:
        - labelname: mtxrWl60GStaIndex
          type: gauge
      - name: mtxrWl60GStaDistance
        oid: *******.4.1.14988.*******.1.10
        type: gauge
        help: meters - *******.4.1.14988.*******.1.10
        indexes:
        - labelname: mtxrWl60GStaIndex
          type: gauge
      - name: mtxrWlCMREntryCount
        oid: *******.4.1.14988.*******0
        type: gauge
        help: Wireless CAPSMAN remote-cap entry count - *******.4.1.14988.*******0
      - name: mtxrWlCMRemoteIndex
        oid: *******.4.1.14988.********.1.1
        type: gauge
        help: ' - *******.4.1.14988.********.1.1'
        indexes:
        - labelname: mtxrWlCMRemoteIndex
          type: gauge
      - name: mtxrWlCMRemoteName
        oid: *******.4.1.14988.********.1.2
        type: DisplayString
        help: ' - *******.4.1.14988.********.1.2'
        indexes:
        - labelname: mtxrWlCMRemoteIndex
          type: gauge
      - name: mtxrWlCMRemoteState
        oid: *******.4.1.14988.********.1.3
        type: DisplayString
        help: ' - *******.4.1.14988.********.1.3'
        indexes:
        - labelname: mtxrWlCMRemoteIndex
          type: gauge
      - name: mtxrWlCMRemoteAddress
        oid: *******.4.1.14988.********.1.4
        type: DisplayString
        help: ' - *******.4.1.14988.********.1.4'
        indexes:
        - labelname: mtxrWlCMRemoteIndex
          type: gauge
      - name: mtxrWlCMRemoteRadios
        oid: *******.4.1.14988.********.1.5
        type: counter
        help: ' - *******.4.1.14988.********.1.5'
        indexes:
        - labelname: mtxrWlCMRemoteIndex
          type: gauge
      - name: mtxrQueueSimpleIndex
        oid: *******.4.1.14988.*******.1.1
        type: gauge
        help: ' - *******.4.1.14988.*******.1.1'
        indexes:
        - labelname: mtxrQueueSimpleIndex
          type: gauge
      - name: mtxrQueueSimpleName
        oid: *******.4.1.14988.*******.1.2
        type: DisplayString
        help: ' - *******.4.1.14988.*******.1.2'
        indexes:
        - labelname: mtxrQueueSimpleIndex
          type: gauge
      - name: mtxrQueueSimpleSrcAddr
        oid: *******.4.1.14988.*******.1.3
        type: InetAddressIPv4
        help: ' - *******.4.1.14988.*******.1.3'
        indexes:
        - labelname: mtxrQueueSimpleIndex
          type: gauge
      - name: mtxrQueueSimpleSrcMask
        oid: *******.4.1.14988.*******.1.4
        type: InetAddressIPv4
        help: ' - *******.4.1.14988.*******.1.4'
        indexes:
        - labelname: mtxrQueueSimpleIndex
          type: gauge
      - name: mtxrQueueSimpleDstAddr
        oid: *******.4.1.14988.*******.1.5
        type: InetAddressIPv4
        help: ' - *******.4.1.14988.*******.1.5'
        indexes:
        - labelname: mtxrQueueSimpleIndex
          type: gauge
      - name: mtxrQueueSimpleDstMask
        oid: *******.4.1.14988.*******.1.6
        type: InetAddressIPv4
        help: ' - *******.4.1.14988.*******.1.6'
        indexes:
        - labelname: mtxrQueueSimpleIndex
          type: gauge
      - name: mtxrQueueSimpleIface
        oid: *******.4.1.14988.*******.1.7
        type: gauge
        help: interface index - *******.4.1.14988.*******.1.7
        indexes:
        - labelname: mtxrQueueSimpleIndex
          type: gauge
      - name: mtxrQueueSimpleBytesIn
        oid: *******.4.1.14988.*******.1.8
        type: counter
        help: ' - *******.4.1.14988.*******.1.8'
        indexes:
        - labelname: mtxrQueueSimpleIndex
          type: gauge
      - name: mtxrQueueSimpleBytesOut
        oid: *******.4.1.14988.*******.1.9
        type: counter
        help: ' - *******.4.1.14988.*******.1.9'
        indexes:
        - labelname: mtxrQueueSimpleIndex
          type: gauge
      - name: mtxrQueueSimplePacketsIn
        oid: *******.4.1.14988.*******.1.10
        type: counter
        help: ' - *******.4.1.14988.*******.1.10'
        indexes:
        - labelname: mtxrQueueSimpleIndex
          type: gauge
      - name: mtxrQueueSimplePacketsOut
        oid: *******.4.1.14988.*******.1.11
        type: counter
        help: ' - *******.4.1.14988.*******.1.11'
        indexes:
        - labelname: mtxrQueueSimpleIndex
          type: gauge
      - name: mtxrQueueSimplePCQQueuesIn
        oid: *******.4.1.14988.*******.1.12
        type: counter
        help: ' - *******.4.1.14988.*******.1.12'
        indexes:
        - labelname: mtxrQueueSimpleIndex
          type: gauge
      - name: mtxrQueueSimplePCQQueuesOut
        oid: *******.4.1.14988.*******.1.13
        type: counter
        help: ' - *******.4.1.14988.*******.1.13'
        indexes:
        - labelname: mtxrQueueSimpleIndex
          type: gauge
      - name: mtxrQueueSimpleDroppedIn
        oid: *******.4.1.14988.*******.1.14
        type: counter
        help: ' - *******.4.1.14988.*******.1.14'
        indexes:
        - labelname: mtxrQueueSimpleIndex
          type: gauge
      - name: mtxrQueueSimpleDroppedOut
        oid: *******.4.1.14988.*******.1.15
        type: counter
        help: ' - *******.4.1.14988.*******.1.15'
        indexes:
        - labelname: mtxrQueueSimpleIndex
          type: gauge
      - name: mtxrQueueTreeIndex
        oid: *******.4.1.14988.1.1.2.2.1.1
        type: gauge
        help: ' - *******.4.1.14988.1.1.2.2.1.1'
        indexes:
        - labelname: mtxrQueueTreeIndex
          type: gauge
      - name: mtxrQueueTreeName
        oid: *******.4.1.14988.1.1.2.2.1.2
        type: DisplayString
        help: ' - *******.4.1.14988.1.1.2.2.1.2'
        indexes:
        - labelname: mtxrQueueTreeIndex
          type: gauge
      - name: mtxrQueueTreeFlow
        oid: *******.4.1.14988.1.1.2.2.1.3
        type: DisplayString
        help: flowmark - *******.4.1.14988.1.1.2.2.1.3
        indexes:
        - labelname: mtxrQueueTreeIndex
          type: gauge
      - name: mtxrQueueTreeParentIndex
        oid: *******.4.1.14988.1.1.2.2.1.4
        type: gauge
        help: index of parent tree queue or parent interface - *******.4.1.14988.1.1.2.2.1.4
        indexes:
        - labelname: mtxrQueueTreeIndex
          type: gauge
      - name: mtxrQueueTreeBytes
        oid: *******.4.1.14988.1.1.2.2.1.5
        type: counter
        help: ' - *******.4.1.14988.1.1.2.2.1.5'
        indexes:
        - labelname: mtxrQueueTreeIndex
          type: gauge
      - name: mtxrQueueTreePackets
        oid: *******.4.1.14988.1.1.2.2.1.6
        type: counter
        help: ' - *******.4.1.14988.1.1.2.2.1.6'
        indexes:
        - labelname: mtxrQueueTreeIndex
          type: gauge
      - name: mtxrQueueTreeHCBytes
        oid: *******.4.1.14988.1.1.2.2.1.7
        type: counter
        help: ' - *******.4.1.14988.1.1.2.2.1.7'
        indexes:
        - labelname: mtxrQueueTreeIndex
          type: gauge
      - name: mtxrQueueTreePCQQueues
        oid: *******.4.1.14988.1.1.2.2.1.8
        type: counter
        help: ' - *******.4.1.14988.1.1.2.2.1.8'
        indexes:
        - labelname: mtxrQueueTreeIndex
          type: gauge
      - name: mtxrQueueTreeDropped
        oid: *******.4.1.14988.1.1.2.2.1.9
        type: counter
        help: ' - *******.4.1.14988.1.1.2.2.1.9'
        indexes:
        - labelname: mtxrQueueTreeIndex
          type: gauge
      - name: mtxrHlCoreVoltage
        oid: *******.4.1.149********.1
        type: gauge
        help: core voltage - *******.4.1.149********.1
      - name: mtxrHlThreeDotThreeVoltage
        oid: *******.4.1.149********.2
        type: gauge
        help: 3.3V voltage - *******.4.1.149********.2
      - name: mtxrHlFiveVoltage
        oid: *******.4.1.149********.3
        type: gauge
        help: 5V voltage - *******.4.1.149********.3
      - name: mtxrHlTwelveVoltage
        oid: *******.4.1.149********.4
        type: gauge
        help: 12V voltage - *******.4.1.149********.4
      - name: mtxrHlSensorTemperature
        oid: *******.4.1.14988.*******
        type: gauge
        help: temperature at sensor chip - *******.4.1.14988.*******
      - name: mtxrHlCpuTemperature
        oid: *******.4.1.14988.*******
        type: gauge
        help: temperature near cpu - *******.4.1.14988.*******
      - name: mtxrHlBoardTemperature
        oid: *******.4.1.14988.*******
        type: gauge
        help: ' - *******.4.1.14988.*******'
      - name: mtxrHlVoltage
        oid: *******.4.1.14988.*******
        type: gauge
        help: ' - *******.4.1.14988.*******'
      - name: mtxrHlActiveFan
        oid: *******.4.1.14988.*******
        type: DisplayString
        help: ' - *******.4.1.14988.*******'
      - name: mtxrHlTemperature
        oid: *******.4.1.14988.********
        type: gauge
        help: ' - *******.4.1.14988.********'
      - name: mtxrHlProcessorTemperature
        oid: *******.4.1.14988.********
        type: gauge
        help: ' - *******.4.1.14988.********'
      - name: mtxrHlPower
        oid: *******.4.1.14988.********
        type: gauge
        help: Watts - *******.4.1.14988.********
      - name: mtxrHlCurrent
        oid: *******.4.1.149********.13
        type: gauge
        help: mA - *******.4.1.149********.13
      - name: mtxrHlProcessorFrequency
        oid: *******.4.1.149********.14
        type: gauge
        help: Mhz - *******.4.1.149********.14
      - name: mtxrHlPowerSupplyState
        oid: *******.4.1.149********.15
        type: gauge
        help: PSU state ok - *******.4.1.149********.15
        enum_values:
          0: "false"
          1: "true"
      - name: mtxrHlBackupPowerSupplyState
        oid: *******.4.1.149********.16
        type: gauge
        help: backup PSU state ok - *******.4.1.149********.16
        enum_values:
          0: "false"
          1: "true"
      - name: mtxrHlFanSpeed1
        oid: *******.4.1.149********.17
        type: gauge
        help: rpm - *******.4.1.149********.17
      - name: mtxrHlFanSpeed2
        oid: *******.4.1.149********.18
        type: gauge
        help: rpm - *******.4.1.149********.18
      - name: mtxrGaugeIndex
        oid: *******.4.1.14988.********0.1.1
        type: gauge
        help: ' - *******.4.1.14988.********0.1.1'
        indexes:
        - labelname: mtxrGaugeIndex
          type: gauge
      - name: mtxrGaugeName
        oid: *******.4.1.14988.********0.1.2
        type: DisplayString
        help: ' - *******.4.1.14988.********0.1.2'
        indexes:
        - labelname: mtxrGaugeIndex
          type: gauge
      - name: mtxrGaugeValue
        oid: *******.4.1.14988.********0.1.3
        type: gauge
        help: ' - *******.4.1.14988.********0.1.3'
        indexes:
        - labelname: mtxrGaugeIndex
          type: gauge
      - name: mtxrGaugeUnit
        oid: *******.4.1.14988.********0.1.4
        type: gauge
        help: units - *******.4.1.14988.********0.1.4
        indexes:
        - labelname: mtxrGaugeIndex
          type: gauge
        enum_values:
          1: celsius
          2: rpm
          3: dV
          4: dA
          5: dW
          6: status
      - name: mtxrLicSoftwareId
        oid: *******.4.1.14988.1.1.4.1
        type: DisplayString
        help: software id - *******.4.1.14988.1.1.4.1
      - name: mtxrLicUpgrUntil
        oid: *******.4.1.14988.1.1.4.2
        type: DateAndTime
        help: current key allows upgrading until this date - *******.4.1.14988.1.1.4.2
      - name: mtxrLicLevel
        oid: *******.4.1.14988.1.1.4.3
        type: gauge
        help: current key level - *******.4.1.14988.1.1.4.3
      - name: mtxrLicVersion
        oid: *******.4.1.14988.1.1.4.4
        type: DisplayString
        help: software version - *******.4.1.14988.1.1.4.4
      - name: mtxrLicUpgradableTo
        oid: *******.4.1.14988.1.1.4.5
        type: gauge
        help: upgradable to - *******.4.1.14988.1.1.4.5
      - name: mtxrHotspotActiveUserIndex
        oid: *******.4.1.14988.*******.1.1
        type: gauge
        help: ' - *******.4.1.14988.*******.1.1'
        indexes:
        - labelname: mtxrHotspotActiveUserIndex
          type: gauge
      - name: mtxrHotspotActiveUserServerID
        oid: *******.4.1.14988.*******.1.2
        type: gauge
        help: ' - *******.4.1.14988.*******.1.2'
        indexes:
        - labelname: mtxrHotspotActiveUserIndex
          type: gauge
      - name: mtxrHotspotActiveUserName
        oid: *******.4.1.14988.*******.1.3
        type: DisplayString
        help: ' - *******.4.1.14988.*******.1.3'
        indexes:
        - labelname: mtxrHotspotActiveUserIndex
          type: gauge
      - name: mtxrHotspotActiveUserDomain
        oid: *******.4.1.14988.*******.1.4
        type: DisplayString
        help: ' - *******.4.1.14988.*******.1.4'
        indexes:
        - labelname: mtxrHotspotActiveUserIndex
          type: gauge
      - name: mtxrHotspotActiveUserIP
        oid: *******.4.1.14988.*******.1.5
        type: InetAddressIPv4
        help: ' - *******.4.1.14988.*******.1.5'
        indexes:
        - labelname: mtxrHotspotActiveUserIndex
          type: gauge
      - name: mtxrHotspotActiveUserMAC
        oid: *******.4.1.14988.*******.1.6
        type: PhysAddress48
        help: ' - *******.4.1.14988.*******.1.6'
        indexes:
        - labelname: mtxrHotspotActiveUserIndex
          type: gauge
      - name: mtxrHotspotActiveUserConnectTime
        oid: *******.4.1.14988.*******.1.7
        type: gauge
        help: ' - *******.4.1.14988.*******.1.7'
        indexes:
        - labelname: mtxrHotspotActiveUserIndex
          type: gauge
      - name: mtxrHotspotActiveUserValidTillTime
        oid: *******.4.1.14988.*******.1.8
        type: gauge
        help: ' - *******.4.1.14988.*******.1.8'
        indexes:
        - labelname: mtxrHotspotActiveUserIndex
          type: gauge
      - name: mtxrHotspotActiveUserIdleStartTime
        oid: *******.4.1.14988.*******.1.9
        type: gauge
        help: ' - *******.4.1.14988.*******.1.9'
        indexes:
        - labelname: mtxrHotspotActiveUserIndex
          type: gauge
      - name: mtxrHotspotActiveUserIdleTimeout
        oid: *******.4.1.14988.*******.1.10
        type: gauge
        help: ' - *******.4.1.14988.*******.1.10'
        indexes:
        - labelname: mtxrHotspotActiveUserIndex
          type: gauge
      - name: mtxrHotspotActiveUserPingTimeout
        oid: *******.4.1.14988.*******.1.11
        type: gauge
        help: ' - *******.4.1.14988.*******.1.11'
        indexes:
        - labelname: mtxrHotspotActiveUserIndex
          type: gauge
      - name: mtxrHotspotActiveUserBytesIn
        oid: *******.4.1.14988.*******.1.12
        type: counter
        help: ' - *******.4.1.14988.*******.1.12'
        indexes:
        - labelname: mtxrHotspotActiveUserIndex
          type: gauge
      - name: mtxrHotspotActiveUserBytesOut
        oid: *******.4.1.14988.*******.1.13
        type: counter
        help: ' - *******.4.1.14988.*******.1.13'
        indexes:
        - labelname: mtxrHotspotActiveUserIndex
          type: gauge
      - name: mtxrHotspotActiveUserPacketsIn
        oid: *******.4.1.14988.*******.1.14
        type: counter
        help: ' - *******.4.1.14988.*******.1.14'
        indexes:
        - labelname: mtxrHotspotActiveUserIndex
          type: gauge
      - name: mtxrHotspotActiveUserPacketsOut
        oid: *******.4.1.14988.*******.1.15
        type: counter
        help: ' - *******.4.1.14988.*******.1.15'
        indexes:
        - labelname: mtxrHotspotActiveUserIndex
          type: gauge
      - name: mtxrHotspotActiveUserLimitBytesIn
        oid: *******.4.1.14988.*******.1.16
        type: counter
        help: ' - *******.4.1.14988.*******.1.16'
        indexes:
        - labelname: mtxrHotspotActiveUserIndex
          type: gauge
      - name: mtxrHotspotActiveUserLimitBytesOut
        oid: *******.4.1.14988.*******.1.17
        type: counter
        help: ' - *******.4.1.14988.*******.1.17'
        indexes:
        - labelname: mtxrHotspotActiveUserIndex
          type: gauge
      - name: mtxrHotspotActiveUserAdvertStatus
        oid: *******.4.1.14988.*******.1.18
        type: gauge
        help: ' - *******.4.1.14988.*******.1.18'
        indexes:
        - labelname: mtxrHotspotActiveUserIndex
          type: gauge
      - name: mtxrHotspotActiveUserRadius
        oid: *******.4.1.14988.*******.1.19
        type: gauge
        help: ' - *******.4.1.14988.*******.1.19'
        indexes:
        - labelname: mtxrHotspotActiveUserIndex
          type: gauge
      - name: mtxrHotspotActiveUserBlockedByAdvert
        oid: *******.4.1.14988.*******.1.20
        type: gauge
        help: ' - *******.4.1.14988.*******.1.20'
        indexes:
        - labelname: mtxrHotspotActiveUserIndex
          type: gauge
      - name: mtxrDHCPLeaseCount
        oid: *******.4.1.14988.1.1.6.1
        type: gauge
        help: ' - *******.4.1.14988.1.1.6.1'
      - name: mtxrSystemReboot
        oid: *******.4.1.14988.1.1.7.1
        type: gauge
        help: set non zero to reboot - *******.4.1.14988.1.1.7.1
      - name: mtxrUSBPowerReset
        oid: *******.4.1.14988.1.1.7.2
        type: gauge
        help: switches off usb power for specified amout of seconds - *******.4.1.14988.1.1.7.2
      - name: mtxrSerialNumber
        oid: *******.4.1.14988.1.1.7.3
        type: DisplayString
        help: RouterBOARD serial number - *******.4.1.14988.1.1.7.3
      - name: mtxrFirmwareVersion
        oid: *******.4.1.14988.1.1.7.4
        type: DisplayString
        help: Current firmware version - *******.4.1.14988.1.1.7.4
      - name: mtxrNote
        oid: *******.4.1.14988.*******
        type: DisplayString
        help: note - *******.4.1.14988.*******
      - name: mtxrBuildTime
        oid: *******.4.1.14988.*******
        type: DisplayString
        help: build time - *******.4.1.14988.*******
      - name: mtxrFirmwareUpgradeVersion
        oid: *******.4.1.14988.*******
        type: DisplayString
        help: Upgrade firmware version - *******.4.1.14988.*******
      - name: mtxrBoardName
        oid: *******.4.1.14988.*******
        type: DisplayString
        help: board name - *******.4.1.14988.*******
      - name: mtxrScriptIndex
        oid: *******.4.1.14988.*******.1.1
        type: gauge
        help: ' - *******.4.1.14988.*******.1.1'
        indexes:
        - labelname: mtxrScriptIndex
          type: gauge
      - name: mtxrScriptName
        oid: *******.4.1.14988.*******.1.2
        type: DisplayString
        help: ' - *******.4.1.14988.*******.1.2'
        indexes:
        - labelname: mtxrScriptIndex
          type: gauge
      - name: mtxrScriptRunCmd
        oid: *******.4.1.14988.*******.1.3
        type: gauge
        help: set non zero to run - *******.4.1.14988.*******.1.3
        indexes:
        - labelname: mtxrScriptIndex
          type: gauge
      - name: mtxrDnStatIndex
        oid: *******.4.1.14988.********.1.1
        type: gauge
        help: ' - *******.4.1.14988.********.1.1'
        indexes:
        - labelname: mtxrDnStatIndex
          type: gauge
      - name: mtxrDnStatTxRate
        oid: *******.4.1.14988.********.1.2
        type: gauge
        help: bits per second - *******.4.1.14988.********.1.2
        indexes:
        - labelname: mtxrDnStatIndex
          type: gauge
      - name: mtxrDnStatRxRate
        oid: *******.4.1.14988.********.1.3
        type: gauge
        help: bits per second - *******.4.1.14988.********.1.3
        indexes:
        - labelname: mtxrDnStatIndex
          type: gauge
      - name: mtxrDnStatTxStrength
        oid: *******.4.1.14988.********.1.4
        type: gauge
        help: dBm - *******.4.1.14988.********.1.4
        indexes:
        - labelname: mtxrDnStatIndex
          type: gauge
      - name: mtxrDnStatRxStrength
        oid: *******.4.1.14988.********.1.5
        type: gauge
        help: dBm - *******.4.1.14988.********.1.5
        indexes:
        - labelname: mtxrDnStatIndex
          type: gauge
      - name: mtxrDnConnected
        oid: *******.4.1.14988.********.1.6
        type: gauge
        help: 0 - not connected, connected otherwise - *******.4.1.14988.********.1.6
        indexes:
        - labelname: mtxrDnStatIndex
          type: gauge
      - name: mtxrNeighborIndex
        oid: *******.4.1.14988.1.1.1*******
        type: gauge
        help: ' - *******.4.1.14988.1.1.1*******'
        indexes:
        - labelname: mtxrNeighborIndex
          type: gauge
      - name: mtxrNeighborIpAddress
        oid: *******.4.1.14988.1.1.1*******
        type: InetAddressIPv4
        help: ' - *******.4.1.14988.1.1.1*******'
        indexes:
        - labelname: mtxrNeighborIndex
          type: gauge
      - name: mtxrNeighborMacAddress
        oid: *******.4.1.14988.1.1.1*******
        type: PhysAddress48
        help: ' - *******.4.1.14988.1.1.1*******'
        indexes:
        - labelname: mtxrNeighborIndex
          type: gauge
      - name: mtxrNeighborVersion
        oid: *******.4.1.14988.********.1.4
        type: DisplayString
        help: ' - *******.4.1.14988.********.1.4'
        indexes:
        - labelname: mtxrNeighborIndex
          type: gauge
      - name: mtxrNeighborPlatform
        oid: *******.4.1.14988.********.1.5
        type: DisplayString
        help: ' - *******.4.1.14988.********.1.5'
        indexes:
        - labelname: mtxrNeighborIndex
          type: gauge
      - name: mtxrNeighborIdentity
        oid: *******.4.1.14988.********.1.6
        type: DisplayString
        help: ' - *******.4.1.14988.********.1.6'
        indexes:
        - labelname: mtxrNeighborIndex
          type: gauge
      - name: mtxrNeighborSoftwareID
        oid: *******.4.1.14988.********.1.7
        type: DisplayString
        help: ' - *******.4.1.14988.********.1.7'
        indexes:
        - labelname: mtxrNeighborIndex
          type: gauge
      - name: mtxrNeighborInterfaceID
        oid: *******.4.1.14988.********.1.8
        type: gauge
        help: ' - *******.4.1.14988.********.1.8'
        indexes:
        - labelname: mtxrNeighborIndex
          type: gauge
      - name: mtxrDate
        oid: *******.4.1.14988.1.1.12.1
        type: gauge
        help: UNIX time - *******.4.1.14988.1.1.12.1
      - name: mtxrLongtitude
        oid: *******.4.1.14988.1.1.12.2
        type: DisplayString
        help: longtitude - *******.4.1.14988.1.1.12.2
      - name: mtxrLatitude
        oid: *******.4.1.14988.1.1.12.3
        type: DisplayString
        help: latitude - *******.4.1.14988.1.1.12.3
      - name: mtxrAltitude
        oid: *******.4.1.14988.1.1.12.4
        type: DisplayString
        help: altitude - *******.4.1.14988.1.1.12.4
      - name: mtxrSpeed
        oid: *******.4.1.14988.1.1.12.5
        type: DisplayString
        help: speed - *******.4.1.14988.1.1.12.5
      - name: mtxrSattelites
        oid: *******.4.1.14988.1.1.12.6
        type: gauge
        help: visible sattelite count - *******.4.1.14988.1.1.12.6
      - name: mtxrValid
        oid: *******.4.1.14988.1.1.12.7
        type: gauge
        help: is the data valid - *******.4.1.14988.1.1.12.7
      - name: mtxrWirelessModemSignalStrength
        oid: *******.4.1.14988.1.1.13.1
        type: gauge
        help: signal strength in dBm (if first ppp-client modem supports) - *******.4.1.14988.1.1.13.1
      - name: mtxrWirelessModemSignalECIO
        oid: *******.4.1.14988.1.1.13.2
        type: gauge
        help: signal EC/IO in dB (if first ppp-client modem supports) - *******.4.1.14988.1.1.13.2
      - name: mtxrInterfaceStatsIndex
        oid: *******.4.1.14988.********.1.1
        type: gauge
        help: ' - *******.4.1.14988.********.1.1'
        indexes:
        - labelname: mtxrInterfaceStatsIndex
          type: gauge
        lookups:
        - labels:
          - mtxrInterfaceStatsIndex
          labelname: ifName
          oid: *******.********.1.1.1
          type: DisplayString
      - name: mtxrInterfaceStatsName
        oid: *******.4.1.14988.********.1.2
        type: DisplayString
        help: ' - *******.4.1.14988.********.1.2'
        indexes:
        - labelname: mtxrInterfaceStatsIndex
          type: gauge
        lookups:
        - labels:
          - mtxrInterfaceStatsIndex
          labelname: ifName
          oid: *******.********.1.1.1
          type: DisplayString
      - name: mtxrInterfaceStatsDriverRxBytes
        oid: *******.4.1.14988.********.1.11
        type: counter
        help: ' - *******.4.1.14988.********.1.11'
        indexes:
        - labelname: mtxrInterfaceStatsIndex
          type: gauge
        lookups:
        - labels:
          - mtxrInterfaceStatsIndex
          labelname: ifName
          oid: *******.********.1.1.1
          type: DisplayString
      - name: mtxrInterfaceStatsDriverRxPackets
        oid: *******.4.1.14988.********.1.12
        type: counter
        help: ' - *******.4.1.14988.********.1.12'
        indexes:
        - labelname: mtxrInterfaceStatsIndex
          type: gauge
        lookups:
        - labels:
          - mtxrInterfaceStatsIndex
          labelname: ifName
          oid: *******.********.1.1.1
          type: DisplayString
      - name: mtxrInterfaceStatsDriverTxBytes
        oid: *******.4.1.14988.********.1.13
        type: counter
        help: ' - *******.4.1.14988.********.1.13'
        indexes:
        - labelname: mtxrInterfaceStatsIndex
          type: gauge
        lookups:
        - labels:
          - mtxrInterfaceStatsIndex
          labelname: ifName
          oid: *******.********.1.1.1
          type: DisplayString
      - name: mtxrInterfaceStatsDriverTxPackets
        oid: *******.4.1.14988.********.1.14
        type: counter
        help: ' - *******.4.1.14988.********.1.14'
        indexes:
        - labelname: mtxrInterfaceStatsIndex
          type: gauge
        lookups:
        - labels:
          - mtxrInterfaceStatsIndex
          labelname: ifName
          oid: *******.********.1.1.1
          type: DisplayString
      - name: mtxrInterfaceStatsTxRx64
        oid: *******.4.1.14988.********.1.15
        type: counter
        help: ' - *******.4.1.14988.********.1.15'
        indexes:
        - labelname: mtxrInterfaceStatsIndex
          type: gauge
        lookups:
        - labels:
          - mtxrInterfaceStatsIndex
          labelname: ifName
          oid: *******.********.1.1.1
          type: DisplayString
      - name: mtxrInterfaceStatsTxRx65To127
        oid: *******.4.1.14988.********.1.16
        type: counter
        help: ' - *******.4.1.14988.********.1.16'
        indexes:
        - labelname: mtxrInterfaceStatsIndex
          type: gauge
        lookups:
        - labels:
          - mtxrInterfaceStatsIndex
          labelname: ifName
          oid: *******.********.1.1.1
          type: DisplayString
      - name: mtxrInterfaceStatsTxRx128To255
        oid: *******.4.1.14988.********.1.17
        type: counter
        help: ' - *******.4.1.14988.********.1.17'
        indexes:
        - labelname: mtxrInterfaceStatsIndex
          type: gauge
        lookups:
        - labels:
          - mtxrInterfaceStatsIndex
          labelname: ifName
          oid: *******.********.1.1.1
          type: DisplayString
      - name: mtxrInterfaceStatsTxRx256To511
        oid: *******.4.1.14988.********.1.18
        type: counter
        help: ' - *******.4.1.14988.********.1.18'
        indexes:
        - labelname: mtxrInterfaceStatsIndex
          type: gauge
        lookups:
        - labels:
          - mtxrInterfaceStatsIndex
          labelname: ifName
          oid: *******.********.1.1.1
          type: DisplayString
      - name: mtxrInterfaceStatsTxRx512To1023
        oid: *******.4.1.14988.********.1.19
        type: counter
        help: ' - *******.4.1.14988.********.1.19'
        indexes:
        - labelname: mtxrInterfaceStatsIndex
          type: gauge
        lookups:
        - labels:
          - mtxrInterfaceStatsIndex
          labelname: ifName
          oid: *******.********.1.1.1
          type: DisplayString
      - name: mtxrInterfaceStatsTxRx1024To1518
        oid: *******.4.1.14988.********.1.20
        type: counter
        help: ' - *******.4.1.14988.********.1.20'
        indexes:
        - labelname: mtxrInterfaceStatsIndex
          type: gauge
        lookups:
        - labels:
          - mtxrInterfaceStatsIndex
          labelname: ifName
          oid: *******.********.1.1.1
          type: DisplayString
      - name: mtxrInterfaceStatsTxRx1519ToMax
        oid: *******.4.1.14988.********.1.21
        type: counter
        help: ' - *******.4.1.14988.********.1.21'
        indexes:
        - labelname: mtxrInterfaceStatsIndex
          type: gauge
        lookups:
        - labels:
          - mtxrInterfaceStatsIndex
          labelname: ifName
          oid: *******.********.1.1.1
          type: DisplayString
      - name: mtxrInterfaceStatsRxBytes
        oid: *******.4.1.14988.********.1.31
        type: counter
        help: ' - *******.4.1.14988.********.1.31'
        indexes:
        - labelname: mtxrInterfaceStatsIndex
          type: gauge
        lookups:
        - labels:
          - mtxrInterfaceStatsIndex
          labelname: ifName
          oid: *******.********.1.1.1
          type: DisplayString
      - name: mtxrInterfaceStatsRxPackets
        oid: *******.4.1.14988.********.1.32
        type: counter
        help: ' - *******.4.1.14988.********.1.32'
        indexes:
        - labelname: mtxrInterfaceStatsIndex
          type: gauge
        lookups:
        - labels:
          - mtxrInterfaceStatsIndex
          labelname: ifName
          oid: *******.********.1.1.1
          type: DisplayString
      - name: mtxrInterfaceStatsRxTooShort
        oid: *******.4.1.14988.********.1.33
        type: counter
        help: ' - *******.4.1.14988.********.1.33'
        indexes:
        - labelname: mtxrInterfaceStatsIndex
          type: gauge
        lookups:
        - labels:
          - mtxrInterfaceStatsIndex
          labelname: ifName
          oid: *******.********.1.1.1
          type: DisplayString
      - name: mtxrInterfaceStatsRx64
        oid: *******.4.1.14988.********.1.34
        type: counter
        help: ' - *******.4.1.14988.********.1.34'
        indexes:
        - labelname: mtxrInterfaceStatsIndex
          type: gauge
        lookups:
        - labels:
          - mtxrInterfaceStatsIndex
          labelname: ifName
          oid: *******.********.1.1.1
          type: DisplayString
      - name: mtxrInterfaceStatsRx65To127
        oid: *******.4.1.14988.********.1.35
        type: counter
        help: ' - *******.4.1.14988.********.1.35'
        indexes:
        - labelname: mtxrInterfaceStatsIndex
          type: gauge
        lookups:
        - labels:
          - mtxrInterfaceStatsIndex
          labelname: ifName
          oid: *******.********.1.1.1
          type: DisplayString
      - name: mtxrInterfaceStatsRx128To255
        oid: *******.4.1.14988.********.1.36
        type: counter
        help: ' - *******.4.1.14988.********.1.36'
        indexes:
        - labelname: mtxrInterfaceStatsIndex
          type: gauge
        lookups:
        - labels:
          - mtxrInterfaceStatsIndex
          labelname: ifName
          oid: *******.********.1.1.1
          type: DisplayString
      - name: mtxrInterfaceStatsRx256To511
        oid: *******.4.1.14988.********.1.37
        type: counter
        help: ' - *******.4.1.14988.********.1.37'
        indexes:
        - labelname: mtxrInterfaceStatsIndex
          type: gauge
        lookups:
        - labels:
          - mtxrInterfaceStatsIndex
          labelname: ifName
          oid: *******.********.1.1.1
          type: DisplayString
      - name: mtxrInterfaceStatsRx512To1023
        oid: *******.4.1.14988.********.1.38
        type: counter
        help: ' - *******.4.1.14988.********.1.38'
        indexes:
        - labelname: mtxrInterfaceStatsIndex
          type: gauge
        lookups:
        - labels:
          - mtxrInterfaceStatsIndex
          labelname: ifName
          oid: *******.********.1.1.1
          type: DisplayString
      - name: mtxrInterfaceStatsRx1024To1518
        oid: *******.4.1.14988.********.1.39
        type: counter
        help: ' - *******.4.1.14988.********.1.39'
        indexes:
        - labelname: mtxrInterfaceStatsIndex
          type: gauge
        lookups:
        - labels:
          - mtxrInterfaceStatsIndex
          labelname: ifName
          oid: *******.********.1.1.1
          type: DisplayString
      - name: mtxrInterfaceStatsRx1519ToMax
        oid: *******.4.1.14988.********.1.40
        type: counter
        help: ' - *******.4.1.14988.********.1.40'
        indexes:
        - labelname: mtxrInterfaceStatsIndex
          type: gauge
        lookups:
        - labels:
          - mtxrInterfaceStatsIndex
          labelname: ifName
          oid: *******.********.1.1.1
          type: DisplayString
      - name: mtxrInterfaceStatsRxTooLong
        oid: *******.4.1.14988.********.1.41
        type: counter
        help: ' - *******.4.1.14988.********.1.41'
        indexes:
        - labelname: mtxrInterfaceStatsIndex
          type: gauge
        lookups:
        - labels:
          - mtxrInterfaceStatsIndex
          labelname: ifName
          oid: *******.********.1.1.1
          type: DisplayString
      - name: mtxrInterfaceStatsRxBroadcast
        oid: *******.4.1.14988.********.1.42
        type: counter
        help: ' - *******.4.1.14988.********.1.42'
        indexes:
        - labelname: mtxrInterfaceStatsIndex
          type: gauge
        lookups:
        - labels:
          - mtxrInterfaceStatsIndex
          labelname: ifName
          oid: *******.********.1.1.1
          type: DisplayString
      - name: mtxrInterfaceStatsRxPause
        oid: *******.4.1.14988.********.1.43
        type: counter
        help: ' - *******.4.1.14988.********.1.43'
        indexes:
        - labelname: mtxrInterfaceStatsIndex
          type: gauge
        lookups:
        - labels:
          - mtxrInterfaceStatsIndex
          labelname: ifName
          oid: *******.********.1.1.1
          type: DisplayString
      - name: mtxrInterfaceStatsRxMulticast
        oid: *******.4.1.14988.********.1.44
        type: counter
        help: ' - *******.4.1.14988.********.1.44'
        indexes:
        - labelname: mtxrInterfaceStatsIndex
          type: gauge
        lookups:
        - labels:
          - mtxrInterfaceStatsIndex
          labelname: ifName
          oid: *******.********.1.1.1
          type: DisplayString
      - name: mtxrInterfaceStatsRxFCSError
        oid: *******.4.1.14988.********.1.45
        type: counter
        help: ' - *******.4.1.14988.********.1.45'
        indexes:
        - labelname: mtxrInterfaceStatsIndex
          type: gauge
        lookups:
        - labels:
          - mtxrInterfaceStatsIndex
          labelname: ifName
          oid: *******.********.1.1.1
          type: DisplayString
      - name: mtxrInterfaceStatsRxAlignError
        oid: *******.4.1.14988.********.1.46
        type: counter
        help: ' - *******.4.1.14988.********.1.46'
        indexes:
        - labelname: mtxrInterfaceStatsIndex
          type: gauge
        lookups:
        - labels:
          - mtxrInterfaceStatsIndex
          labelname: ifName
          oid: *******.********.1.1.1
          type: DisplayString
      - name: mtxrInterfaceStatsRxFragment
        oid: *******.4.1.14988.********.1.47
        type: counter
        help: ' - *******.4.1.14988.********.1.47'
        indexes:
        - labelname: mtxrInterfaceStatsIndex
          type: gauge
        lookups:
        - labels:
          - mtxrInterfaceStatsIndex
          labelname: ifName
          oid: *******.********.1.1.1
          type: DisplayString
      - name: mtxrInterfaceStatsRxOverflow
        oid: *******.4.1.14988.********.1.48
        type: counter
        help: ' - *******.4.1.14988.********.1.48'
        indexes:
        - labelname: mtxrInterfaceStatsIndex
          type: gauge
        lookups:
        - labels:
          - mtxrInterfaceStatsIndex
          labelname: ifName
          oid: *******.********.1.1.1
          type: DisplayString
      - name: mtxrInterfaceStatsRxControl
        oid: *******.4.1.14988.********.1.49
        type: counter
        help: ' - *******.4.1.14988.********.1.49'
        indexes:
        - labelname: mtxrInterfaceStatsIndex
          type: gauge
        lookups:
        - labels:
          - mtxrInterfaceStatsIndex
          labelname: ifName
          oid: *******.********.1.1.1
          type: DisplayString
      - name: mtxrInterfaceStatsRxUnknownOp
        oid: *******.4.1.14988.********.1.50
        type: counter
        help: ' - *******.4.1.14988.********.1.50'
        indexes:
        - labelname: mtxrInterfaceStatsIndex
          type: gauge
        lookups:
        - labels:
          - mtxrInterfaceStatsIndex
          labelname: ifName
          oid: *******.********.1.1.1
          type: DisplayString
      - name: mtxrInterfaceStatsRxLengthError
        oid: *******.4.1.14988.********.1.51
        type: counter
        help: ' - *******.4.1.14988.********.1.51'
        indexes:
        - labelname: mtxrInterfaceStatsIndex
          type: gauge
        lookups:
        - labels:
          - mtxrInterfaceStatsIndex
          labelname: ifName
          oid: *******.********.1.1.1
          type: DisplayString
      - name: mtxrInterfaceStatsRxCodeError
        oid: *******.4.1.14988.********.1.52
        type: counter
        help: ' - *******.4.1.14988.********.1.52'
        indexes:
        - labelname: mtxrInterfaceStatsIndex
          type: gauge
        lookups:
        - labels:
          - mtxrInterfaceStatsIndex
          labelname: ifName
          oid: *******.********.1.1.1
          type: DisplayString
      - name: mtxrInterfaceStatsRxCarrierError
        oid: *******.4.1.14988.********.1.53
        type: counter
        help: ' - *******.4.1.14988.********.1.53'
        indexes:
        - labelname: mtxrInterfaceStatsIndex
          type: gauge
        lookups:
        - labels:
          - mtxrInterfaceStatsIndex
          labelname: ifName
          oid: *******.********.1.1.1
          type: DisplayString
      - name: mtxrInterfaceStatsRxJabber
        oid: *******.4.1.14988.********.1.54
        type: counter
        help: ' - *******.4.1.14988.********.1.54'
        indexes:
        - labelname: mtxrInterfaceStatsIndex
          type: gauge
        lookups:
        - labels:
          - mtxrInterfaceStatsIndex
          labelname: ifName
          oid: *******.********.1.1.1
          type: DisplayString
      - name: mtxrInterfaceStatsRxDrop
        oid: *******.4.1.14988.********.1.55
        type: counter
        help: ' - *******.4.1.14988.********.1.55'
        indexes:
        - labelname: mtxrInterfaceStatsIndex
          type: gauge
        lookups:
        - labels:
          - mtxrInterfaceStatsIndex
          labelname: ifName
          oid: *******.********.1.1.1
          type: DisplayString
      - name: mtxrInterfaceStatsTxBytes
        oid: *******.4.1.14988.********.1.61
        type: counter
        help: ' - *******.4.1.14988.********.1.61'
        indexes:
        - labelname: mtxrInterfaceStatsIndex
          type: gauge
        lookups:
        - labels:
          - mtxrInterfaceStatsIndex
          labelname: ifName
          oid: *******.********.1.1.1
          type: DisplayString
      - name: mtxrInterfaceStatsTxPackets
        oid: *******.4.1.14988.********.1.62
        type: counter
        help: ' - *******.4.1.14988.********.1.62'
        indexes:
        - labelname: mtxrInterfaceStatsIndex
          type: gauge
        lookups:
        - labels:
          - mtxrInterfaceStatsIndex
          labelname: ifName
          oid: *******.********.1.1.1
          type: DisplayString
      - name: mtxrInterfaceStatsTxTooShort
        oid: *******.4.1.14988.********.1.63
        type: counter
        help: ' - *******.4.1.14988.********.1.63'
        indexes:
        - labelname: mtxrInterfaceStatsIndex
          type: gauge
        lookups:
        - labels:
          - mtxrInterfaceStatsIndex
          labelname: ifName
          oid: *******.********.1.1.1
          type: DisplayString
      - name: mtxrInterfaceStatsTx64
        oid: *******.4.1.14988.********.1.64
        type: counter
        help: ' - *******.4.1.14988.********.1.64'
        indexes:
        - labelname: mtxrInterfaceStatsIndex
          type: gauge
        lookups:
        - labels:
          - mtxrInterfaceStatsIndex
          labelname: ifName
          oid: *******.********.1.1.1
          type: DisplayString
      - name: mtxrInterfaceStatsTx65To127
        oid: *******.4.1.14988.********.1.65
        type: counter
        help: ' - *******.4.1.14988.********.1.65'
        indexes:
        - labelname: mtxrInterfaceStatsIndex
          type: gauge
        lookups:
        - labels:
          - mtxrInterfaceStatsIndex
          labelname: ifName
          oid: *******.********.1.1.1
          type: DisplayString
      - name: mtxrInterfaceStatsTx128To255
        oid: *******.4.1.14988.********.1.66
        type: counter
        help: ' - *******.4.1.14988.********.1.66'
        indexes:
        - labelname: mtxrInterfaceStatsIndex
          type: gauge
        lookups:
        - labels:
          - mtxrInterfaceStatsIndex
          labelname: ifName
          oid: *******.********.1.1.1
          type: DisplayString
      - name: mtxrInterfaceStatsTx256To511
        oid: *******.4.1.14988.********.1.67
        type: counter
        help: ' - *******.4.1.14988.********.1.67'
        indexes:
        - labelname: mtxrInterfaceStatsIndex
          type: gauge
        lookups:
        - labels:
          - mtxrInterfaceStatsIndex
          labelname: ifName
          oid: *******.********.1.1.1
          type: DisplayString
      - name: mtxrInterfaceStatsTx512To1023
        oid: *******.4.1.14988.********.1.68
        type: counter
        help: ' - *******.4.1.14988.********.1.68'
        indexes:
        - labelname: mtxrInterfaceStatsIndex
          type: gauge
        lookups:
        - labels:
          - mtxrInterfaceStatsIndex
          labelname: ifName
          oid: *******.********.1.1.1
          type: DisplayString
      - name: mtxrInterfaceStatsTx1024To1518
        oid: *******.4.1.14988.********.1.69
        type: counter
        help: ' - *******.4.1.14988.********.1.69'
        indexes:
        - labelname: mtxrInterfaceStatsIndex
          type: gauge
        lookups:
        - labels:
          - mtxrInterfaceStatsIndex
          labelname: ifName
          oid: *******.********.1.1.1
          type: DisplayString
      - name: mtxrInterfaceStatsTx1519ToMax
        oid: *******.4.1.14988.********.1.70
        type: counter
        help: ' - *******.4.1.14988.********.1.70'
        indexes:
        - labelname: mtxrInterfaceStatsIndex
          type: gauge
        lookups:
        - labels:
          - mtxrInterfaceStatsIndex
          labelname: ifName
          oid: *******.********.1.1.1
          type: DisplayString
      - name: mtxrInterfaceStatsTxTooLong
        oid: *******.4.1.14988.********.1.71
        type: counter
        help: ' - *******.4.1.14988.********.1.71'
        indexes:
        - labelname: mtxrInterfaceStatsIndex
          type: gauge
        lookups:
        - labels:
          - mtxrInterfaceStatsIndex
          labelname: ifName
          oid: *******.********.1.1.1
          type: DisplayString
      - name: mtxrInterfaceStatsTxBroadcast
        oid: *******.4.1.14988.********.1.72
        type: counter
        help: ' - *******.4.1.14988.********.1.72'
        indexes:
        - labelname: mtxrInterfaceStatsIndex
          type: gauge
        lookups:
        - labels:
          - mtxrInterfaceStatsIndex
          labelname: ifName
          oid: *******.********.1.1.1
          type: DisplayString
      - name: mtxrInterfaceStatsTxPause
        oid: *******.4.1.14988.********.1.73
        type: counter
        help: ' - *******.4.1.14988.********.1.73'
        indexes:
        - labelname: mtxrInterfaceStatsIndex
          type: gauge
        lookups:
        - labels:
          - mtxrInterfaceStatsIndex
          labelname: ifName
          oid: *******.********.1.1.1
          type: DisplayString
      - name: mtxrInterfaceStatsTxMulticast
        oid: *******.4.1.14988.********.1.74
        type: counter
        help: ' - *******.4.1.14988.********.1.74'
        indexes:
        - labelname: mtxrInterfaceStatsIndex
          type: gauge
        lookups:
        - labels:
          - mtxrInterfaceStatsIndex
          labelname: ifName
          oid: *******.********.1.1.1
          type: DisplayString
      - name: mtxrInterfaceStatsTxUnderrun
        oid: *******.4.1.14988.********.1.75
        type: counter
        help: ' - *******.4.1.14988.********.1.75'
        indexes:
        - labelname: mtxrInterfaceStatsIndex
          type: gauge
        lookups:
        - labels:
          - mtxrInterfaceStatsIndex
          labelname: ifName
          oid: *******.********.1.1.1
          type: DisplayString
      - name: mtxrInterfaceStatsTxCollision
        oid: *******.4.1.14988.********.1.76
        type: counter
        help: ' - *******.4.1.14988.********.1.76'
        indexes:
        - labelname: mtxrInterfaceStatsIndex
          type: gauge
        lookups:
        - labels:
          - mtxrInterfaceStatsIndex
          labelname: ifName
          oid: *******.********.1.1.1
          type: DisplayString
      - name: mtxrInterfaceStatsTxExcessiveCollision
        oid: *******.4.1.14988.********.1.77
        type: counter
        help: ' - *******.4.1.14988.********.1.77'
        indexes:
        - labelname: mtxrInterfaceStatsIndex
          type: gauge
        lookups:
        - labels:
          - mtxrInterfaceStatsIndex
          labelname: ifName
          oid: *******.********.1.1.1
          type: DisplayString
      - name: mtxrInterfaceStatsTxMultipleCollision
        oid: *******.4.1.14988.********.1.78
        type: counter
        help: ' - *******.4.1.14988.********.1.78'
        indexes:
        - labelname: mtxrInterfaceStatsIndex
          type: gauge
        lookups:
        - labels:
          - mtxrInterfaceStatsIndex
          labelname: ifName
          oid: *******.********.1.1.1
          type: DisplayString
      - name: mtxrInterfaceStatsTxSingleCollision
        oid: *******.4.1.14988.********.1.79
        type: counter
        help: ' - *******.4.1.14988.********.1.79'
        indexes:
        - labelname: mtxrInterfaceStatsIndex
          type: gauge
        lookups:
        - labels:
          - mtxrInterfaceStatsIndex
          labelname: ifName
          oid: *******.********.1.1.1
          type: DisplayString
      - name: mtxrInterfaceStatsTxExcessiveDeferred
        oid: *******.4.1.14988.********.1.80
        type: counter
        help: ' - *******.4.1.14988.********.1.80'
        indexes:
        - labelname: mtxrInterfaceStatsIndex
          type: gauge
        lookups:
        - labels:
          - mtxrInterfaceStatsIndex
          labelname: ifName
          oid: *******.********.1.1.1
          type: DisplayString
      - name: mtxrInterfaceStatsTxDeferred
        oid: *******.4.1.14988.********.1.81
        type: counter
        help: ' - *******.4.1.14988.********.1.81'
        indexes:
        - labelname: mtxrInterfaceStatsIndex
          type: gauge
        lookups:
        - labels:
          - mtxrInterfaceStatsIndex
          labelname: ifName
          oid: *******.********.1.1.1
          type: DisplayString
      - name: mtxrInterfaceStatsTxLateCollision
        oid: *******.4.1.14988.********.1.82
        type: counter
        help: ' - *******.4.1.14988.********.1.82'
        indexes:
        - labelname: mtxrInterfaceStatsIndex
          type: gauge
        lookups:
        - labels:
          - mtxrInterfaceStatsIndex
          labelname: ifName
          oid: *******.********.1.1.1
          type: DisplayString
      - name: mtxrInterfaceStatsTxTotalCollision
        oid: *******.4.1.14988.********.1.83
        type: counter
        help: ' - *******.4.1.14988.********.1.83'
        indexes:
        - labelname: mtxrInterfaceStatsIndex
          type: gauge
        lookups:
        - labels:
          - mtxrInterfaceStatsIndex
          labelname: ifName
          oid: *******.********.1.1.1
          type: DisplayString
      - name: mtxrInterfaceStatsTxPauseHonored
        oid: *******.4.1.14988.********.1.84
        type: counter
        help: ' - *******.4.1.14988.********.1.84'
        indexes:
        - labelname: mtxrInterfaceStatsIndex
          type: gauge
        lookups:
        - labels:
          - mtxrInterfaceStatsIndex
          labelname: ifName
          oid: *******.********.1.1.1
          type: DisplayString
      - name: mtxrInterfaceStatsTxDrop
        oid: *******.4.1.14988.********.1.85
        type: counter
        help: ' - *******.4.1.14988.********.1.85'
        indexes:
        - labelname: mtxrInterfaceStatsIndex
          type: gauge
        lookups:
        - labels:
          - mtxrInterfaceStatsIndex
          labelname: ifName
          oid: *******.********.1.1.1
          type: DisplayString
      - name: mtxrInterfaceStatsTxJabber
        oid: *******.4.1.14988.********.1.86
        type: counter
        help: ' - *******.4.1.14988.********.1.86'
        indexes:
        - labelname: mtxrInterfaceStatsIndex
          type: gauge
        lookups:
        - labels:
          - mtxrInterfaceStatsIndex
          labelname: ifName
          oid: *******.********.1.1.1
          type: DisplayString
      - name: mtxrInterfaceStatsTxFCSError
        oid: *******.4.1.14988.********.1.87
        type: counter
        help: ' - *******.4.1.14988.********.1.87'
        indexes:
        - labelname: mtxrInterfaceStatsIndex
          type: gauge
        lookups:
        - labels:
          - mtxrInterfaceStatsIndex
          labelname: ifName
          oid: *******.********.1.1.1
          type: DisplayString
      - name: mtxrInterfaceStatsTxControl
        oid: *******.4.1.14988.********.1.88
        type: counter
        help: ' - *******.4.1.14988.********.1.88'
        indexes:
        - labelname: mtxrInterfaceStatsIndex
          type: gauge
        lookups:
        - labels:
          - mtxrInterfaceStatsIndex
          labelname: ifName
          oid: *******.********.1.1.1
          type: DisplayString
      - name: mtxrInterfaceStatsTxFragment
        oid: *******.4.1.14988.********.1.89
        type: counter
        help: ' - *******.4.1.14988.********.1.89'
        indexes:
        - labelname: mtxrInterfaceStatsIndex
          type: gauge
        lookups:
        - labels:
          - mtxrInterfaceStatsIndex
          labelname: ifName
          oid: *******.********.1.1.1
          type: DisplayString
      - name: mtxrInterfaceStatsLinkDowns
        oid: *******.4.1.14988.********.1.90
        type: counter
        help: ' - *******.4.1.14988.********.1.90'
        indexes:
        - labelname: mtxrInterfaceStatsIndex
          type: gauge
        lookups:
        - labels:
          - mtxrInterfaceStatsIndex
          labelname: ifName
          oid: *******.********.1.1.1
          type: DisplayString
      - name: mtxrPOEInterfaceIndex
        oid: *******.4.1.14988.1.1.15.1.1.1
        type: gauge
        help: ' - *******.4.1.14988.1.1.15.1.1.1'
        indexes:
        - labelname: mtxrPOEInterfaceIndex
          type: gauge
      - name: mtxrPOEName
        oid: *******.4.1.14988.1.1.15.1.1.2
        type: DisplayString
        help: ' - *******.4.1.14988.1.1.15.1.1.2'
        indexes:
        - labelname: mtxrPOEInterfaceIndex
          type: gauge
      - name: mtxrPOEStatus
        oid: *******.4.1.14988.1.1.15.1.1.3
        type: gauge
        help: ' - *******.4.1.14988.1.1.15.1.1.3'
        indexes:
        - labelname: mtxrPOEInterfaceIndex
          type: gauge
        enum_values:
          1: disabled
          2: waitingForLoad
          3: poweredOn
          4: overload
      - name: mtxrPOEVoltage
        oid: *******.4.1.14988.1.1.15.1.1.4
        type: gauge
        help: V - *******.4.1.14988.1.1.15.1.1.4
        indexes:
        - labelname: mtxrPOEInterfaceIndex
          type: gauge
      - name: mtxrPOECurrent
        oid: *******.4.1.14988.1.1.15.1.1.5
        type: gauge
        help: mA - *******.4.1.14988.1.1.15.1.1.5
        indexes:
        - labelname: mtxrPOEInterfaceIndex
          type: gauge
      - name: mtxrPOEPower
        oid: *******.4.1.14988.1.1.15.1.1.6
        type: gauge
        help: W - *******.4.1.14988.1.1.15.1.1.6
        indexes:
        - labelname: mtxrPOEInterfaceIndex
          type: gauge
      - name: mtxrLTEModemInterfaceIndex
        oid: *******.4.1.14988.1.1.16.1.1.1
        type: gauge
        help: ' - *******.4.1.14988.1.1.16.1.1.1'
        indexes:
        - labelname: mtxrLTEModemInterfaceIndex
          type: gauge
      - name: mtxrLTEModemSignalRSSI
        oid: *******.4.1.14988.1.1.16.1.1.2
        type: gauge
        help: dBm - *******.4.1.14988.1.1.16.1.1.2
        indexes:
        - labelname: mtxrLTEModemInterfaceIndex
          type: gauge
      - name: mtxrLTEModemSignalRSRQ
        oid: *******.4.1.14988.1.1.16.1.1.3
        type: gauge
        help: dB - *******.4.1.14988.1.1.16.1.1.3
        indexes:
        - labelname: mtxrLTEModemInterfaceIndex
          type: gauge
      - name: mtxrLTEModemSignalRSRP
        oid: *******.4.1.14988.1.1.16.1.1.4
        type: gauge
        help: dBm - *******.4.1.14988.1.1.16.1.1.4
        indexes:
        - labelname: mtxrLTEModemInterfaceIndex
          type: gauge
      - name: mtxrLTEModemCellId
        oid: *******.4.1.14988.1.1.16.1.1.5
        type: gauge
        help: current cell ID - *******.4.1.14988.1.1.16.1.1.5
        indexes:
        - labelname: mtxrLTEModemInterfaceIndex
          type: gauge
      - name: mtxrLTEModemAccessTechnology
        oid: *******.4.1.14988.1.1.16.1.1.6
        type: gauge
        help: as reported by +CREG - *******.4.1.14988.1.1.16.1.1.6
        indexes:
        - labelname: mtxrLTEModemInterfaceIndex
          type: gauge
        enum_values:
          -1: unknown
          0: gsmcompact
          1: gsm
          2: utran
          3: egprs
          4: hsdpa
          5: hsupa
          6: hsdpahsupa
          7: eutran
      - name: mtxrLTEModemSignalSINR
        oid: *******.4.1.14988.1.1.16.1.1.7
        type: gauge
        help: dB - *******.4.1.14988.1.1.16.1.1.7
        indexes:
        - labelname: mtxrLTEModemInterfaceIndex
          type: gauge
      - name: mtxrLTEModemEnbId
        oid: *******.4.1.14988.1.1.16.1.1.8
        type: gauge
        help: ' - *******.4.1.14988.1.1.16.1.1.8'
        indexes:
        - labelname: mtxrLTEModemInterfaceIndex
          type: gauge
      - name: mtxrLTEModemSectorId
        oid: *******.4.1.14988.1.1.16.1.1.9
        type: gauge
        help: ' - *******.4.1.14988.1.1.16.1.1.9'
        indexes:
        - labelname: mtxrLTEModemInterfaceIndex
          type: gauge
      - name: mtxrLTEModemLac
        oid: *******.4.1.14988.1.1.16.1.1.10
        type: gauge
        help: ' - *******.4.1.14988.1.1.16.1.1.10'
        indexes:
        - labelname: mtxrLTEModemInterfaceIndex
          type: gauge
      - name: mtxrLTEModemIMEI
        oid: *******.4.1.14988.1.1.16.1.1.11
        type: DisplayString
        help: ' - *******.4.1.14988.1.1.16.1.1.11'
        indexes:
        - labelname: mtxrLTEModemInterfaceIndex
          type: gauge
      - name: mtxrLTEModemIMSI
        oid: *******.4.1.14988.1.1.16.1.1.12
        type: DisplayString
        help: ' - *******.4.1.14988.1.1.16.1.1.12'
        indexes:
        - labelname: mtxrLTEModemInterfaceIndex
          type: gauge
      - name: mtxrLTEModemUICC
        oid: *******.4.1.14988.1.1.16.1.1.13
        type: DisplayString
        help: ' - *******.4.1.14988.1.1.16.1.1.13'
        indexes:
        - labelname: mtxrLTEModemInterfaceIndex
          type: gauge
      - name: mtxrLTEModemRAT
        oid: *******.4.1.14988.1.1.16.1.1.14
        type: DisplayString
        help: ' - *******.4.1.14988.1.1.16.1.1.14'
        indexes:
        - labelname: mtxrLTEModemInterfaceIndex
          type: gauge
      - name: mtxrPartitionIndex
        oid: *******.4.1.14988.********.1.1
        type: gauge
        help: ' - *******.4.1.14988.********.1.1'
        indexes:
        - labelname: mtxrPartitionIndex
          type: gauge
      - name: mtxrPartitionName
        oid: *******.4.1.14988.********.1.2
        type: DisplayString
        help: ' - *******.4.1.14988.********.1.2'
        indexes:
        - labelname: mtxrPartitionIndex
          type: gauge
      - name: mtxrPartitionSize
        oid: *******.4.1.14988.********.1.3
        type: gauge
        help: MB - *******.4.1.14988.********.1.3
        indexes:
        - labelname: mtxrPartitionIndex
          type: gauge
      - name: mtxrPartitionVersion
        oid: *******.4.1.14988.********.1.4
        type: DisplayString
        help: ' - *******.4.1.14988.********.1.4'
        indexes:
        - labelname: mtxrPartitionIndex
          type: gauge
      - name: mtxrPartitionActive
        oid: *******.4.1.14988.********.1.5
        type: gauge
        help: ' - *******.4.1.14988.********.1.5'
        indexes:
        - labelname: mtxrPartitionIndex
          type: gauge
        enum_values:
          0: "false"
          1: "true"
      - name: mtxrPartitionRunning
        oid: *******.4.1.14988.********.1.6
        type: gauge
        help: ' - *******.4.1.14988.********.1.6'
        indexes:
        - labelname: mtxrPartitionIndex
          type: gauge
        enum_values:
          0: "false"
          1: "true"
      - name: mtxrScriptRunIndex
        oid: *******.4.1.14988.********.1.1
        type: gauge
        help: ' - *******.4.1.14988.********.1.1'
        indexes:
        - labelname: mtxrScriptRunIndex
          type: gauge
      - name: mtxrScriptRunOutput
        oid: *******.4.1.14988.********.1.2
        type: DisplayString
        help: this oid on get request will run script and return it's output - *******.4.1.14988.********.1.2
        indexes:
        - labelname: mtxrScriptRunIndex
          type: gauge
      - name: mtxrOpticalIndex
        oid: *******.4.1.14988.********.1.1
        type: gauge
        help: ' - *******.4.1.14988.********.1.1'
        indexes:
        - labelname: mtxrOpticalIndex
          type: gauge
      - name: mtxrOpticalName
        oid: *******.4.1.14988.********.1.2
        type: DisplayString
        help: ' - *******.4.1.14988.********.1.2'
        indexes:
        - labelname: mtxrOpticalIndex
          type: gauge
      - name: mtxrOpticalRxLoss
        oid: *******.4.1.14988.********.1.3
        type: gauge
        help: ' - *******.4.1.14988.********.1.3'
        indexes:
        - labelname: mtxrOpticalIndex
          type: gauge
        enum_values:
          0: "false"
          1: "true"
      - name: mtxrOpticalTxFault
        oid: *******.4.1.14988.********.1.4
        type: gauge
        help: ' - *******.4.1.14988.********.1.4'
        indexes:
        - labelname: mtxrOpticalIndex
          type: gauge
        enum_values:
          0: "false"
          1: "true"
      - name: mtxrOpticalWavelength
        oid: *******.4.1.14988.********.1.5
        type: gauge
        help: ' - *******.4.1.14988.********.1.5'
        indexes:
        - labelname: mtxrOpticalIndex
          type: gauge
      - name: mtxrOpticalTemperature
        oid: *******.4.1.14988.********.1.6
        type: gauge
        help: ' - *******.4.1.14988.********.1.6'
        indexes:
        - labelname: mtxrOpticalIndex
          type: gauge
      - name: mtxrOpticalSupplyVoltage
        oid: *******.4.1.14988.********.1.7
        type: gauge
        help: ' - *******.4.1.14988.********.1.7'
        indexes:
        - labelname: mtxrOpticalIndex
          type: gauge
      - name: mtxrOpticalTxBiasCurrent
        oid: *******.4.1.14988.********.1.8
        type: gauge
        help: ' - *******.4.1.14988.********.1.8'
        indexes:
        - labelname: mtxrOpticalIndex
          type: gauge
      - name: mtxrOpticalTxPower
        oid: *******.4.1.14988.********.1.9
        type: gauge
        help: ' - *******.4.1.14988.********.1.9'
        indexes:
        - labelname: mtxrOpticalIndex
          type: gauge
      - name: mtxrOpticalRxPower
        oid: *******.4.1.14988.********.1.10
        type: gauge
        help: ' - *******.4.1.14988.********.1.10'
        indexes:
        - labelname: mtxrOpticalIndex
          type: gauge
      - name: mtxrIkeSACount
        oid: *******.4.1.14988.1.1.20.1
        type: gauge
        help: IKE SA count - *******.4.1.14988.1.1.20.1
      - name: mtxrIkeSAIndex
        oid: *******.4.1.14988.********.1.1
        type: gauge
        help: ' - *******.4.1.14988.********.1.1'
        indexes:
        - labelname: mtxrIkeSAIndex
          type: gauge
      - name: mtxrIkeSAInitiatorCookie
        oid: *******.4.1.14988.********.1.2
        type: DisplayString
        help: initiator SPI - *******.4.1.14988.********.1.2
        indexes:
        - labelname: mtxrIkeSAIndex
          type: gauge
      - name: mtxrIkeSAResponderCookie
        oid: *******.4.1.14988.********.1.3
        type: DisplayString
        help: responder SPI - *******.4.1.14988.********.1.3
        indexes:
        - labelname: mtxrIkeSAIndex
          type: gauge
      - name: mtxrIkeSAResponder
        oid: *******.4.1.14988.********.1.4
        type: gauge
        help: IKE side - *******.4.1.14988.********.1.4
        indexes:
        - labelname: mtxrIkeSAIndex
          type: gauge
        enum_values:
          0: "false"
          1: "true"
      - name: mtxrIkeSANatt
        oid: *******.4.1.14988.********.1.5
        type: gauge
        help: NAT is detected - *******.4.1.14988.********.1.5
        indexes:
        - labelname: mtxrIkeSAIndex
          type: gauge
        enum_values:
          0: "false"
          1: "true"
      - name: mtxrIkeSAVersion
        oid: *******.4.1.14988.********.1.6
        type: gauge
        help: protocol version - *******.4.1.14988.********.1.6
        indexes:
        - labelname: mtxrIkeSAIndex
          type: gauge
      - name: mtxrIkeSAState
        oid: *******.4.1.14988.********.1.7
        type: gauge
        help: ' - *******.4.1.14988.********.1.7'
        indexes:
        - labelname: mtxrIkeSAIndex
          type: gauge
        enum_values:
          1: exchange
          2: established
          3: expired
          4: eap
      - name: mtxrIkeSAUptime
        oid: *******.4.1.14988.********.1.8
        type: gauge
        help: ' - *******.4.1.14988.********.1.8'
        indexes:
        - labelname: mtxrIkeSAIndex
          type: gauge
      - name: mtxrIkeSASeen
        oid: *******.4.1.14988.********.1.9
        type: gauge
        help: time elapsed since last valid IKE packet - *******.4.1.14988.********.1.9
        indexes:
        - labelname: mtxrIkeSAIndex
          type: gauge
      - name: mtxrIkeSAIdentity
        oid: *******.4.1.14988.********.1.10
        type: DisplayString
        help: peer identity - *******.4.1.14988.********.1.10
        indexes:
        - labelname: mtxrIkeSAIndex
          type: gauge
      - name: mtxrIkeSAPh2Count
        oid: *******.4.1.14988.********.1.11
        type: gauge
        help: total ph2 SA pairs - *******.4.1.14988.********.1.11
        indexes:
        - labelname: mtxrIkeSAIndex
          type: gauge
      - name: mtxrIkeSALocalAddressType
        oid: *******.4.1.14988.********.1.12
        type: gauge
        help: ' - *******.4.1.14988.********.1.12'
        indexes:
        - labelname: mtxrIkeSAIndex
          type: gauge
        enum_values:
          0: unknown
          1: ipv4
          2: ipv6
          3: ipv4z
          4: ipv6z
          16: dns
      - name: mtxrIkeSALocalAddress
        oid: *******.4.1.14988.********.1.13
        type: InetAddress
        help: ' - *******.4.1.14988.********.1.13'
        indexes:
        - labelname: mtxrIkeSAIndex
          type: gauge
      - name: mtxrIkeSALocalPort
        oid: *******.4.1.14988.********.1.14
        type: gauge
        help: ' - *******.4.1.14988.********.1.14'
        indexes:
        - labelname: mtxrIkeSAIndex
          type: gauge
      - name: mtxrIkeSAPeerAddressType
        oid: *******.4.1.14988.********.1.15
        type: gauge
        help: ' - *******.4.1.14988.********.1.15'
        indexes:
        - labelname: mtxrIkeSAIndex
          type: gauge
        enum_values:
          0: unknown
          1: ipv4
          2: ipv6
          3: ipv4z
          4: ipv6z
          16: dns
      - name: mtxrIkeSAPeerAddress
        oid: *******.4.1.14988.********.1.16
        type: InetAddress
        help: ' - *******.4.1.14988.********.1.16'
        indexes:
        - labelname: mtxrIkeSAIndex
          type: gauge
      - name: mtxrIkeSAPeerPort
        oid: *******.4.1.14988.********.1.17
        type: gauge
        help: ' - *******.4.1.14988.********.1.17'
        indexes:
        - labelname: mtxrIkeSAIndex
          type: gauge
      - name: mtxrIkeSADynamicAddressType
        oid: *******.4.1.14988.********.1.18
        type: gauge
        help: ' - *******.4.1.14988.********.1.18'
        indexes:
        - labelname: mtxrIkeSAIndex
          type: gauge
        enum_values:
          0: unknown
          1: ipv4
          2: ipv6
          3: ipv4z
          4: ipv6z
          16: dns
      - name: mtxrIkeSADynamicAddress
        oid: *******.4.1.14988.********.1.19
        type: InetAddress
        help: dynamic address allocated by mode config - *******.4.1.14988.********.1.19
        indexes:
        - labelname: mtxrIkeSAIndex
          type: gauge
      - name: mtxrIkeSATxBytes
        oid: *******.4.1.14988.********.1.20
        type: counter
        help: ph2 SA tx bytes - *******.4.1.14988.********.1.20
        indexes:
        - labelname: mtxrIkeSAIndex
          type: gauge
      - name: mtxrIkeSARxBytes
        oid: *******.4.1.14988.********.1.21
        type: counter
        help: ph2 SA rx bytes - *******.4.1.14988.********.1.21
        indexes:
        - labelname: mtxrIkeSAIndex
          type: gauge
      - name: mtxrIkeSATxPackets
        oid: *******.4.1.14988.********.1.22
        type: counter
        help: ph2 SA tx packets - *******.4.1.14988.********.1.22
        indexes:
        - labelname: mtxrIkeSAIndex
          type: gauge
      - name: mtxrIkeSARxPackets
        oid: *******.4.1.14988.********.1.23
        type: counter
        help: ph2 SA rx packets - *******.4.1.14988.********.1.23
        indexes:
        - labelname: mtxrIkeSAIndex
          type: gauge
      - name: laIndex
        oid: *******.4.1.2021.10.1.1
        type: gauge
        help: reference index/row number for each observed loadave. - *******.4.1.2021.10.1.1
        indexes:
        - labelname: laIndex
          type: gauge
        lookups:
        - labels:
          - laIndex
          labelname: laNames
          oid: *******.4.1.2021.10.1.2
          type: DisplayString
        - labels: []
          labelname: laIndex
kind: ConfigMap
metadata:
  name: snmp-exporter-mikrotik-configmap
  namespace: snmp-exporter
---
apiVersion: v1
data:
  snmp.yml: |
    # WARNING: This file was auto-generated using snmp_exporter generator, manual changes will be lost.
    synology:
      walk:
      - *******.2.1.2
      - *******.********
      - *******.********.1
      - *******.4.1.2021.10.1.2
      - *******.4.1.2021.10.1.5
      - *******.4.1.2021.4
      - *******.4.1.6574.1
      - *******.4.1.6574.101
      - *******.4.1.6574.102
      - *******.4.1.6574.104
      - *******.4.1.6574.2
      - *******.4.1.6574.3
      - *******.4.1.6574.4
      - *******.4.1.6574.5
      - *******.4.1.6574.6
      get:
      - *******.*******.0
      - *******.4.1.2021.11.10.0
      - *******.4.1.2021.11.11.0
      - *******.4.1.2021.11.9.0
      metrics:
      - name: sysUpTime
        oid: *******.*******
        type: gauge
        help: The time (in hundredths of a second) since the network management portion
          of the system was last re-initialized. - *******.*******
      - name: ifNumber
        oid: *******.*******
        type: gauge
        help: The number of network interfaces (regardless of their current state) present
          on this system. - *******.*******
      - name: ifIndex
        oid: *******.*******.1.1
        type: gauge
        help: A unique value, greater than zero, for each interface - *******.*******.1.1
        indexes:
        - labelname: ifIndex
          type: gauge
        lookups:
        - labels:
          - ifIndex
          labelname: ifName
          oid: *******.********.1.1.1
          type: DisplayString
        - labels: []
          labelname: ifIndex
      - name: ifDescr
        oid: *******.*******.1.2
        type: DisplayString
        help: A textual string containing information about the interface - *******.*******.1.2
        indexes:
        - labelname: ifIndex
          type: gauge
        lookups:
        - labels:
          - ifIndex
          labelname: ifName
          oid: *******.********.1.1.1
          type: DisplayString
        - labels: []
          labelname: ifIndex
      - name: ifType
        oid: *******.*******.1.3
        type: EnumAsInfo
        help: The type of interface - *******.*******.1.3
        indexes:
        - labelname: ifIndex
          type: gauge
        lookups:
        - labels:
          - ifIndex
          labelname: ifName
          oid: *******.********.1.1.1
          type: DisplayString
        - labels: []
          labelname: ifIndex
        enum_values:
          1: other
          2: regular1822
          3: hdh1822
          4: ddnX25
          5: rfc877x25
          6: ethernetCsmacd
          7: iso88023Csmacd
          8: iso88024TokenBus
          9: iso88025TokenRing
          10: iso88026Man
          11: starLan
          12: proteon10Mbit
          13: proteon80Mbit
          14: hyperchannel
          15: fddi
          16: lapb
          17: sdlc
          18: ds1
          19: e1
          20: basicISDN
          21: primaryISDN
          22: propPointToPointSerial
          23: ppp
          24: softwareLoopback
          25: eon
          26: ethernet3Mbit
          27: nsip
          28: slip
          29: ultra
          30: ds3
          31: sip
          32: frameRelay
          33: rs232
          34: para
          35: arcnet
          36: arcnetPlus
          37: atm
          38: miox25
          39: sonet
          40: x25ple
          41: iso88022llc
          42: localTalk
          43: smdsDxi
          44: frameRelayService
          45: v35
          46: hssi
          47: hippi
          48: modem
          49: aal5
          50: sonetPath
          51: sonetVT
          52: smdsIcip
          53: propVirtual
          54: propMultiplexor
          55: ieee80212
          56: fibreChannel
          57: hippiInterface
          58: frameRelayInterconnect
          59: aflane8023
          60: aflane8025
          61: cctEmul
          62: fastEther
          63: isdn
          64: v11
          65: v36
          66: g703at64k
          67: g703at2mb
          68: qllc
          69: fastEtherFX
          70: channel
          71: ieee80211
          72: ibm370parChan
          73: escon
          74: dlsw
          75: isdns
          76: isdnu
          77: lapd
          78: ipSwitch
          79: rsrb
          80: atmLogical
          81: ds0
          82: ds0Bundle
          83: bsc
          84: async
          85: cnr
          86: iso88025Dtr
          87: eplrs
          88: arap
          89: propCnls
          90: hostPad
          91: termPad
          92: frameRelayMPI
          93: x213
          94: adsl
          95: radsl
          96: sdsl
          97: vdsl
          98: iso88025CRFPInt
          99: myrinet
          100: voiceEM
          101: voiceFXO
          102: voiceFXS
          103: voiceEncap
          104: voiceOverIp
          105: atmDxi
          106: atmFuni
          107: atmIma
          108: pppMultilinkBundle
          109: ipOverCdlc
          110: ipOverClaw
          111: stackToStack
          112: virtualIpAddress
          113: mpc
          114: ipOverAtm
          115: iso88025Fiber
          116: tdlc
          117: gigabitEthernet
          118: hdlc
          119: lapf
          120: v37
          121: x25mlp
          122: x25huntGroup
          123: transpHdlc
          124: interleave
          125: fast
          126: ip
          127: docsCableMaclayer
          128: docsCableDownstream
          129: docsCableUpstream
          130: a12MppSwitch
          131: tunnel
          132: coffee
          133: ces
          134: atmSubInterface
          135: l2vlan
          136: l3ipvlan
          137: l3ipxvlan
          138: digitalPowerline
          139: mediaMailOverIp
          140: dtm
          141: dcn
          142: ipForward
          143: msdsl
          144: ieee1394
          145: if-gsn
          146: dvbRccMacLayer
          147: dvbRccDownstream
          148: dvbRccUpstream
          149: atmVirtual
          150: mplsTunnel
          151: srp
          152: voiceOverAtm
          153: voiceOverFrameRelay
          154: idsl
          155: compositeLink
          156: ss7SigLink
          157: propWirelessP2P
          158: frForward
          159: rfc1483
          160: usb
          161: ieee8023adLag
          162: bgppolicyaccounting
          163: frf16MfrBundle
          164: h323Gatekeeper
          165: h323Proxy
          166: mpls
          167: mfSigLink
          168: hdsl2
          169: shdsl
          170: ds1FDL
          171: pos
          172: dvbAsiIn
          173: dvbAsiOut
          174: plc
          175: nfas
          176: tr008
          177: gr303RDT
          178: gr303IDT
          179: isup
          180: propDocsWirelessMaclayer
          181: propDocsWirelessDownstream
          182: propDocsWirelessUpstream
          183: hiperlan2
          184: propBWAp2Mp
          185: sonetOverheadChannel
          186: digitalWrapperOverheadChannel
          187: aal2
          188: radioMAC
          189: atmRadio
          190: imt
          191: mvl
          192: reachDSL
          193: frDlciEndPt
          194: atmVciEndPt
          195: opticalChannel
          196: opticalTransport
          197: propAtm
          198: voiceOverCable
          199: infiniband
          200: teLink
          201: q2931
          202: virtualTg
          203: sipTg
          204: sipSig
          205: docsCableUpstreamChannel
          206: econet
          207: pon155
          208: pon622
          209: bridge
          210: linegroup
          211: voiceEMFGD
          212: voiceFGDEANA
          213: voiceDID
          214: mpegTransport
          215: sixToFour
          216: gtp
          217: pdnEtherLoop1
          218: pdnEtherLoop2
          219: opticalChannelGroup
          220: homepna
          221: gfp
          222: ciscoISLvlan
          223: actelisMetaLOOP
          224: fcipLink
          225: rpr
          226: qam
          227: lmp
          228: cblVectaStar
          229: docsCableMCmtsDownstream
          230: adsl2
          231: macSecControlledIF
          232: macSecUncontrolledIF
          233: aviciOpticalEther
          234: atmbond
          235: voiceFGDOS
          236: mocaVersion1
          237: ieee80216WMAN
          238: adsl2plus
          239: dvbRcsMacLayer
          240: dvbTdm
          241: dvbRcsTdma
          242: x86Laps
          243: wwanPP
          244: wwanPP2
          245: voiceEBS
          246: ifPwType
          247: ilan
          248: pip
          249: aluELP
          250: gpon
          251: vdsl2
          252: capwapDot11Profile
          253: capwapDot11Bss
          254: capwapWtpVirtualRadio
          255: bits
          256: docsCableUpstreamRfPort
          257: cableDownstreamRfPort
          258: vmwareVirtualNic
          259: ieee802154
          260: otnOdu
          261: otnOtu
          262: ifVfiType
          263: g9981
          264: g9982
          265: g9983
          266: aluEpon
          267: aluEponOnu
          268: aluEponPhysicalUni
          269: aluEponLogicalLink
          270: aluGponOnu
          271: aluGponPhysicalUni
          272: vmwareNicTeam
          277: docsOfdmDownstream
          278: docsOfdmaUpstream
          279: gfast
          280: sdci
          281: xboxWireless
          282: fastdsl
          283: docsCableScte55d1FwdOob
          284: docsCableScte55d1RetOob
          285: docsCableScte55d2DsOob
          286: docsCableScte55d2UsOob
          287: docsCableNdf
          288: docsCableNdr
          289: ptm
          290: ghn
          291: otnOtsi
          292: otnOtuc
          293: otnOduc
          294: otnOtsig
          295: microwaveCarrierTermination
          296: microwaveRadioLinkTerminal
          297: ieee8021axDrni
          298: ax25
          299: ieee19061nanocom
          300: cpri
          301: omni
          302: roe
          303: p2pOverLan
      - name: ifMtu
        oid: *******.*******.1.4
        type: gauge
        help: The size of the largest packet which can be sent/received on the interface,
          specified in octets - *******.*******.1.4
        indexes:
        - labelname: ifIndex
          type: gauge
        lookups:
        - labels:
          - ifIndex
          labelname: ifName
          oid: *******.********.1.1.1
          type: DisplayString
        - labels: []
          labelname: ifIndex
      - name: ifSpeed
        oid: *******.*******.1.5
        type: gauge
        help: An estimate of the interface's current bandwidth in bits per second - *******.*******.1.5
        indexes:
        - labelname: ifIndex
          type: gauge
        lookups:
        - labels:
          - ifIndex
          labelname: ifName
          oid: *******.********.1.1.1
          type: DisplayString
        - labels: []
          labelname: ifIndex
      - name: ifPhysAddress
        oid: *******.*******.1.6
        type: PhysAddress48
        help: The interface's address at its protocol sub-layer - *******.*******.1.6
        indexes:
        - labelname: ifIndex
          type: gauge
        lookups:
        - labels:
          - ifIndex
          labelname: ifName
          oid: *******.********.1.1.1
          type: DisplayString
        - labels: []
          labelname: ifIndex
      - name: ifAdminStatus
        oid: *******.*******.1.7
        type: gauge
        help: The desired state of the interface - *******.*******.1.7
        indexes:
        - labelname: ifIndex
          type: gauge
        lookups:
        - labels:
          - ifIndex
          labelname: ifName
          oid: *******.********.1.1.1
          type: DisplayString
        - labels: []
          labelname: ifIndex
        enum_values:
          1: up
          2: down
          3: testing
      - name: ifOperStatus
        oid: *******.*******.1.8
        type: gauge
        help: The current operational state of the interface - *******.*******.1.8
        indexes:
        - labelname: ifIndex
          type: gauge
        lookups:
        - labels:
          - ifIndex
          labelname: ifName
          oid: *******.********.1.1.1
          type: DisplayString
        - labels: []
          labelname: ifIndex
        enum_values:
          1: up
          2: down
          3: testing
          4: unknown
          5: dormant
          6: notPresent
          7: lowerLayerDown
      - name: ifLastChange
        oid: *******.*******.1.9
        type: gauge
        help: The value of sysUpTime at the time the interface entered its current operational
          state - *******.*******.1.9
        indexes:
        - labelname: ifIndex
          type: gauge
        lookups:
        - labels:
          - ifIndex
          labelname: ifName
          oid: *******.********.1.1.1
          type: DisplayString
        - labels: []
          labelname: ifIndex
      - name: ifInOctets
        oid: *******.*******.1.10
        type: counter
        help: The total number of octets received on the interface, including framing
          characters - *******.*******.1.10
        indexes:
        - labelname: ifIndex
          type: gauge
        lookups:
        - labels:
          - ifIndex
          labelname: ifName
          oid: *******.********.1.1.1
          type: DisplayString
        - labels: []
          labelname: ifIndex
      - name: ifInUcastPkts
        oid: *******.*******.1.11
        type: counter
        help: The number of packets, delivered by this sub-layer to a higher (sub-)layer,
          which were not addressed to a multicast or broadcast address at this sub-layer
          - *******.*******.1.11
        indexes:
        - labelname: ifIndex
          type: gauge
        lookups:
        - labels:
          - ifIndex
          labelname: ifName
          oid: *******.********.1.1.1
          type: DisplayString
        - labels: []
          labelname: ifIndex
      - name: ifInNUcastPkts
        oid: *******.*******.1.12
        type: counter
        help: The number of packets, delivered by this sub-layer to a higher (sub-)layer,
          which were addressed to a multicast or broadcast address at this sub-layer -
          *******.*******.1.12
        indexes:
        - labelname: ifIndex
          type: gauge
        lookups:
        - labels:
          - ifIndex
          labelname: ifName
          oid: *******.********.1.1.1
          type: DisplayString
        - labels: []
          labelname: ifIndex
      - name: ifInDiscards
        oid: *******.*******.1.13
        type: counter
        help: The number of inbound packets which were chosen to be discarded even though
          no errors had been detected to prevent their being deliverable to a higher-layer
          protocol - *******.*******.1.13
        indexes:
        - labelname: ifIndex
          type: gauge
        lookups:
        - labels:
          - ifIndex
          labelname: ifName
          oid: *******.********.1.1.1
          type: DisplayString
        - labels: []
          labelname: ifIndex
      - name: ifInErrors
        oid: *******.*******.1.14
        type: counter
        help: For packet-oriented interfaces, the number of inbound packets that contained
          errors preventing them from being deliverable to a higher-layer protocol - *******.*******.1.14
        indexes:
        - labelname: ifIndex
          type: gauge
        lookups:
        - labels:
          - ifIndex
          labelname: ifName
          oid: *******.********.1.1.1
          type: DisplayString
        - labels: []
          labelname: ifIndex
      - name: ifInUnknownProtos
        oid: *******.*******.1.15
        type: counter
        help: For packet-oriented interfaces, the number of packets received via the interface
          which were discarded because of an unknown or unsupported protocol - *******.*******.1.15
        indexes:
        - labelname: ifIndex
          type: gauge
        lookups:
        - labels:
          - ifIndex
          labelname: ifName
          oid: *******.********.1.1.1
          type: DisplayString
        - labels: []
          labelname: ifIndex
      - name: ifOutOctets
        oid: *******.*******.1.16
        type: counter
        help: The total number of octets transmitted out of the interface, including framing
          characters - *******.*******.1.16
        indexes:
        - labelname: ifIndex
          type: gauge
        lookups:
        - labels:
          - ifIndex
          labelname: ifName
          oid: *******.********.1.1.1
          type: DisplayString
        - labels: []
          labelname: ifIndex
      - name: ifOutUcastPkts
        oid: *******.*******.1.17
        type: counter
        help: The total number of packets that higher-level protocols requested be transmitted,
          and which were not addressed to a multicast or broadcast address at this sub-layer,
          including those that were discarded or not sent - *******.*******.1.17
        indexes:
        - labelname: ifIndex
          type: gauge
        lookups:
        - labels:
          - ifIndex
          labelname: ifName
          oid: *******.********.1.1.1
          type: DisplayString
        - labels: []
          labelname: ifIndex
      - name: ifOutNUcastPkts
        oid: *******.*******.1.18
        type: counter
        help: The total number of packets that higher-level protocols requested be transmitted,
          and which were addressed to a multicast or broadcast address at this sub-layer,
          including those that were discarded or not sent - *******.*******.1.18
        indexes:
        - labelname: ifIndex
          type: gauge
        lookups:
        - labels:
          - ifIndex
          labelname: ifName
          oid: *******.********.1.1.1
          type: DisplayString
        - labels: []
          labelname: ifIndex
      - name: ifOutDiscards
        oid: *******.*******.1.19
        type: counter
        help: The number of outbound packets which were chosen to be discarded even though
          no errors had been detected to prevent their being transmitted - *******.*******.1.19
        indexes:
        - labelname: ifIndex
          type: gauge
        lookups:
        - labels:
          - ifIndex
          labelname: ifName
          oid: *******.********.1.1.1
          type: DisplayString
        - labels: []
          labelname: ifIndex
      - name: ifOutErrors
        oid: *******.*******.1.20
        type: counter
        help: For packet-oriented interfaces, the number of outbound packets that could
          not be transmitted because of errors - *******.*******.1.20
        indexes:
        - labelname: ifIndex
          type: gauge
        lookups:
        - labels:
          - ifIndex
          labelname: ifName
          oid: *******.********.1.1.1
          type: DisplayString
        - labels: []
          labelname: ifIndex
      - name: ifOutQLen
        oid: *******.*******.1.21
        type: gauge
        help: The length of the output packet queue (in packets). - *******.*******.1.21
        indexes:
        - labelname: ifIndex
          type: gauge
        lookups:
        - labels:
          - ifIndex
          labelname: ifName
          oid: *******.********.1.1.1
          type: DisplayString
        - labels: []
          labelname: ifIndex
      - name: ifSpecific
        oid: *******.*******.1.22
        type: OctetString
        help: A reference to MIB definitions specific to the particular media being used
          to realize the interface - *******.*******.1.22
        indexes:
        - labelname: ifIndex
          type: gauge
        lookups:
        - labels:
          - ifIndex
          labelname: ifName
          oid: *******.********.1.1.1
          type: DisplayString
        - labels: []
          labelname: ifIndex
      - name: hrMemorySize
        oid: *******.********.2
        type: gauge
        help: The amount of physical read-write main memory, typically RAM, contained
          by the host. - *******.********.2
      - name: hrStorageIndex
        oid: *******.********.3.1.1
        type: gauge
        help: A unique value for each logical storage area contained by the host. - *******.********.3.1.1
        indexes:
        - labelname: hrStorageIndex
          type: gauge
        lookups:
        - labels:
          - hrStorageIndex
          labelname: hrStorageDescr
          oid: *******.********.3.1.3
          type: DisplayString
        - labels: []
          labelname: hrStorageIndex
      - name: hrStorageType
        oid: *******.********.3.1.2
        type: OctetString
        help: The type of storage represented by this entry. - *******.********.3.1.2
        indexes:
        - labelname: hrStorageIndex
          type: gauge
        lookups:
        - labels:
          - hrStorageIndex
          labelname: hrStorageDescr
          oid: *******.********.3.1.3
          type: DisplayString
        - labels: []
          labelname: hrStorageIndex
      - name: hrStorageDescr
        oid: *******.********.3.1.3
        type: DisplayString
        help: A description of the type and instance of the storage described by this
          entry. - *******.********.3.1.3
        indexes:
        - labelname: hrStorageIndex
          type: gauge
        lookups:
        - labels:
          - hrStorageIndex
          labelname: hrStorageDescr
          oid: *******.********.3.1.3
          type: DisplayString
        - labels: []
          labelname: hrStorageIndex
      - name: hrStorageAllocationUnits
        oid: *******.********.3.1.4
        type: gauge
        help: The size, in bytes, of the data objects allocated from this pool - *******.********.3.1.4
        indexes:
        - labelname: hrStorageIndex
          type: gauge
        lookups:
        - labels:
          - hrStorageIndex
          labelname: hrStorageDescr
          oid: *******.********.3.1.3
          type: DisplayString
        - labels: []
          labelname: hrStorageIndex
      - name: hrStorageSize
        oid: *******.********.3.1.5
        type: gauge
        help: The size of the storage represented by this entry, in units of hrStorageAllocationUnits
          - *******.********.3.1.5
        indexes:
        - labelname: hrStorageIndex
          type: gauge
        lookups:
        - labels:
          - hrStorageIndex
          labelname: hrStorageDescr
          oid: *******.********.3.1.3
          type: DisplayString
        - labels: []
          labelname: hrStorageIndex
      - name: hrStorageUsed
        oid: *******.********.3.1.6
        type: gauge
        help: The amount of the storage represented by this entry that is allocated, in
          units of hrStorageAllocationUnits. - *******.********.3.1.6
        indexes:
        - labelname: hrStorageIndex
          type: gauge
        lookups:
        - labels:
          - hrStorageIndex
          labelname: hrStorageDescr
          oid: *******.********.3.1.3
          type: DisplayString
        - labels: []
          labelname: hrStorageIndex
      - name: hrStorageAllocationFailures
        oid: *******.********.3.1.7
        type: counter
        help: The number of requests for storage represented by this entry that could
          not be honored due to not enough storage - *******.********.3.1.7
        indexes:
        - labelname: hrStorageIndex
          type: gauge
        lookups:
        - labels:
          - hrStorageIndex
          labelname: hrStorageDescr
          oid: *******.********.3.1.3
          type: DisplayString
        - labels: []
          labelname: hrStorageIndex
      - name: ifName
        oid: *******.********.1.1.1
        type: DisplayString
        help: The textual name of the interface - *******.********.1.1.1
        indexes:
        - labelname: ifIndex
          type: gauge
        lookups:
        - labels:
          - ifIndex
          labelname: ifName
          oid: *******.********.1.1.1
          type: DisplayString
        - labels: []
          labelname: ifIndex
      - name: ifInMulticastPkts
        oid: *******.********.1.1.2
        type: counter
        help: The number of packets, delivered by this sub-layer to a higher (sub-)layer,
          which were addressed to a multicast address at this sub-layer - *******.********.1.1.2
        indexes:
        - labelname: ifIndex
          type: gauge
        lookups:
        - labels:
          - ifIndex
          labelname: ifName
          oid: *******.********.1.1.1
          type: DisplayString
        - labels: []
          labelname: ifIndex
      - name: ifInBroadcastPkts
        oid: *******.********.1.1.3
        type: counter
        help: The number of packets, delivered by this sub-layer to a higher (sub-)layer,
          which were addressed to a broadcast address at this sub-layer - *******.********.1.1.3
        indexes:
        - labelname: ifIndex
          type: gauge
        lookups:
        - labels:
          - ifIndex
          labelname: ifName
          oid: *******.********.1.1.1
          type: DisplayString
        - labels: []
          labelname: ifIndex
      - name: ifOutMulticastPkts
        oid: *******.********.1.1.4
        type: counter
        help: The total number of packets that higher-level protocols requested be transmitted,
          and which were addressed to a multicast address at this sub-layer, including
          those that were discarded or not sent - *******.********.1.1.4
        indexes:
        - labelname: ifIndex
          type: gauge
        lookups:
        - labels:
          - ifIndex
          labelname: ifName
          oid: *******.********.1.1.1
          type: DisplayString
        - labels: []
          labelname: ifIndex
      - name: ifOutBroadcastPkts
        oid: *******.********.1.1.5
        type: counter
        help: The total number of packets that higher-level protocols requested be transmitted,
          and which were addressed to a broadcast address at this sub-layer, including
          those that were discarded or not sent - *******.********.1.1.5
        indexes:
        - labelname: ifIndex
          type: gauge
        lookups:
        - labels:
          - ifIndex
          labelname: ifName
          oid: *******.********.1.1.1
          type: DisplayString
        - labels: []
          labelname: ifIndex
      - name: ifHCInOctets
        oid: *******.********.1.1.6
        type: counter
        help: The total number of octets received on the interface, including framing
          characters - *******.********.1.1.6
        indexes:
        - labelname: ifIndex
          type: gauge
        lookups:
        - labels:
          - ifIndex
          labelname: ifName
          oid: *******.********.1.1.1
          type: DisplayString
        - labels: []
          labelname: ifIndex
      - name: ifHCInUcastPkts
        oid: *******.********.1.1.7
        type: counter
        help: The number of packets, delivered by this sub-layer to a higher (sub-)layer,
          which were not addressed to a multicast or broadcast address at this sub-layer
          - *******.********.1.1.7
        indexes:
        - labelname: ifIndex
          type: gauge
        lookups:
        - labels:
          - ifIndex
          labelname: ifName
          oid: *******.********.1.1.1
          type: DisplayString
        - labels: []
          labelname: ifIndex
      - name: ifHCInMulticastPkts
        oid: *******.********.1.1.8
        type: counter
        help: The number of packets, delivered by this sub-layer to a higher (sub-)layer,
          which were addressed to a multicast address at this sub-layer - *******.********.1.1.8
        indexes:
        - labelname: ifIndex
          type: gauge
        lookups:
        - labels:
          - ifIndex
          labelname: ifName
          oid: *******.********.1.1.1
          type: DisplayString
        - labels: []
          labelname: ifIndex
      - name: ifHCInBroadcastPkts
        oid: *******.********.1.1.9
        type: counter
        help: The number of packets, delivered by this sub-layer to a higher (sub-)layer,
          which were addressed to a broadcast address at this sub-layer - *******.********.1.1.9
        indexes:
        - labelname: ifIndex
          type: gauge
        lookups:
        - labels:
          - ifIndex
          labelname: ifName
          oid: *******.********.1.1.1
          type: DisplayString
        - labels: []
          labelname: ifIndex
      - name: ifHCOutOctets
        oid: *******.********.1.1.10
        type: counter
        help: The total number of octets transmitted out of the interface, including framing
          characters - *******.********.1.1.10
        indexes:
        - labelname: ifIndex
          type: gauge
        lookups:
        - labels:
          - ifIndex
          labelname: ifName
          oid: *******.********.1.1.1
          type: DisplayString
        - labels: []
          labelname: ifIndex
      - name: ifHCOutUcastPkts
        oid: *******.********.1.1.11
        type: counter
        help: The total number of packets that higher-level protocols requested be transmitted,
          and which were not addressed to a multicast or broadcast address at this sub-layer,
          including those that were discarded or not sent - *******.********.1.1.11
        indexes:
        - labelname: ifIndex
          type: gauge
        lookups:
        - labels:
          - ifIndex
          labelname: ifName
          oid: *******.********.1.1.1
          type: DisplayString
        - labels: []
          labelname: ifIndex
      - name: ifHCOutMulticastPkts
        oid: *******.********.1.1.12
        type: counter
        help: The total number of packets that higher-level protocols requested be transmitted,
          and which were addressed to a multicast address at this sub-layer, including
          those that were discarded or not sent - *******.********.1.1.12
        indexes:
        - labelname: ifIndex
          type: gauge
        lookups:
        - labels:
          - ifIndex
          labelname: ifName
          oid: *******.********.1.1.1
          type: DisplayString
        - labels: []
          labelname: ifIndex
      - name: ifHCOutBroadcastPkts
        oid: *******.********.1.1.13
        type: counter
        help: The total number of packets that higher-level protocols requested be transmitted,
          and which were addressed to a broadcast address at this sub-layer, including
          those that were discarded or not sent - *******.********.1.1.13
        indexes:
        - labelname: ifIndex
          type: gauge
        lookups:
        - labels:
          - ifIndex
          labelname: ifName
          oid: *******.********.1.1.1
          type: DisplayString
        - labels: []
          labelname: ifIndex
      - name: ifLinkUpDownTrapEnable
        oid: *******.********.1.1.14
        type: gauge
        help: Indicates whether linkUp/linkDown traps should be generated for this interface
          - *******.********.1.1.14
        indexes:
        - labelname: ifIndex
          type: gauge
        lookups:
        - labels:
          - ifIndex
          labelname: ifName
          oid: *******.********.1.1.1
          type: DisplayString
        - labels: []
          labelname: ifIndex
        enum_values:
          1: enabled
          2: disabled
      - name: ifHighSpeed
        oid: *******.********.1.1.15
        type: gauge
        help: An estimate of the interface's current bandwidth in units of 1,000,000 bits
          per second - *******.********.1.1.15
        indexes:
        - labelname: ifIndex
          type: gauge
        lookups:
        - labels:
          - ifIndex
          labelname: ifName
          oid: *******.********.1.1.1
          type: DisplayString
        - labels: []
          labelname: ifIndex
      - name: ifPromiscuousMode
        oid: *******.********.1.1.16
        type: gauge
        help: This object has a value of false(2) if this interface only accepts packets/frames
          that are addressed to this station - *******.********.1.1.16
        indexes:
        - labelname: ifIndex
          type: gauge
        lookups:
        - labels:
          - ifIndex
          labelname: ifName
          oid: *******.********.1.1.1
          type: DisplayString
        - labels: []
          labelname: ifIndex
        enum_values:
          1: "true"
          2: "false"
      - name: ifConnectorPresent
        oid: *******.********.1.1.17
        type: gauge
        help: This object has the value 'true(1)' if the interface sublayer has a physical
          connector and the value 'false(2)' otherwise. - *******.********.1.1.17
        indexes:
        - labelname: ifIndex
          type: gauge
        lookups:
        - labels:
          - ifIndex
          labelname: ifName
          oid: *******.********.1.1.1
          type: DisplayString
        - labels: []
          labelname: ifIndex
        enum_values:
          1: "true"
          2: "false"
      - name: ifAlias
        oid: *******.********.1.1.18
        type: DisplayString
        help: This object is an 'alias' name for the interface as specified by a network
          manager, and provides a non-volatile 'handle' for the interface - *******.********.1.1.18
        indexes:
        - labelname: ifIndex
          type: gauge
        lookups:
        - labels:
          - ifIndex
          labelname: ifName
          oid: *******.********.1.1.1
          type: DisplayString
        - labels: []
          labelname: ifIndex
      - name: ifCounterDiscontinuityTime
        oid: *******.********.1.1.19
        type: gauge
        help: The value of sysUpTime on the most recent occasion at which any one or more
          of this interface's counters suffered a discontinuity - *******.********.1.1.19
        indexes:
        - labelname: ifIndex
          type: gauge
        lookups:
        - labels:
          - ifIndex
          labelname: ifName
          oid: *******.********.1.1.1
          type: DisplayString
        - labels: []
          labelname: ifIndex
      - name: laNames
        oid: *******.4.1.2021.10.1.2
        type: DisplayString
        help: The list of loadave names we're watching. - *******.4.1.2021.10.1.2
        indexes:
        - labelname: laIndex
          type: gauge
        lookups:
        - labels:
          - laIndex
          labelname: laNames
          oid: *******.4.1.2021.10.1.2
          type: DisplayString
        - labels: []
          labelname: laIndex
      - name: laLoadInt
        oid: *******.4.1.2021.10.1.5
        type: gauge
        help: The 1,5 and 15 minute load averages as an integer - *******.4.1.2021.10.1.5
        indexes:
        - labelname: laIndex
          type: gauge
        lookups:
        - labels:
          - laIndex
          labelname: laNames
          oid: *******.4.1.2021.10.1.2
          type: DisplayString
        - labels: []
          labelname: laIndex
      - name: ssCpuSystem
        oid: *******.4.1.2021.11.10
        type: gauge
        help: The percentage of CPU time spent processing system-level code, calculated
          over the last minute - *******.4.1.2021.11.10
      - name: ssCpuIdle
        oid: *******.4.1.2021.11.11
        type: gauge
        help: The percentage of processor time spent idle, calculated over the last minute
          - *******.4.1.2021.11.11
      - name: ssCpuUser
        oid: *******.4.1.2021.11.9
        type: gauge
        help: The percentage of CPU time spent processing user-level code, calculated
          over the last minute - *******.4.1.2021.11.9
      - name: memIndex
        oid: *******.4.1.2021.4.1
        type: gauge
        help: Bogus Index - *******.4.1.2021.4.1
      - name: memErrorName
        oid: *******.4.1.2021.4.2
        type: DisplayString
        help: Bogus Name - *******.4.1.2021.4.2
      - name: memTotalSwap
        oid: *******.4.1.2021.4.3
        type: gauge
        help: The total amount of swap space configured for this host. - *******.4.1.2021.4.3
      - name: memAvailSwap
        oid: *******.4.1.2021.4.4
        type: gauge
        help: The amount of swap space currently unused or available. - *******.4.1.2021.4.4
      - name: memTotalReal
        oid: *******.4.1.2021.4.5
        type: gauge
        help: The total amount of real/physical memory installed on this host. - *******.4.1.2021.4.5
      - name: memAvailReal
        oid: *******.4.1.2021.4.6
        type: gauge
        help: The amount of real/physical memory currently unused or available. - *******.4.1.2021.4.6
      - name: memTotalSwapTXT
        oid: *******.4.1.2021.4.7
        type: gauge
        help: The total amount of swap space or virtual memory allocated for text pages
          on this host - *******.4.1.2021.4.7
      - name: memAvailSwapTXT
        oid: *******.4.1.2021.4.8
        type: gauge
        help: The amount of swap space or virtual memory currently being used by text
          pages on this host - *******.4.1.2021.4.8
      - name: memTotalRealTXT
        oid: *******.4.1.2021.4.9
        type: gauge
        help: The total amount of real/physical memory allocated for text pages on this
          host - *******.4.1.2021.4.9
      - name: memAvailRealTXT
        oid: *******.4.1.2021.4.10
        type: gauge
        help: The amount of real/physical memory currently being used by text pages on
          this host - *******.4.1.2021.4.10
      - name: memTotalFree
        oid: *******.4.1.2021.4.11
        type: gauge
        help: The total amount of memory free or available for use on this host - *******.4.1.2021.4.11
      - name: memMinimumSwap
        oid: *******.4.1.2021.4.12
        type: gauge
        help: The minimum amount of swap space expected to be kept free or available during
          normal operation of this host - *******.4.1.2021.4.12
      - name: memShared
        oid: *******.4.1.2021.4.13
        type: gauge
        help: The total amount of real or virtual memory currently allocated for use as
          shared memory - *******.4.1.2021.4.13
      - name: memBuffer
        oid: *******.4.1.2021.4.14
        type: gauge
        help: The total amount of real or virtual memory currently allocated for use as
          memory buffers - *******.4.1.2021.4.14
      - name: memCached
        oid: *******.4.1.2021.4.15
        type: gauge
        help: The total amount of real or virtual memory currently allocated for use as
          cached memory - *******.4.1.2021.4.15
      - name: memUsedSwapTXT
        oid: *******.4.1.2021.4.16
        type: gauge
        help: The amount of swap space or virtual memory currently being used by text
          pages on this host - *******.4.1.2021.4.16
      - name: memUsedRealTXT
        oid: *******.4.1.2021.4.17
        type: gauge
        help: The amount of real/physical memory currently being used by text pages on
          this host - *******.4.1.2021.4.17
      - name: memTotalSwapX
        oid: *******.4.1.2021.4.18
        type: counter
        help: The total amount of swap space configured for this host. - *******.4.1.2021.4.18
      - name: memAvailSwapX
        oid: *******.4.1.2021.4.19
        type: counter
        help: The amount of swap space currently unused or available. - *******.4.1.2021.4.19
      - name: memTotalRealX
        oid: *******.4.1.2021.4.20
        type: counter
        help: The total amount of real/physical memory installed on this host. - *******.4.1.2021.4.20
      - name: memAvailRealX
        oid: *******.4.1.2021.4.21
        type: counter
        help: The amount of real/physical memory currently unused or available. - *******.4.1.2021.4.21
      - name: memTotalFreeX
        oid: *******.4.1.2021.4.22
        type: counter
        help: The total amount of memory free or available for use on this host - *******.4.1.2021.4.22
      - name: memMinimumSwapX
        oid: *******.4.1.2021.4.23
        type: counter
        help: The minimum amount of swap space expected to be kept free or available during
          normal operation of this host - *******.4.1.2021.4.23
      - name: memSharedX
        oid: *******.4.1.2021.4.24
        type: counter
        help: The total amount of real or virtual memory currently allocated for use as
          shared memory - *******.4.1.2021.4.24
      - name: memBufferX
        oid: *******.4.1.2021.4.25
        type: counter
        help: The total amount of real or virtual memory currently allocated for use as
          memory buffers - *******.4.1.2021.4.25
      - name: memCachedX
        oid: *******.4.1.2021.4.26
        type: counter
        help: The total amount of real or virtual memory currently allocated for use as
          cached memory - *******.4.1.2021.4.26
      - name: memSwapError
        oid: *******.4.1.2021.4.100
        type: gauge
        help: Indicates whether the amount of available swap space (as reported by 'memAvailSwap(4)'),
          is less than the desired minimum (specified by 'memMinimumSwap(12)'). - *******.4.1.2021.4.100
        enum_values:
          0: noError
          1: error
      - name: memSwapErrorMsg
        oid: *******.4.1.2021.4.101
        type: DisplayString
        help: Describes whether the amount of available swap space (as reported by 'memAvailSwap(4)'),
          is less than the desired minimum (specified by 'memMinimumSwap(12)'). - *******.4.1.2021.4.101
      - name: systemStatus
        oid: *******.4.1.6574.1.1
        type: gauge
        help: Synology system status Each meanings of status represented describe below
          - *******.4.1.6574.1.1
      - name: temperature
        oid: *******.4.1.6574.1.2
        type: gauge
        help: Synology system temperature The temperature of Disk Station uses Celsius
          degree. - *******.4.1.6574.1.2
      - name: powerStatus
        oid: *******.4.1.6574.1.3
        type: gauge
        help: Synology power status Each meanings of status represented describe below
          - *******.4.1.6574.1.3
      - name: systemFanStatus
        oid: *******.4.1.6574.1.4.1
        type: gauge
        help: Synology system fan status Each meanings of status represented describe
          below - *******.4.1.6574.1.4.1
      - name: cpuFanStatus
        oid: *******.4.1.6574.1.4.2
        type: gauge
        help: Synology cpu fan status Each meanings of status represented describe below
          - *******.4.1.6574.1.4.2
      - name: modelName
        oid: *******.4.1.6574.1.5.1
        type: DisplayString
        help: The Model name of this NAS - *******.4.1.6574.1.5.1
      - name: serialNumber
        oid: *******.4.1.6574.1.5.2
        type: OctetString
        help: The serial number of this NAS - *******.4.1.6574.1.5.2
      - name: version
        oid: *******.4.1.6574.1.5.3
        type: OctetString
        help: The version of this DSM - *******.4.1.6574.1.5.3
      - name: upgradeAvailable
        oid: *******.4.1.6574.1.5.4
        type: gauge
        help: This oid is for checking whether there is a latest DSM can be upgraded -
          *******.4.1.6574.1.5.4
      - name: controllerNumber
        oid: *******.4.1.6574.1.6
        type: gauge
        help: Synology system controller number Controller A(0) Controller B(1) - *******.4.1.6574.1.6
      - name: storageIOIndex
        oid: *******.4.1.6574.10*******
        type: gauge
        help: Reference index for each observed device. - *******.4.1.6574.10*******
        indexes:
        - labelname: storageIOIndex
          type: gauge
        lookups:
        - labels:
          - storageIOIndex
          labelname: storageIODevice
          oid: *******.4.1.6574.10*******
          type: DisplayString
        - labels: []
          labelname: storageIOIndex
      - name: storageIODevice
        oid: *******.4.1.6574.10*******
        type: DisplayString
        help: The name of the device we are counting/checking. - *******.4.1.6574.10*******
        indexes:
        - labelname: storageIOIndex
          type: gauge
        lookups:
        - labels:
          - storageIOIndex
          labelname: storageIODevice
          oid: *******.4.1.6574.10*******
          type: DisplayString
        - labels: []
          labelname: storageIOIndex
      - name: storageIONRead
        oid: *******.4.1.6574.10*******
        type: counter
        help: The number of bytes read from this device since boot. - *******.4.1.6574.10*******
        indexes:
        - labelname: storageIOIndex
          type: gauge
        lookups:
        - labels:
          - storageIOIndex
          labelname: storageIODevice
          oid: *******.4.1.6574.10*******
          type: DisplayString
        - labels: []
          labelname: storageIOIndex
      - name: storageIONWritten
        oid: *******.4.1.6574.10*******
        type: counter
        help: The number of bytes written to this device since boot. - *******.4.1.6574.10*******
        indexes:
        - labelname: storageIOIndex
          type: gauge
        lookups:
        - labels:
          - storageIOIndex
          labelname: storageIODevice
          oid: *******.4.1.6574.10*******
          type: DisplayString
        - labels: []
          labelname: storageIOIndex
      - name: storageIOReads
        oid: *******.4.1.6574.10*******
        type: counter
        help: The number of read accesses from this device since boot. - *******.4.1.6574.10*******
        indexes:
        - labelname: storageIOIndex
          type: gauge
        lookups:
        - labels:
          - storageIOIndex
          labelname: storageIODevice
          oid: *******.4.1.6574.10*******
          type: DisplayString
        - labels: []
          labelname: storageIOIndex
      - name: storageIOWrites
        oid: *******.4.1.6574.10*******
        type: counter
        help: The number of write accesses to this device since boot. - *******.4.1.6574.10*******
        indexes:
        - labelname: storageIOIndex
          type: gauge
        lookups:
        - labels:
          - storageIOIndex
          labelname: storageIODevice
          oid: *******.4.1.6574.10*******
          type: DisplayString
        - labels: []
          labelname: storageIOIndex
      - name: storageIOLA
        oid: *******.4.1.6574.10*******
        type: gauge
        help: The load of disk (%) - *******.4.1.6574.10*******
        indexes:
        - labelname: storageIOIndex
          type: gauge
        lookups:
        - labels:
          - storageIOIndex
          labelname: storageIODevice
          oid: *******.4.1.6574.10*******
          type: DisplayString
        - labels: []
          labelname: storageIOIndex
      - name: storageIOLA1
        oid: *******.4.1.6574.10*******
        type: gauge
        help: The 1 minute average load of disk (%) - *******.4.1.6574.10*******
        indexes:
        - labelname: storageIOIndex
          type: gauge
        lookups:
        - labels:
          - storageIOIndex
          labelname: storageIODevice
          oid: *******.4.1.6574.10*******
          type: DisplayString
        - labels: []
          labelname: storageIOIndex
      - name: storageIOLA5
        oid: *******.4.1.6574.10*******0
        type: gauge
        help: The 5 minute average load of disk (%) - *******.4.1.6574.10*******0
        indexes:
        - labelname: storageIOIndex
          type: gauge
        lookups:
        - labels:
          - storageIOIndex
          labelname: storageIODevice
          oid: *******.4.1.6574.10*******
          type: DisplayString
        - labels: []
          labelname: storageIOIndex
      - name: storageIOLA15
        oid: *******.4.1.6574.10********
        type: gauge
        help: The 15 minute average load of disk (%) - *******.4.1.6574.10********
        indexes:
        - labelname: storageIOIndex
          type: gauge
        lookups:
        - labels:
          - storageIOIndex
          labelname: storageIODevice
          oid: *******.4.1.6574.10*******
          type: DisplayString
        - labels: []
          labelname: storageIOIndex
      - name: storageIONReadX
        oid: *******.4.1.6574.10*******2
        type: counter
        help: The number of bytes read from this device since boot. - *******.4.1.6574.10*******2
        indexes:
        - labelname: storageIOIndex
          type: gauge
        lookups:
        - labels:
          - storageIOIndex
          labelname: storageIODevice
          oid: *******.4.1.6574.10*******
          type: DisplayString
        - labels: []
          labelname: storageIOIndex
      - name: storageIONWrittenX
        oid: *******.4.1.6574.10*******3
        type: counter
        help: The number of bytes written to this device since boot. - *******.4.1.6574.10*******3
        indexes:
        - labelname: storageIOIndex
          type: gauge
        lookups:
        - labels:
          - storageIOIndex
          labelname: storageIODevice
          oid: *******.4.1.6574.10*******
          type: DisplayString
        - labels: []
          labelname: storageIOIndex
      - name: storageIODeviceSerial
        oid: *******.4.1.6574.10*******4
        type: DisplayString
        help: The name of the device we are counting/checking. - *******.4.1.6574.10*******4
        indexes:
        - labelname: storageIOIndex
          type: gauge
        lookups:
        - labels:
          - storageIOIndex
          labelname: storageIODevice
          oid: *******.4.1.6574.10*******
          type: DisplayString
        - labels: []
          labelname: storageIOIndex
      - name: spaceIOIndex
        oid: *******.4.1.6574.10*******
        type: gauge
        help: Reference index for each observed device. - *******.4.1.6574.10*******
        indexes:
        - labelname: spaceIOIndex
          type: gauge
        lookups:
        - labels:
          - spaceIOIndex
          labelname: spaceIODevice
          oid: *******.4.1.6574.102.1.1.2
          type: DisplayString
        - labels: []
          labelname: spaceIOIndex
      - name: spaceIODevice
        oid: *******.4.1.6574.102.1.1.2
        type: DisplayString
        help: The name of the device we are counting/checking. - *******.4.1.6574.102.1.1.2
        indexes:
        - labelname: spaceIOIndex
          type: gauge
        lookups:
        - labels:
          - spaceIOIndex
          labelname: spaceIODevice
          oid: *******.4.1.6574.102.1.1.2
          type: DisplayString
        - labels: []
          labelname: spaceIOIndex
      - name: spaceIONRead
        oid: *******.4.1.6574.10*******
        type: counter
        help: The number of bytes read from this device since boot. - *******.4.1.6574.10*******
        indexes:
        - labelname: spaceIOIndex
          type: gauge
        lookups:
        - labels:
          - spaceIOIndex
          labelname: spaceIODevice
          oid: *******.4.1.6574.102.1.1.2
          type: DisplayString
        - labels: []
          labelname: spaceIOIndex
      - name: spaceIONWritten
        oid: *******.4.1.6574.102.1.1.4
        type: counter
        help: The number of bytes written to this device since boot. - *******.4.1.6574.102.1.1.4
        indexes:
        - labelname: spaceIOIndex
          type: gauge
        lookups:
        - labels:
          - spaceIOIndex
          labelname: spaceIODevice
          oid: *******.4.1.6574.102.1.1.2
          type: DisplayString
        - labels: []
          labelname: spaceIOIndex
      - name: spaceIOReads
        oid: *******.4.1.6574.10*******
        type: counter
        help: The number of read accesses from this device since boot. - *******.4.1.6574.10*******
        indexes:
        - labelname: spaceIOIndex
          type: gauge
        lookups:
        - labels:
          - spaceIOIndex
          labelname: spaceIODevice
          oid: *******.4.1.6574.102.1.1.2
          type: DisplayString
        - labels: []
          labelname: spaceIOIndex
      - name: spaceIOWrites
        oid: *******.4.1.6574.102.1.1.6
        type: counter
        help: The number of write accesses to this device since boot. - *******.4.1.6574.102.1.1.6
        indexes:
        - labelname: spaceIOIndex
          type: gauge
        lookups:
        - labels:
          - spaceIOIndex
          labelname: spaceIODevice
          oid: *******.4.1.6574.102.1.1.2
          type: DisplayString
        - labels: []
          labelname: spaceIOIndex
      - name: spaceIOLA
        oid: *******.4.1.6574.102.1.1.8
        type: gauge
        help: The load of disk (%) - *******.4.1.6574.102.1.1.8
        indexes:
        - labelname: spaceIOIndex
          type: gauge
        lookups:
        - labels:
          - spaceIOIndex
          labelname: spaceIODevice
          oid: *******.4.1.6574.102.1.1.2
          type: DisplayString
        - labels: []
          labelname: spaceIOIndex
      - name: spaceIOLA1
        oid: *******.4.1.6574.102.1.1.9
        type: gauge
        help: The 1 minute average load of disk (%) - *******.4.1.6574.102.1.1.9
        indexes:
        - labelname: spaceIOIndex
          type: gauge
        lookups:
        - labels:
          - spaceIOIndex
          labelname: spaceIODevice
          oid: *******.4.1.6574.102.1.1.2
          type: DisplayString
        - labels: []
          labelname: spaceIOIndex
      - name: spaceIOLA5
        oid: *******.4.1.6574.10*******0
        type: gauge
        help: The 5 minute average load of disk (%) - *******.4.1.6574.10*******0
        indexes:
        - labelname: spaceIOIndex
          type: gauge
        lookups:
        - labels:
          - spaceIOIndex
          labelname: spaceIODevice
          oid: *******.4.1.6574.102.1.1.2
          type: DisplayString
        - labels: []
          labelname: spaceIOIndex
      - name: spaceIOLA15
        oid: *******.4.1.6574.10*******1
        type: gauge
        help: The 15 minute average load of disk (%) - *******.4.1.6574.10*******1
        indexes:
        - labelname: spaceIOIndex
          type: gauge
        lookups:
        - labels:
          - spaceIOIndex
          labelname: spaceIODevice
          oid: *******.4.1.6574.102.1.1.2
          type: DisplayString
        - labels: []
          labelname: spaceIOIndex
      - name: spaceIONReadX
        oid: *******.4.1.6574.10*******2
        type: counter
        help: The number of bytes read from this device since boot. - *******.4.1.6574.10*******2
        indexes:
        - labelname: spaceIOIndex
          type: gauge
        lookups:
        - labels:
          - spaceIOIndex
          labelname: spaceIODevice
          oid: *******.4.1.6574.102.1.1.2
          type: DisplayString
        - labels: []
          labelname: spaceIOIndex
      - name: spaceIONWrittenX
        oid: *******.4.1.6574.10*******3
        type: counter
        help: The number of bytes written to this device since boot. - *******.4.1.6574.10*******3
        indexes:
        - labelname: spaceIOIndex
          type: gauge
        lookups:
        - labels:
          - spaceIOIndex
          labelname: spaceIODevice
          oid: *******.4.1.6574.102.1.1.2
          type: DisplayString
        - labels: []
          labelname: spaceIOIndex
      - name: spaceUUID
        oid: *******.4.1.6574.10*******4
        type: DisplayString
        help: The uuid of space. - *******.4.1.6574.10*******4
        indexes:
        - labelname: spaceIOIndex
          type: gauge
        lookups:
        - labels:
          - spaceIOIndex
          labelname: spaceIODevice
          oid: *******.4.1.6574.102.1.1.2
          type: DisplayString
        - labels: []
          labelname: spaceIOIndex
      - name: iSCSILUNInfoIndex
        oid: *******.4.1.6574.104.1.1.1
        type: gauge
        help: LUN info index - *******.4.1.6574.104.1.1.1
        indexes:
        - labelname: iSCSILUNInfoIndex
          type: gauge
      - name: iSCSILUNUUID
        oid: *******.4.1.6574.104.1.1.2
        type: OctetString
        help: LUN uuid - *******.4.1.6574.104.1.1.2
        indexes:
        - labelname: iSCSILUNInfoIndex
          type: gauge
      - name: iSCSILUNName
        oid: *******.4.1.6574.104.1.1.3
        type: OctetString
        help: LUN name - *******.4.1.6574.104.1.1.3
        indexes:
        - labelname: iSCSILUNInfoIndex
          type: gauge
      - name: iSCSILUNThroughputReadHigh
        oid: *******.4.1.6574.104.1.1.4
        type: gauge
        help: LUN read throughput over 32 bits part - *******.4.1.6574.104.1.1.4
        indexes:
        - labelname: iSCSILUNInfoIndex
          type: gauge
      - name: iSCSILUNThroughputReadLow
        oid: *******.4.1.6574.104.1.1.5
        type: gauge
        help: LUN read throughput in unsigned 32 bit - *******.4.1.6574.104.1.1.5
        indexes:
        - labelname: iSCSILUNInfoIndex
          type: gauge
      - name: iSCSILUNThroughputWriteHigh
        oid: *******.4.1.6574.104.1.1.6
        type: gauge
        help: LUN write throughput over 32 bits part - *******.4.1.6574.104.1.1.6
        indexes:
        - labelname: iSCSILUNInfoIndex
          type: gauge
      - name: iSCSILUNThroughputWriteLow
        oid: *******.4.1.6574.104.1.1.7
        type: gauge
        help: LUN write throughput in unsigned 32 bit - *******.4.1.6574.104.1.1.7
        indexes:
        - labelname: iSCSILUNInfoIndex
          type: gauge
      - name: iSCSILUNIopsRead
        oid: *******.4.1.6574.104.1.1.8
        type: gauge
        help: LUN read iops - *******.4.1.6574.104.1.1.8
        indexes:
        - labelname: iSCSILUNInfoIndex
          type: gauge
      - name: iSCSILUNIopsWrite
        oid: *******.4.1.6574.104.1.1.9
        type: gauge
        help: LUN write iops - *******.4.1.6574.104.1.1.9
        indexes:
        - labelname: iSCSILUNInfoIndex
          type: gauge
      - name: iSCSILUNDiskLatencyRead
        oid: *******.4.1.6574.104.1.1.10
        type: gauge
        help: LUN disk latency when reading - *******.4.1.6574.104.1.1.10
        indexes:
        - labelname: iSCSILUNInfoIndex
          type: gauge
      - name: iSCSILUNDiskLatencyWrite
        oid: *******.4.1.6574.104.1.1.11
        type: gauge
        help: LUN disk latency when writing - *******.4.1.6574.104.1.1.11
        indexes:
        - labelname: iSCSILUNInfoIndex
          type: gauge
      - name: iSCSILUNNetworkLatencyTx
        oid: *******.4.1.6574.104.1.1.12
        type: gauge
        help: LUN transfer data network latency - *******.4.1.6574.104.1.1.12
        indexes:
        - labelname: iSCSILUNInfoIndex
          type: gauge
      - name: iSCSILUNNetworkLatencyRx
        oid: *******.4.1.6574.104.1.1.13
        type: gauge
        help: LUN receive data network latency - *******.4.1.6574.104.1.1.13
        indexes:
        - labelname: iSCSILUNInfoIndex
          type: gauge
      - name: iSCSILUNIoSizeRead
        oid: *******.4.1.6574.104.1.1.14
        type: gauge
        help: LUN average io size when reading - *******.4.1.6574.104.1.1.14
        indexes:
        - labelname: iSCSILUNInfoIndex
          type: gauge
      - name: iSCSILUNIoSizeWrite
        oid: *******.4.1.6574.104.1.1.15
        type: gauge
        help: LUN average io size when writing - *******.4.1.6574.104.1.1.15
        indexes:
        - labelname: iSCSILUNInfoIndex
          type: gauge
      - name: iSCSILUNQueueDepth
        oid: *******.4.1.6574.104.1.1.16
        type: gauge
        help: Num of iSCSI commands in LUN queue - *******.4.1.6574.104.1.1.16
        indexes:
        - labelname: iSCSILUNInfoIndex
          type: gauge
      - name: iSCSILUNType
        oid: *******.4.1.6574.104.1.1.17
        type: OctetString
        help: LUN type - *******.4.1.6574.104.1.1.17
        indexes:
        - labelname: iSCSILUNInfoIndex
          type: gauge
      - name: iSCSILUNDiskLatencyAvg
        oid: *******.4.1.6574.104.1.1.18
        type: gauge
        help: Average latency of LUN disk - *******.4.1.6574.104.1.1.18
        indexes:
        - labelname: iSCSILUNInfoIndex
          type: gauge
      - name: iSCSILUNThinProvisionVolFreeMBs
        oid: *******.4.1.6574.104.1.1.19
        type: gauge
        help: Free space(MB) of thin provisioning lun's volume - *******.4.1.6574.104.1.1.19
        indexes:
        - labelname: iSCSILUNInfoIndex
          type: gauge
      - name: diskIndex
        oid: *******.4.1.6574.*******
        type: gauge
        help: The index of disk table - *******.4.1.6574.*******
        indexes:
        - labelname: diskIndex
          type: gauge
        lookups:
        - labels:
          - diskIndex
          labelname: diskID
          oid: *******.4.1.6574.2.1.1.2
          type: OctetString
        - labels: []
          labelname: diskIndex
      - name: diskID
        oid: *******.4.1.6574.2.1.1.2
        type: OctetString
        help: Synology disk ID The ID of disk is assigned by disk Station. - *******.4.1.6574.2.1.1.2
        indexes:
        - labelname: diskIndex
          type: gauge
        lookups:
        - labels:
          - diskIndex
          labelname: diskID
          oid: *******.4.1.6574.2.1.1.2
          type: OctetString
        - labels: []
          labelname: diskIndex
      - name: diskModel
        oid: *******.4.1.6574.*******
        type: DisplayString
        help: Synology disk model name The disk model name will be showed here. - *******.4.1.6574.*******
        indexes:
        - labelname: diskIndex
          type: gauge
        lookups:
        - labels:
          - diskIndex
          labelname: diskID
          oid: *******.4.1.6574.2.1.1.2
          type: OctetString
        - labels: []
          labelname: diskIndex
      - name: diskType
        oid: *******.4.1.6574.2.1.1.4
        type: DisplayString
        help: Synology disk type The type of disk will be showed here, including SATA,
          SSD and so on. - *******.4.1.6574.2.1.1.4
        indexes:
        - labelname: diskIndex
          type: gauge
        lookups:
        - labels:
          - diskIndex
          labelname: diskID
          oid: *******.4.1.6574.2.1.1.2
          type: OctetString
        - labels: []
          labelname: diskIndex
      - name: diskStatus
        oid: *******.4.1.6574.*******
        type: gauge
        help: Synology disk status Each meanings of status represented describe below
          - *******.4.1.6574.*******
        indexes:
        - labelname: diskIndex
          type: gauge
        lookups:
        - labels:
          - diskIndex
          labelname: diskID
          oid: *******.4.1.6574.2.1.1.2
          type: OctetString
        - labels: []
          labelname: diskIndex
      - name: diskTemperature
        oid: *******.4.1.6574.2.1.1.6
        type: gauge
        help: Synology disk temperature The temperature of each disk uses Celsius degree.
          - *******.4.1.6574.2.1.1.6
        indexes:
        - labelname: diskIndex
          type: gauge
        lookups:
        - labels:
          - diskIndex
          labelname: diskID
          oid: *******.4.1.6574.2.1.1.2
          type: OctetString
        - labels: []
          labelname: diskIndex
      - name: diskRole
        oid: *******.4.1.6574.2.1.1.7
        type: OctetString
        help: Synology disk role The role of the disk in system - *******.4.1.6574.2.1.1.7
        indexes:
        - labelname: diskIndex
          type: gauge
        lookups:
        - labels:
          - diskIndex
          labelname: diskID
          oid: *******.4.1.6574.2.1.1.2
          type: OctetString
        - labels: []
          labelname: diskIndex
      - name: diskRetry
        oid: *******.4.1.6574.2.1.1.8
        type: gauge
        help: Synology disk retry count The count of each disk connection retries. - *******.4.1.6574.2.1.1.8
        indexes:
        - labelname: diskIndex
          type: gauge
        lookups:
        - labels:
          - diskIndex
          labelname: diskID
          oid: *******.4.1.6574.2.1.1.2
          type: OctetString
        - labels: []
          labelname: diskIndex
      - name: diskBadSector
        oid: *******.4.1.6574.2.1.1.9
        type: gauge
        help: Synology disk bad sector count The count of each disk I/O bad sector. -
          *******.4.1.6574.2.1.1.9
        indexes:
        - labelname: diskIndex
          type: gauge
        lookups:
        - labels:
          - diskIndex
          labelname: diskID
          oid: *******.4.1.6574.2.1.1.2
          type: OctetString
        - labels: []
          labelname: diskIndex
      - name: diskIdentifyFail
        oid: *******.4.1.6574.*******0
        type: gauge
        help: Synology disk identify fail count The count of each disk identify fails.
          - *******.4.1.6574.*******0
        indexes:
        - labelname: diskIndex
          type: gauge
        lookups:
        - labels:
          - diskIndex
          labelname: diskID
          oid: *******.4.1.6574.2.1.1.2
          type: OctetString
        - labels: []
          labelname: diskIndex
      - name: diskRemainLife
        oid: *******.4.1.6574.*******1
        type: gauge
        help: Synology disk remainLife The estimate remain life of each disk. - *******.4.1.6574.*******1
        indexes:
        - labelname: diskIndex
          type: gauge
        lookups:
        - labels:
          - diskIndex
          labelname: diskID
          oid: *******.4.1.6574.2.1.1.2
          type: OctetString
        - labels: []
          labelname: diskIndex
      - name: diskName
        oid: *******.4.1.6574.*******2
        type: OctetString
        help: Synology disk name The name of disk which align to storage manager. - *******.4.1.6574.*******2
        indexes:
        - labelname: diskIndex
          type: gauge
        lookups:
        - labels:
          - diskIndex
          labelname: diskID
          oid: *******.4.1.6574.2.1.1.2
          type: OctetString
        - labels: []
          labelname: diskIndex
      - name: diskHealthStatus
        oid: *******.4.1.6574.*******3
        type: gauge
        help: Synology disk health status Each meanings of status represented describe
          below - *******.4.1.6574.*******3
        indexes:
        - labelname: diskIndex
          type: gauge
        lookups:
        - labels:
          - diskIndex
          labelname: diskID
          oid: *******.4.1.6574.2.1.1.2
          type: OctetString
        - labels: []
          labelname: diskIndex
      - name: raidIndex
        oid: *******.4.1.6574.3.1.1.1
        type: gauge
        help: The index of raid table - *******.4.1.6574.3.1.1.1
        indexes:
        - labelname: raidIndex
          type: gauge
        lookups:
        - labels:
          - raidIndex
          labelname: raidName
          oid: *******.4.1.6574.3.1.1.2
          type: DisplayString
        - labels: []
          labelname: raidIndex
      - name: raidName
        oid: *******.4.1.6574.3.1.1.2
        type: DisplayString
        help: Synology raid name The name of each raid will be showed here. - *******.4.1.6574.3.1.1.2
        indexes:
        - labelname: raidIndex
          type: gauge
        lookups:
        - labels:
          - raidIndex
          labelname: raidName
          oid: *******.4.1.6574.3.1.1.2
          type: DisplayString
        - labels: []
          labelname: raidIndex
      - name: raidStatus
        oid: *******.4.1.6574.3.1.1.3
        type: gauge
        help: Synology Raid status Each meanings of status represented describe below
          - *******.4.1.6574.3.1.1.3
        indexes:
        - labelname: raidIndex
          type: gauge
        lookups:
        - labels:
          - raidIndex
          labelname: raidName
          oid: *******.4.1.6574.3.1.1.2
          type: DisplayString
        - labels: []
          labelname: raidIndex
      - name: raidFreeSize
        oid: *******.4.1.6574.3.1.1.4
        type: gauge
        help: Synology raid freesize Free space in bytes. - *******.4.1.6574.3.1.1.4
        indexes:
        - labelname: raidIndex
          type: gauge
        lookups:
        - labels:
          - raidIndex
          labelname: raidName
          oid: *******.4.1.6574.3.1.1.2
          type: DisplayString
        - labels: []
          labelname: raidIndex
      - name: raidTotalSize
        oid: *******.4.1.6574.3.1.1.5
        type: gauge
        help: Synology raid totalsize Total space in bytes. - *******.4.1.6574.3.1.1.5
        indexes:
        - labelname: raidIndex
          type: gauge
        lookups:
        - labels:
          - raidIndex
          labelname: raidName
          oid: *******.4.1.6574.3.1.1.2
          type: DisplayString
        - labels: []
          labelname: raidIndex
      - name: raidHotspareCnt
        oid: *******.4.1.6574.3.1.1.6
        type: gauge
        help: Synology raid hotspare Total hotspare disks count - *******.4.1.6574.3.1.1.6
        indexes:
        - labelname: raidIndex
          type: gauge
        lookups:
        - labels:
          - raidIndex
          labelname: raidName
          oid: *******.4.1.6574.3.1.1.2
          type: DisplayString
        - labels: []
          labelname: raidIndex
      - name: upsDeviceModel
        oid: *******.4.1.6574.4.1.1
        type: DisplayString
        help: Device model - *******.4.1.6574.4.1.1
      - name: upsDeviceManufacturer
        oid: *******.4.1.6574.4.1.2
        type: DisplayString
        help: Device manufacturer - *******.4.1.6574.4.1.2
      - name: upsDeviceSerial
        oid: *******.4.1.6574.4.1.3
        type: DisplayString
        help: Device serial number. - *******.4.1.6574.4.1.3
      - name: upsDeviceType
        oid: *******.4.1.6574.4.1.4
        type: DisplayString
        help: Device type (ups, pdu, scd, psu) - *******.4.1.6574.4.1.4
      - name: upsDeviceDescription
        oid: *******.4.1.6574.4.1.5
        type: DisplayString
        help: Device description. - *******.4.1.6574.4.1.5
      - name: upsDeviceContact
        oid: *******.4.1.6574.4.1.6
        type: DisplayString
        help: Device administrator name. - *******.4.1.6574.4.1.6
      - name: upsDeviceLocation
        oid: *******.4.1.6574.4.1.7
        type: DisplayString
        help: Device physical location. - *******.4.1.6574.4.1.7
      - name: upsDevicePart
        oid: *******.4.1.6574.4.1.8
        type: DisplayString
        help: Device part number. - *******.4.1.6574.4.1.8
      - name: upsDeviceMACAddr
        oid: *******.4.1.6574.4.1.9
        type: DisplayString
        help: Physical network address of the device. - *******.4.1.6574.4.1.9
      - name: upsInfoStatus
        oid: *******.4.1.6574.4.2.1
        type: DisplayString
        help: UPS status. - *******.4.1.6574.4.2.1
      - name: upsInfoAlarm
        oid: *******.4.1.6574.4.2.2
        type: DisplayString
        help: UPS alarms - *******.4.1.6574.4.2.2
      - name: upsInfoTime
        oid: *******.4.1.6574.4.2.3
        type: DisplayString
        help: Internal UPS clock time - *******.4.1.6574.4.2.3
      - name: upsInfoDate
        oid: *******.4.1.6574.4.2.4
        type: DisplayString
        help: Internal UPS clock date - *******.4.1.6574.4.2.4
      - name: upsInfoModel
        oid: *******.4.1.6574.4.2.5
        type: DisplayString
        help: UPS model - *******.4.1.6574.4.2.5
      - name: upsInfoMfrName
        oid: *******.4.1.6574.*******
        type: DisplayString
        help: UPS manufacturer - *******.4.1.6574.*******
      - name: upsInfoMfrDate
        oid: *******.4.1.6574.*******
        type: DisplayString
        help: UPS manufacturing date - *******.4.1.6574.*******
      - name: upsInfoSerial
        oid: *******.4.1.6574.4.2.7
        type: DisplayString
        help: UPS serial number - *******.4.1.6574.4.2.7
      - name: upsInfoVendorID
        oid: *******.4.1.6574.4.2.8
        type: DisplayString
        help: Vendor ID for USB devices - *******.4.1.6574.4.2.8
      - name: upsInfoProductID
        oid: *******.4.1.6574.4.2.9
        type: DisplayString
        help: Product ID for USB devices - *******.4.1.6574.4.2.9
      - name: upsInfoFirmwareName
        oid: *******.4.1.6574.4.2.10.1
        type: DisplayString
        help: UPS firmware - *******.4.1.6574.4.2.10.1
      - name: upsInfoFirmwareAux
        oid: *******.4.1.6574.4.2.10.2
        type: DisplayString
        help: Auxiliary device firmware - *******.4.1.6574.4.2.10.2
      - name: upsInfoTemperature
        oid: *******.4.1.6574.4.2.11
        type: Float
        help: UPS temperature (degrees C) - *******.4.1.6574.4.2.11
      - name: upsInfoLoadValue
        oid: *******.4.1.6574.4.2.12.1
        type: Float
        help: Load on UPS (percent) - *******.4.1.6574.4.2.12.1
      - name: upsInfoLoadHigh
        oid: *******.4.1.6574.4.2.12.2
        type: Float
        help: Load when UPS switches to overload condition (OVER) (percent) - *******.4.1.6574.4.2.12.2
      - name: upsInfoID
        oid: *******.4.1.6574.4.2.13
        type: DisplayString
        help: UPS system identifier - *******.4.1.6574.4.2.13
      - name: upsInfoDelayStart
        oid: *******.4.1.6574.4.2.14.1
        type: gauge
        help: Interval to wait before restarting the load (seconds) - *******.4.1.6574.4.2.14.1
      - name: upsInfoDelayReboot
        oid: *******.4.1.6574.4.2.14.2
        type: gauge
        help: Interval to wait before rebooting the UPS (seconds) - *******.4.1.6574.4.2.14.2
      - name: upsInfoDelayShutdown
        oid: *******.4.1.6574.4.2.14.3
        type: gauge
        help: Interval to wait after shutdown with delay command (seconds) - *******.4.1.6574.4.2.14.3
      - name: upsInfoTimerStart
        oid: *******.4.1.6574.4.2.15.1
        type: gauge
        help: Time before the load will be started (seconds) - *******.4.1.6574.4.2.15.1
      - name: upsInfoTimerReboot
        oid: *******.4.1.6574.4.2.15.2
        type: gauge
        help: Time before the load will be rebooted (seconds) - *******.4.1.6574.4.2.15.2
      - name: upsInfoTimerShutdown
        oid: *******.4.1.6574.4.2.15.3
        type: gauge
        help: Time before the load will be shutdown (seconds) - *******.4.1.6574.4.2.15.3
      - name: upsInfoTestInterval
        oid: *******.4.1.6574.4.2.16.1
        type: gauge
        help: Interval between self tests - *******.4.1.6574.4.2.16.1
      - name: upsInfoTestResult
        oid: *******.4.1.6574.4.2.16.2
        type: DisplayString
        help: Results of last self test - *******.4.1.6574.4.2.16.2
      - name: upsInfoDisplayLanguage
        oid: *******.4.1.6574.4.2.17
        type: DisplayString
        help: Language to use on front panel - *******.4.1.6574.4.2.17
      - name: upsInfoContacts
        oid: *******.4.1.6574.4.2.18
        type: DisplayString
        help: UPS external contact sensors - *******.4.1.6574.4.2.18
      - name: upsInfoEffciency
        oid: *******.4.1.6574.4.2.19
        type: gauge
        help: Efficiency of the UPS (ratio of the output current on the input current)
          (percent) - *******.4.1.6574.4.2.19
      - name: upsInfoPowerValue
        oid: *******.4.1.6574.4.2.20.1
        type: Float
        help: Current value of apparent power (Volt-Amps) - *******.4.1.6574.4.2.20.1
      - name: upsInfoPowerNominal
        oid: *******.4.1.6574.4.2.20.2
        type: Float
        help: Nominal value of apparent power (Volt-Amps) - *******.4.1.6574.4.2.20.2
      - name: upsInfoRealPowerValue
        oid: *******.4.1.6574.4.2.21.1
        type: Float
        help: Current value of real power (Watts) - *******.4.1.6574.4.2.21.1
      - name: upsInfoRealPowerNominal
        oid: *******.4.1.6574.4.2.21.2
        type: Float
        help: Nominal value of real power (Watts) - *******.4.1.6574.4.2.21.2
      - name: upsInfoBeeperStatus
        oid: *******.4.1.6574.4.2.22
        type: DisplayString
        help: UPS beeper status (enabled, disabled or muted) - *******.4.1.6574.4.2.22
      - name: upsInfoType
        oid: *******.4.1.6574.4.2.23
        type: DisplayString
        help: UPS type - *******.4.1.6574.4.2.23
      - name: upsInfoWatchdogStatus
        oid: *******.4.1.6574.4.2.24
        type: DisplayString
        help: UPS watchdog status (enabled or disabled) - *******.4.1.6574.4.2.24
      - name: upsInfoStartAuto
        oid: *******.4.1.6574.4.2.25.1
        type: DisplayString
        help: UPS starts when mains is (re)applied - *******.4.1.6574.4.2.25.1
      - name: upsInfoStartBattery
        oid: *******.4.1.6574.4.2.25.2
        type: DisplayString
        help: Allow to start UPS from battery - *******.4.1.6574.4.2.25.2
      - name: upsInfoStartReboot
        oid: *******.4.1.6574.4.2.25.3
        type: DisplayString
        help: UPS coldstarts from battery (enabled or disabled) - *******.4.1.6574.4.2.25.3
      - name: upsBatteryChargeValue
        oid: *******.4.1.6574.4.3.1.1
        type: Float
        help: Battery charge - *******.4.1.6574.4.3.1.1
      - name: upsBatteryChargeLow
        oid: *******.4.1.6574.4.3.1.2
        type: Float
        help: Remaining battery level when UPS switches to LB (percent) - *******.4.1.6574.4.3.1.2
      - name: upsBatteryChargeRestart
        oid: *******.4.1.6574.4.3.1.3
        type: Float
        help: Minimum battery level for UPS restart after power-off - *******.4.1.6574.4.3.1.3
      - name: upsBatteryChargeWarning
        oid: *******.4.1.6574.4.3.1.4
        type: Float
        help: Battery level when UPS switches to Warning state (percent) - *******.4.1.6574.4.3.1.4
      - name: upsBatteryVoltageValue
        oid: *******.4.1.6574.4.3.2.1
        type: Float
        help: The magnitude of the present battery voltage. - *******.4.1.6574.4.3.2.1
      - name: upsBatteryVoltageNominal
        oid: *******.4.1.6574.4.3.2.2
        type: Float
        help: Nominal battery voltage. - *******.4.1.6574.4.3.2.2
      - name: upsBatteryVoltageLow
        oid: *******.4.1.6574.4.3.2.3
        type: Float
        help: Minimum battery voltage, that triggers FSD status . - *******.4.1.6574.4.3.2.3
      - name: upsBatteryVoltageHigh
        oid: *******.4.1.6574.4.3.2.4
        type: Float
        help: Maximum battery voltage (Ie battery.charge = 100). - *******.4.1.6574.4.3.2.4
      - name: upsBatteryCapacity
        oid: *******.4.1.6574.4.3.3
        type: Float
        help: Battery capacity (Ah) - *******.4.1.6574.4.3.3
      - name: upsBatteryCurrent
        oid: *******.4.1.6574.4.3.4
        type: Float
        help: The present battery current. - *******.4.1.6574.4.3.4
      - name: upsBatteryTemperature
        oid: *******.4.1.6574.4.3.5
        type: Float
        help: The ambient temperature at or near the UPS Battery casing. - *******.4.1.6574.4.3.5
      - name: upsBatteryRuntimeValue
        oid: *******.4.1.6574.4.3.6.1
        type: gauge
        help: Battery runtime (seconds) - *******.4.1.6574.4.3.6.1
      - name: upsBatteryRuntimeLow
        oid: *******.4.1.6574.4.3.6.2
        type: gauge
        help: Remaining battery runtime when UPS switches to LB (seconds) - *******.4.1.6574.4.3.6.2
      - name: upsBatteryRuntimeRestart
        oid: *******.4.1.6574.4.3.6.3
        type: gauge
        help: Minimum battery runtime for UPS restart after power-off (seconds) - *******.4.1.6574.4.3.6.3
      - name: upsBatteryAlarmThreshold
        oid: *******.4.1.6574.4.3.7
        type: DisplayString
        help: Battery alarm threshold - *******.4.1.6574.4.3.7
      - name: upsBatteryDate
        oid: *******.4.1.6574.4.3.8
        type: DisplayString
        help: Battery change date - *******.4.1.6574.4.3.8
      - name: upsBatteryMfrDate
        oid: *******.4.1.6574.4.3.9
        type: DisplayString
        help: Battery manufacturing date - *******.4.1.6574.4.3.9
      - name: upsBatteryPacks
        oid: *******.4.1.6574.4.3.10
        type: gauge
        help: Number of battery packs - *******.4.1.6574.4.3.10
      - name: upsBatteryPacksBad
        oid: *******.4.1.6574.4.3.11
        type: gauge
        help: Number of bad battery packs - *******.4.1.6574.4.3.11
      - name: upsBatteryType
        oid: *******.4.1.6574.4.3.12
        type: DisplayString
        help: Battery chemistry - *******.4.1.6574.4.3.12
      - name: upsBatteryProtection
        oid: *******.4.1.6574.4.3.13
        type: DisplayString
        help: Prevent deep discharge of battery - *******.4.1.6574.4.3.13
      - name: upsBatteryEnergySave
        oid: *******.4.1.6574.4.3.14
        type: DisplayString
        help: Switch off when running on battery and no/low load - *******.4.1.6574.4.3.14
      - name: upsInputVoltageValue
        oid: *******.4.1.6574.4.4.1.1
        type: Float
        help: Input voltage - *******.4.1.6574.4.4.1.1
      - name: upsInputVoltageMax
        oid: *******.4.1.6574.4.4.1.2
        type: Float
        help: Maximum incoming voltage seen - *******.4.1.6574.4.4.1.2
      - name: upsInputVoltageMin
        oid: *******.4.1.6574.4.4.1.3
        type: Float
        help: Minimum incoming voltage seen - *******.4.1.6574.4.4.1.3
      - name: upsInputVoltageNominal
        oid: *******.4.1.6574.4.4.1.4
        type: Float
        help: Nominal input voltage - *******.4.1.6574.4.4.1.4
      - name: upsInputVoltageExtend
        oid: *******.4.1.6574.4.4.1.5
        type: DisplayString
        help: Extended input voltage range - *******.4.1.6574.4.4.1.5
      - name: upsInputVoltageFault
        oid: *******.4.1.6574.4.4.1.6
        type: Float
        help: Input voltage Fault - *******.4.1.6574.4.4.1.6
      - name: upsInputTransferReason
        oid: *******.4.1.6574.4.4.2.1
        type: DisplayString
        help: Reason for last transfer to battery - *******.4.1.6574.4.4.2.1
      - name: upsInputTransferLow
        oid: *******.4.1.6574.4.4.2.2
        type: Float
        help: Low voltage transfer point - *******.4.1.6574.4.4.2.2
      - name: upsInputTransferHigh
        oid: *******.4.1.6574.4.4.2.3
        type: Float
        help: High voltage transfer point - *******.4.1.6574.4.4.2.3
      - name: upsInputTransferLowMin
        oid: *******.4.1.6574.4.4.2.4
        type: Float
        help: smallest settable low voltage transfer point - *******.4.1.6574.4.4.2.4
      - name: upsInputTransferLowMax
        oid: *******.4.1.6574.4.4.2.5
        type: Float
        help: greatest settable low voltage transfer point - *******.4.1.6574.4.4.2.5
      - name: upsInputTransferHighMin
        oid: *******.4.1.6574.4.4.2.6
        type: Float
        help: smallest settable high voltage transfer point - *******.4.1.6574.4.4.2.6
      - name: upsInputTransferHighMax
        oid: *******.4.1.6574.4.4.2.7
        type: Float
        help: greatest settable high voltage transfer point - *******.4.1.6574.4.4.2.7
      - name: upsInputTransferBoostLow
        oid: *******.4.1.6574.4.4.2.8
        type: Float
        help: Low voltage boosting transfer point - *******.4.1.6574.4.4.2.8
      - name: upsInputTransferBoostHigh
        oid: *******.4.1.6574.4.4.2.9
        type: Float
        help: High voltage boosting transfer point - *******.4.1.6574.4.4.2.9
      - name: upsInputTransferTrimLow
        oid: *******.4.1.6574.4.4.2.10
        type: Float
        help: Low voltage trimming transfer point - *******.4.1.6574.4.4.2.10
      - name: upsInputTransferTrimHigh
        oid: *******.4.1.6574.4.4.2.11
        type: Float
        help: High voltage trimming transfer point - *******.4.1.6574.4.4.2.11
      - name: upsInputSensitivity
        oid: *******.4.1.6574.4.4.3
        type: DisplayString
        help: Input power sensitivity - *******.4.1.6574.4.4.3
      - name: upsInputQuality
        oid: *******.4.1.6574.4.4.4
        type: DisplayString
        help: Input power quality - *******.4.1.6574.4.4.4
      - name: upsInputCurrentValue
        oid: *******.4.1.6574.4.4.5.1
        type: Float
        help: Input current (A) - *******.4.1.6574.4.4.5.1
      - name: upsInputCurrentNominal
        oid: *******.4.1.6574.4.4.5.2
        type: Float
        help: Nominal input current (A) - *******.4.1.6574.4.4.5.2
      - name: upsInputFrequencyValue
        oid: *******.4.1.6574.4.4.6.1
        type: Float
        help: Input line frequency (Hz) - *******.4.1.6574.4.4.6.1
      - name: upsInputFrequencyNominal
        oid: *******.4.1.6574.4.4.6.2
        type: Float
        help: Nominal input line frequency (Hz) - *******.4.1.6574.4.4.6.2
      - name: upsInputFrequencyLow
        oid: *******.4.1.6574.4.4.6.3
        type: Float
        help: Input line frequency low (Hz) - *******.4.1.6574.4.4.6.3
      - name: upsInputFrequencyHigh
        oid: *******.4.1.6574.4.4.6.4
        type: Float
        help: Input line frequency high (Hz) - *******.4.1.6574.4.4.6.4
      - name: upsInputFrequencyExtend
        oid: *******.4.1.6574.4.4.6.5
        type: DisplayString
        help: Extended input frequency range - *******.4.1.6574.4.4.6.5
      - name: upsOutputVoltageValue
        oid: *******.4.1.6574.4.5.1.1
        type: Float
        help: Output voltage (V) - *******.4.1.6574.4.5.1.1
      - name: upsOutputVoltageNominal
        oid: *******.4.1.6574.4.5.1.2
        type: Float
        help: Nominal output voltage (V) - *******.4.1.6574.4.5.1.2
      - name: upsOutputFrequencyValue
        oid: *******.4.1.6574.4.5.2.1
        type: Float
        help: Output frequency (Hz) - *******.4.1.6574.4.5.2.1
      - name: upsOutputFrequencyNominal
        oid: *******.4.1.6574.4.5.2.2
        type: Float
        help: Nominal output frequency (Hz) - *******.4.1.6574.4.5.2.2
      - name: upsOutputCurrentValue
        oid: *******.4.1.6574.4.5.3.1
        type: Float
        help: Output current (A) - *******.4.1.6574.4.5.3.1
      - name: upsOutputCurrentNominal
        oid: *******.4.1.6574.4.5.3.2
        type: Float
        help: Nominal output current (A) - *******.4.1.6574.4.5.3.2
      - name: upsAmbientTemperatureValue
        oid: *******.4.1.6574.4.6.1.1
        type: Float
        help: Ambient temperature (degrees C) - *******.4.1.6574.4.6.1.1
      - name: upsAmbientTemperatureAlarm
        oid: *******.4.1.6574.4.6.1.2
        type: DisplayString
        help: Temperature alarm (enabled/disabled) - *******.4.1.6574.4.6.1.2
      - name: upsAmbientTemperatureHigh
        oid: *******.4.1.6574.4.6.1.3
        type: Float
        help: Temperature threshold high (degrees C) - *******.4.1.6574.4.6.1.3
      - name: upsAmbientTemperatureLow
        oid: *******.4.1.6574.4.6.1.4
        type: Float
        help: Temperature threshold low (degrees C) - *******.4.1.6574.4.6.1.4
      - name: upsAmbientTemperatureMax
        oid: *******.4.1.6574.4.6.1.5
        type: Float
        help: Maximum temperature seen (degrees C) - *******.4.1.6574.4.6.1.5
      - name: upsAmbientTemperatureMin
        oid: *******.4.1.6574.4.6.1.6
        type: Float
        help: Minimum temperature seen (degrees C) - *******.4.1.6574.4.6.1.6
      - name: upsAmbientHumidityValue
        oid: *******.4.1.6574.4.6.2.1
        type: Float
        help: Ambient relative humidity (percent) - *******.4.1.6574.4.6.2.1
      - name: upsAmbientHumidityAlarm
        oid: *******.4.1.6574.4.6.2.2
        type: DisplayString
        help: Relative humidity alarm (enabled/disabled) - *******.4.1.6574.4.6.2.2
      - name: upsAmbientHumidityHigh
        oid: *******.4.1.6574.4.6.2.3
        type: Float
        help: Relative humidity threshold high (percent) - *******.4.1.6574.4.6.2.3
      - name: upsAmbientHumidityLow
        oid: *******.4.1.6574.4.6.2.4
        type: Float
        help: Relative humidity threshold low (percent) - *******.4.1.6574.4.6.2.4
      - name: upsAmbientHumidityMax
        oid: *******.4.1.6574.4.6.2.5
        type: Float
        help: Maximum relative humidity seen (percent) - *******.4.1.6574.4.6.2.5
      - name: upsAmbientHumidityMin
        oid: *******.4.1.6574.4.6.2.6
        type: Float
        help: Minimum relative humidity seen (percent) - *******.4.1.6574.4.6.2.6
      - name: upsDriverName
        oid: *******.4.1.6574.4.7.1
        type: DisplayString
        help: Driver name - *******.4.1.6574.4.7.1
      - name: upsDriverVersion
        oid: *******.4.1.6574.4.7.2
        type: DisplayString
        help: Driver version (NUT release) - *******.4.1.6574.4.7.2
      - name: upsDriverVersionData
        oid: *******.4.1.6574.4.7.3
        type: DisplayString
        help: Driver version data - *******.4.1.6574.4.7.3
      - name: upsDriverVersionInternal
        oid: *******.4.1.6574.4.7.4
        type: DisplayString
        help: Internal driver version (if tracked separately) - *******.4.1.6574.4.7.4
      - name: upsDriverPollInterval
        oid: *******.4.1.6574.4.7.5
        type: gauge
        help: Poll interval setup in configuration file - *******.4.1.6574.4.7.5
      - name: upsDriverPort
        oid: *******.4.1.6574.4.7.6
        type: DisplayString
        help: Port setup in configuration file - *******.4.1.6574.4.7.6
      - name: upsDriverPollFrequency
        oid: *******.4.1.6574.4.7.7
        type: gauge
        help: Poll frequency - *******.4.1.6574.4.7.7
      - name: upsDriverProductID
        oid: *******.4.1.6574.4.7.8
        type: DisplayString
        help: Product ID - *******.4.1.6574.4.7.8
      - name: upsDriverSnmpVersion
        oid: *******.4.1.6574.4.7.9
        type: DisplayString
        help: Snmp version - *******.4.1.6574.4.7.9
      - name: upsServerInfo
        oid: *******.4.1.6574.4.8.1
        type: DisplayString
        help: Server information - *******.4.1.6574.4.8.1
      - name: upsServerVersion
        oid: *******.4.1.6574.4.8.2
        type: DisplayString
        help: Server version - *******.4.1.6574.4.8.2
      - name: diskSMARTInfoIndex
        oid: *******.4.1.6574.5.1.1.1
        type: gauge
        help: SMART info index - *******.4.1.6574.5.1.1.1
        indexes:
        - labelname: diskSMARTInfoIndex
          type: gauge
      - name: diskSMARTInfoDevName
        oid: *******.4.1.6574.5.1.1.2
        type: DisplayString
        help: SMART info device name - *******.4.1.6574.5.1.1.2
        indexes:
        - labelname: diskSMARTInfoIndex
          type: gauge
      - name: diskSMARTAttrName
        oid: *******.4.1.6574.5.1.1.3
        type: DisplayString
        help: SMART attribute name - *******.4.1.6574.5.1.1.3
        indexes:
        - labelname: diskSMARTInfoIndex
          type: gauge
      - name: diskSMARTAttrId
        oid: *******.4.1.6574.5.1.1.4
        type: gauge
        help: SMART attribute ID - *******.4.1.6574.5.1.1.4
        indexes:
        - labelname: diskSMARTInfoIndex
          type: gauge
      - name: diskSMARTAttrCurrent
        oid: *******.4.1.6574.5.1.1.5
        type: gauge
        help: SMART attribute current value - *******.4.1.6574.5.1.1.5
        indexes:
        - labelname: diskSMARTInfoIndex
          type: gauge
      - name: diskSMARTAttrWorst
        oid: *******.4.1.6574.5.1.1.6
        type: gauge
        help: SMART attribute worst value - *******.4.1.6574.5.1.1.6
        indexes:
        - labelname: diskSMARTInfoIndex
          type: gauge
      - name: diskSMARTAttrThreshold
        oid: *******.4.1.6574.5.1.1.7
        type: gauge
        help: SMART attribute threshold value - *******.4.1.6574.5.1.1.7
        indexes:
        - labelname: diskSMARTInfoIndex
          type: gauge
      - name: diskSMARTAttrRaw
        oid: *******.4.1.6574.5.1.1.8
        type: gauge
        help: SMART attribute raw value - *******.4.1.6574.5.1.1.8
        indexes:
        - labelname: diskSMARTInfoIndex
          type: gauge
      - name: diskSMARTAttrStatus
        oid: *******.4.1.6574.5.1.1.9
        type: DisplayString
        help: SMART attribute status - *******.4.1.6574.5.1.1.9
        indexes:
        - labelname: diskSMARTInfoIndex
          type: gauge
      - name: serviceInfoIndex
        oid: *******.4.1.6574.6.1.1.1
        type: gauge
        help: Service info index - *******.4.1.6574.6.1.1.1
        indexes:
        - labelname: serviceInfoIndex
          type: gauge
        lookups:
        - labels:
          - serviceInfoIndex
          labelname: serviceName
          oid: *******.4.1.6574.6.1.1.2
          type: DisplayString
        - labels: []
          labelname: serviceInfoIndex
      - name: serviceName
        oid: *******.4.1.6574.6.1.1.2
        type: DisplayString
        help: Service name - *******.4.1.6574.6.1.1.2
        indexes:
        - labelname: serviceInfoIndex
          type: gauge
        lookups:
        - labels:
          - serviceInfoIndex
          labelname: serviceName
          oid: *******.4.1.6574.6.1.1.2
          type: DisplayString
        - labels: []
          labelname: serviceInfoIndex
      - name: serviceUsers
        oid: *******.4.1.6574.6.1.1.3
        type: gauge
        help: Number of users using this service - *******.4.1.6574.6.1.1.3
        indexes:
        - labelname: serviceInfoIndex
          type: gauge
        lookups:
        - labels:
          - serviceInfoIndex
          labelname: serviceName
          oid: *******.4.1.6574.6.1.1.2
          type: DisplayString
        - labels: []
          labelname: serviceInfoIndex
      auth:
        community: synology
kind: ConfigMap
metadata:
  name: snmp-exporter-synology-configmap
  namespace: snmp-exporter
