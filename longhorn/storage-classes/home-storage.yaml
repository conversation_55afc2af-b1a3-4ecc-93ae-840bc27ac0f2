apiVersion: storage.k8s.io/v1
kind: StorageClass
metadata:
  name: home-storage
provisioner: driver.longhorn.io
allowVolumeExpansion: true
parameters:
  numberOfReplicas: "2"
  dataLocality: "best-effort"
  nodeSelector: "storage,home"
  replicaSoftAntiAffinity: "enabled" # Node level anti-affinity
  staleReplicaTimeout: "2880" # 48 hours in minutes
  disableRevisionCounter: "true" # Disable revision counter
