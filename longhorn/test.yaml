---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: longhorn-volv-pvc
spec:
  accessModes:
    - ReadWriteOnce
  storageClassName: home-storage
  resources:
    requests:
      storage: 1Gi

---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: volume-test-deployment
spec:
  strategy:
    type: Recreate
  replicas: 1
  selector:
    matchLabels:
      app: volume-test
  template:
    metadata:
      labels:
        app: volume-test
    spec:
      nodeSelector:
        kubernetes.io/hostname: home05
      containers:
      - name: volume-test
        image: busybox
        imagePullPolicy: IfNotPresent
        command: ['sh', '-c', 'while true; do date >> /data/test.txt; sleep 30; done']
        volumeMounts:
        - name: longhorn-volv-storage
          mountPath: /data
      volumes:
      - name: longhorn-volv-storage
        persistentVolumeClaim:
          claimName: longhorn-volv-pvc
