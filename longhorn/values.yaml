ingress:
  enabled: true
  annotations:
    cert-manager.io/cluster-issuer: letsencrypt-cloudflare-sklenarovi
    nginx.ingress.kubernetes.io/auth-url: "https://oauth.k8s.sklenarovi.cz/oauth2/auth"
    nginx.ingress.kubernetes.io/auth-signin: "https://oauth.k8s.sklenarovi.cz/oauth2/start?rd=$pass_access_scheme://$http_host$escaped_request_uri"
  host: "longhorn.k8s.sklenarovi.cz"
  tls: true
  tlsSecret: "longhorn-k8s-sklenarovi-cz-tls"

persistence:
  defaultClass: false

defaultSettings:
  defaultDataPath: "/data/longhorn/"
  backup-target: "nfs://**************:/volume2/longhorn"
  defaultReplicaCount: 2
  allowCollectingLonghornUsageMetrics: false
  nodeDownPodDeletionPolicy: "delete-both-statefulset-and-deployment-pod"
  # systemManagedComponentsNodeSelector: "location:home"

# User-managed components
# longhornManager:
#  nodeSelector:
#    location: home

# longhornDriver:
#  nodeSelector:
#    location: home

longhornUI:
  nodeSelector:
    location: home
