#!/usr/bin/env python

import argparse
import logging
import logging.handlers
import socket
from datetime import datetime

class RFC5424Formatter(logging.Formatter):
    def __init__(self, msg_format):
        super().__init__(fmt=msg_format, datefmt="%Y-%m-%dT%H:%M:%S.%fZ")

    def format(self, record):
        record.message = record.getMessage()
        #if self.usesTime():
        record.asctime = self.formatTime(record, self.datefmt)
        
        # Format the message in RFC 5424 format
        return f"<{record.levelno}>{1} {record.asctime} {socket.gethostname()} {record.name} {record.process} {record.thread} {record.message}"

# Parse arguments
parser = argparse.ArgumentParser(__file__, description="A syslog message generator")
parser.add_argument("--address", "-a", default="localhost", help="The syslog message recipient address")
parser.add_argument("--port", "-p", type=int, default=5514, help="The syslog message recipient port")
parser.add_argument("--level", "-l", default="DEBUG", help="The syslog message log level")
parser.add_argument("--message", "-m", required=True, help="The syslog message")

def string_to_level(log_level):
    """ Convert a commandline string to a proper log level """
    levels = {
        "CRITICAL": logging.CRITICAL,
        "ERROR": logging.ERROR,
        "WARNING": logging.WARNING,
        "INFO": logging.INFO,
        "DEBUG": logging.DEBUG
    }
    return levels.get(log_level, logging.NOTSET)

if __name__ == "__main__":
    args = parser.parse_args()

    # Set up custom formatter
    formatter = RFC5424Formatter('%(message)s')

    # Configure Syslog handler
    handler = logging.handlers.SysLogHandler(address=(args.address, args.port), facility=19)
    handler.setFormatter(formatter)

    # Configure logger
    syslogger = logging.getLogger('SyslogLogger')
    syslogger.setLevel(string_to_level(args.level))
    syslogger.addHandler(handler)

    # Create a log record
    log_record = logging.LogRecord(name='SyslogLogger', level=string_to_level(args.level), pathname='', lineno=0, msg=args.message, args=(), exc_info=None)
    syslogger.handle(log_record)
