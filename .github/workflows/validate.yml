name: Validate GitOps Repository

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]
  workflow_dispatch:

env:
  KUBERNETES_VERSION: "1.29.0"
  KUSTOMIZE_VERSION: "5.3.0"
  HELM_VERSION: "3.14.0"
  ARGOCD_VERSION: "2.10.0"
  YQ_VERSION: "4.40.5"

jobs:
  yaml-validation:
    name: YAML Syntax Validation
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v5

      - name: Validate YAML files
        uses: ibiqlik/action-yamllint@v3
        with:
          config_file: .yamllint.yml

  kubernetes-validation:
    name: Kubernetes Manifest Validation
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v5

      - name: Install kubectl
        uses: azure/setup-kubectl@v4
        with:
          version: v${{ env.KUBERNETES_VERSION }}

      - name: Install kubeconform
        run: |
          wget https://github.com/yannh/kubeconform/releases/latest/download/kubeconform-linux-amd64.tar.gz
          tar xf kubeconform-linux-amd64.tar.gz
          sudo mv kubeconform /usr/local/bin

      - name: Validate Kubernetes manifests
        run: |
          find . -name '*.yaml' -o -name '*.yml' | \
            grep -E '(deployment|service|ingress|statefulset|daemonset|configmap|secret|pvc|networkpolicy)\.yaml$' | \
            grep -v ".github" | \
            grep -v "/chart/templates/" | \
            grep -v "/charts/" | \
            xargs kubeconform -summary -output json -kubernetes-version ${{ env.KUBERNETES_VERSION }}

  argocd-validation:
    name: ArgoCD Application Validation
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v5

      - name: Install ArgoCD CLI
        run: |
          curl -sSL -o /usr/local/bin/argocd https://github.com/argoproj/argo-cd/releases/download/v${{ env.ARGOCD_VERSION }}/argocd-linux-amd64
          chmod +x /usr/local/bin/argocd

      - name: Install yq
        run: |
          wget https://github.com/mikefarah/yq/releases/download/v${{ env.YQ_VERSION }}/yq_linux_amd64 -O /usr/local/bin/yq
          chmod +x /usr/local/bin/yq

      - name: Validate ArgoCD applications
        run: |
          for app in apps/application-*.yaml; do
            echo "Validating $app"
            # Check required fields
            yq eval '.apiVersion' "$app" | grep -q "argoproj.io/v1alpha1" || exit 1
            yq eval '.kind' "$app" | grep -q "Application" || exit 1
            yq eval '.spec.destination' "$app" > /dev/null || exit 1
            yq eval '.spec.source' "$app" > /dev/null || exit 1

            # Validate source paths exist
            SOURCE_PATH=$(yq eval '.spec.source.path // ""' "$app")
            if [ -n "$SOURCE_PATH" ] && [ "$SOURCE_PATH" != "null" ]; then
              if [ ! -d "$SOURCE_PATH" ]; then
                echo "ERROR: Source path $SOURCE_PATH does not exist for $app"
                exit 1
              fi
            fi
          done

  kustomize-validation:
    name: Kustomize Build Validation
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v5

      - name: Install kustomize
        run: |
          curl -s "https://raw.githubusercontent.com/kubernetes-sigs/kustomize/master/hack/install_kustomize.sh" | \
            bash -s ${{ env.KUSTOMIZE_VERSION }}
          sudo mv kustomize /usr/local/bin/

      - name: Find and build kustomizations
        run: |
          find . -name "kustomization.yaml" -type f | while read -r kustomization; do
            dir=$(dirname "$kustomization")
            echo "Building kustomization in $dir"
            kustomize build "$dir" > /dev/null || exit 1
          done

  helm-validation:
    name: Helm Chart Validation
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v5

      - name: Install Helm
        uses: azure/setup-helm@v4
        with:
          version: v${{ env.HELM_VERSION }}

      - name: Add Helm repositories
        run: |
          # Add common repositories used in the project
          helm repo add bitnami https://charts.bitnami.com/bitnami
          helm repo add prometheus-community https://prometheus-community.github.io/helm-charts
          helm repo add grafana https://grafana.github.io/helm-charts
          helm repo add metallb https://metallb.github.io/metallb
          helm repo add longhorn https://charts.longhorn.io
          helm repo update

      - name: Validate Helm values files
        run: |
          # Find all values.yaml files and attempt to render their parent charts
          for values in $(find . -name "values*.yaml" -not -path "./mutating-webhook/*" -not -path "./.github/*"); do
            dir=$(dirname "$values")
            echo "Checking $values"

            # Skip if it's a Helm chart directory (has Chart.yaml)
            if [ -f "$dir/Chart.yaml" ]; then
              echo "Skipping $values - it's part of a Helm chart"
              continue
            fi

            # Try to determine the chart from the directory name or application file
            chart_name=$(basename "$dir")
            case "$chart_name" in
              grafana|loki-stack|metallb|longhorn)
                echo "Would validate $chart_name with values file $values"
                ;;
            esac
          done

  go-validation:
    name: Go Code Validation
    runs-on: ubuntu-latest
    defaults:
      run:
        working-directory: ./mutating-webhook
    steps:
      - uses: actions/checkout@v5

      - name: Set up Go
        uses: actions/setup-go@v5
        with:
          go-version: '1.25'

      - name: Go mod verify
        run: go mod verify

      - name: Go mod tidy
        run: |
          go mod tidy
          git diff --exit-code go.mod go.sum

      - name: Go vet
        run: go vet ./...

      - name: Go test
        run: go test -v ./...

  security-scanning:
    name: Security Scanning
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v5

      - name: Run Trivy vulnerability scanner
        uses: aquasecurity/trivy-action@master
        with:
          scan-type: 'fs'
          scan-ref: '.'
          format: 'json'
          output: 'trivy-results.json'
          severity: 'CRITICAL,HIGH'
          exit-code: '0'

      - name: Generate security scan summary
        if: always()
        run: |
          echo "## Security Scan Results" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY

          if [ -f trivy-results.json ]; then
            # Count vulnerabilities
            VULN_COUNT=$(jq '[.Results[]?.Vulnerabilities? // [] | length] | add' trivy-results.json || echo "0")
            SECRET_COUNT=$(jq '[.Results[]?.Secrets? // [] | length] | add' trivy-results.json || echo "0")

            echo "### Summary" >> $GITHUB_STEP_SUMMARY
            echo "- Vulnerabilities found: ${VULN_COUNT:-0}" >> $GITHUB_STEP_SUMMARY
            echo "- Secrets found: ${SECRET_COUNT:-0}" >> $GITHUB_STEP_SUMMARY
            echo "" >> $GITHUB_STEP_SUMMARY

            if [ "${SECRET_COUNT:-0}" -gt 0 ]; then
              echo "⚠️ **Warning**: Found ${SECRET_COUNT} secrets in the repository." >> $GITHUB_STEP_SUMMARY
              echo "Please review and consider using GitHub Secrets or external secret management." >> $GITHUB_STEP_SUMMARY
            fi

            if [ "${VULN_COUNT:-0}" -gt 0 ]; then
              echo "⚠️ **Warning**: Found ${VULN_COUNT} vulnerabilities in dependencies." >> $GITHUB_STEP_SUMMARY
            fi
          else
            echo "Security scan completed but no results file found." >> $GITHUB_STEP_SUMMARY
          fi

  network-policy-validation:
    name: Network Policy Validation
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v5

      - name: Validate network policies
        run: |
          echo "Checking network policies..."

          # Check that each namespace in apps has a corresponding network policy
          for app in apps/application-*.yaml; do
            app_name=$(basename "$app" | sed 's/application-//' | sed 's/.yaml//')

            # Skip certain apps that might not need network policies
            case "$app_name" in
              prepare-apps|network-policies|metallb)
                continue
                ;;
            esac

            # Extract namespace from the application file
            namespace=$(grep -A5 "destination:" "$app" | grep "namespace:" | awk '{print $2}' | tr -d '"' || echo "")

            if [ -n "$namespace" ] && [ "$namespace" != "null" ]; then
              policy_file="network-policies/ns_${namespace}.yaml"
              if [ ! -f "$policy_file" ]; then
                echo "WARNING: No network policy found for namespace $namespace (expected at $policy_file)"
              fi
            fi
          done

  renovate-validation:
    name: Renovate Config Validation
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v5

      - name: Validate Renovate config
        uses: suzuki-shunsuke/github-action-renovate-config-validator@v1.1.1
        with:
          config_file_path: 'renovate.json'

  summary:
    name: Validation Summary
    needs: [
      yaml-validation,
      kubernetes-validation,
      argocd-validation,
      kustomize-validation,
      helm-validation,
      go-validation,
      security-scanning,
      network-policy-validation,
      renovate-validation
    ]
    runs-on: ubuntu-latest
    if: always()
    steps:
      - name: Summary
        run: |
          echo "## Validation Summary" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "- yaml-validation: ${{ needs.yaml-validation.result }}" >> $GITHUB_STEP_SUMMARY
          echo "- kubernetes-validation: ${{ needs.kubernetes-validation.result }}" >> $GITHUB_STEP_SUMMARY
          echo "- argocd-validation: ${{ needs.argocd-validation.result }}" >> $GITHUB_STEP_SUMMARY
          echo "- kustomize-validation: ${{ needs.kustomize-validation.result }}" >> $GITHUB_STEP_SUMMARY
          echo "- helm-validation: ${{ needs.helm-validation.result }}" >> $GITHUB_STEP_SUMMARY
          echo "- go-validation: ${{ needs.go-validation.result }}" >> $GITHUB_STEP_SUMMARY
          echo "- security-scanning: ${{ needs.security-scanning.result }}" >> $GITHUB_STEP_SUMMARY
          echo "- network-policy-validation: ${{ needs.network-policy-validation.result }}" >> $GITHUB_STEP_SUMMARY
          echo "- renovate-validation: ${{ needs.renovate-validation.result }}" >> $GITHUB_STEP_SUMMARY
