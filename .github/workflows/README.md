# GitHub Actions CI/CD Pipeline

This directory contains the GitHub Actions workflows for validating the GitOps repository.

## Workflows

### validate.yml

The main validation pipeline that runs on every push and pull request. It includes:

#### 1. YAML Syntax Validation
- Uses yamllint to check all YAML files for syntax errors
- Configured with relaxed line length (150 chars) for Kubernetes manifests

#### 2. Kubernetes Manifest Validation
- Uses kubeconform to validate Kubernetes resources against API schemas
- Checks for deprecated APIs and invalid resource definitions

#### 3. ArgoCD Application Validation
- Validates all ArgoCD Application manifests
- Checks that source paths exist in the repository
- Ensures required fields are present

#### 4. Kustomize Build Validation
- Attempts to build all Kustomize overlays
- Ensures patches and resources are valid

#### 5. Helm Chart Validation
- Validates Helm values files
- Tests chart rendering with provided values

#### 6. Go Code Validation
- Validates the mutating-webhook Go code
- Runs tests, vet, and golangci-lint

#### 7. Security Scanning
- Runs Trivy for vulnerability scanning
- Uses TruffleHog to detect secrets
- Results uploaded to GitHub Security tab

#### 8. Network Policy Validation
- Ensures namespaces have corresponding network policies
- Validates policy syntax

#### 9. Renovate Config Validation
- Validates the Renovate bot configuration

## Required Tools for Local Validation

To run validations locally, install:

```bash
# macOS using Homebrew
brew install yamllint kustomize helm yq
brew install yannh/tap/kubeconform

# Install Go tools (for webhook validation)
go install github.com/golangci/golangci-lint/cmd/golangci-lint@latest
```

## Running Validations Locally

```bash
# Run the local validation script
./scripts/validate-local.sh

# Or run specific validations manually
yamllint .
kustomize build ./path/to/overlay

# Run Go tests
cd mutating-webhook && go test ./...
```

## Extending Validations

To add new validations:

1. Add the job to `.github/workflows/validate.yml`
2. Add it to the `needs` array in the summary job
3. Consider adding it to `scripts/validate-local.sh` for local testing
4. Update this documentation

## Troubleshooting

### Common Issues

1. **Kustomize build failures**: Check that all referenced files exist and patches are valid
2. **ArgoCD validation failures**: Ensure source paths exist and match repository structure
3. **Network policy warnings**: Some system namespaces may not need policies
4. **Resource limit warnings**: Some jobs/cronjobs may not need resource limits

### Skipping Validations

For exceptional cases where validation needs to be skipped:

```yaml
# In your commit message
[skip ci] - Skips all GitHub Actions
```
