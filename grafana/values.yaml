replicas: 1

# Provision grafana-dashboards-kubernetes
dashboardProviders:
  dashboardproviders.yaml:
    apiVersion: 1
    providers:
    - name: 'grafana-dashboards-kubernetes'
      orgId: 1
      folder: 'Kubernetes'
      type: file
      disableDeletion: true
      editable: true
      options:
        path: /var/lib/grafana/dashboards/grafana-dashboards-kubernetes
dashboards:
  grafana-dashboards-kubernetes:
    k8s-system-api-server:
      url: https://raw.githubusercontent.com/dotdc/grafana-dashboards-kubernetes/master/dashboards/k8s-system-api-server.json
      token: ''
    k8s-system-coredns:
      url: https://raw.githubusercontent.com/dotdc/grafana-dashboards-kubernetes/master/dashboards/k8s-system-coredns.json
      token: ''
    k8s-views-global:
      url: https://raw.githubusercontent.com/dotdc/grafana-dashboards-kubernetes/master/dashboards/k8s-views-global.json
      token: ''
    k8s-views-namespaces:
      url: https://raw.githubusercontent.com/dotdc/grafana-dashboards-kubernetes/master/dashboards/k8s-views-namespaces.json
      token: ''
    k8s-views-nodes:
      url: https://raw.githubusercontent.com/dotdc/grafana-dashboards-kubernetes/master/dashboards/k8s-views-nodes.json
      token: ''
    k8s-views-pods:
      url: https://raw.githubusercontent.com/dotdc/grafana-dashboards-kubernetes/master/dashboards/k8s-views-pods.json
      token: ''

service:
  enabled: true
  type: ClusterIP

nodeSelector:
  location: oracle

ingress:
  enabled: true
  # Values can be templated
  annotations:
    cert-manager.io/cluster-issuer: letsencrypt-cloudflare-sklenarovi
    nginx.ingress.kubernetes.io/auth-url: "https://oauth.k8s.sklenarovi.cz/oauth2/auth"
    nginx.ingress.kubernetes.io/auth-signin: "https://oauth.k8s.sklenarovi.cz/oauth2/start?rd=$pass_access_scheme://$http_host$escaped_request_uri"
#    nginx.ingress.kubernetes.io/configuration-snippet: |
#      auth_request_set $user   $upstream_http_x_auth_request_user;
#      auth_request_set $email  $upstream_http_x_auth_request_email;
#      proxy_set_header X-User  $user;
#      proxy_set_header X-Email $email;
  labels: {}
  path: /

  # pathType is only for k8s >= 1.1=
  pathType: Prefix

  hosts:
    - grafana.k8s.sklenarovi.cz
  tls:
    - secretName: grafana-k8s-sklenarovi-cz-tls
      hosts:
        - "grafana.k8s.sklenarovi.cz"

# # Enable persistence using Persistent Volume Claims
# # ref: http://kubernetes.io/docs/user-guide/persistent-volumes/
# #
persistence:
  type: pvc
  enabled: true
  existingClaim: grafana-lh

# Administrator credentials when not using an existing secret (see below)
adminUser: admin
adminPassword: hes123Lo

# # Pass the plugins you want installed as a list.
# #
plugins:
  []
  # - digrich-bubblechart-panel
  # - grafana-clock-panel

# grafana.ini:
#  auth:
#    oauth_auto_login: true
#    # signout_redirect_url: "https://login.dev-hub.esc.esetrs.cz/oauth2/sign_out?rd=https%3A%2F%2Flogin.microsoftonline.com%2F01f7e0e8-c680-4293-8068-d572231a88f4%2Foauth2%2Fv2.0%2Flogout"
#  auth.proxy:
#    enabled: true
#    header_name: X-Auth-Request-Email
#    header_property: email
#    auto_sign_up: true
#  users:
#    allow_sign_up: false
#    auto_assign_org: true
#    auto_assign_org_role: Admin
#  security:
#    allow_embedding: true

grafana.ini:
  auth:
    disable_login_form: true
    disable_signout_menu: true
  auth.anonymous:
    enabled: true
    org_role: Admin
    org_name: Main Org.
  security:
    allow_embedding: true
  users:
    allow_sign_up: false
    auto_assign_org: true
    auto_assign_org_role: Admin
