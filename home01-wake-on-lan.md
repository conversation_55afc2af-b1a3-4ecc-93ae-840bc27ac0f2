Table of Contents

-   [What Is Wake-on-LAN?](https://www.golinuxcloud.com/wake-on-lan-ubuntu/#What_Is_Wake-on-LAN "What Is Wake-on-LAN?")
-   [Does you Network Card support Wake on Lan?](https://www.golinuxcloud.com/wake-on-lan-ubuntu/#Does_you_Network_Card_support_Wake_on_Lan "Does you Network Card support Wake on Lan?")
-   [Enable Wake on Lan Feature](https://www.golinuxcloud.com/wake-on-lan-ubuntu/#Enable_Wake_on_Lan_Feature "Enable Wake on Lan Feature")
-   [Auto Wake-On-Lan Activation at Startup](https://www.golinuxcloud.com/wake-on-lan-ubuntu/#Auto_Wake-On-Lan_Activation_at_Startup "Auto Wake-On-Lan Activation at Startup")
-   [Remote Ubuntu Server Wake Up](https://www.golinuxcloud.com/wake-on-lan-ubuntu/#Remote_Ubuntu_Server_Wake_Up "Remote Ubuntu Server Wake Up")
-   [Summary](https://www.golinuxcloud.com/wake-on-lan-ubuntu/#Summary "Summary")
-   [References](https://www.golinuxcloud.com/wake-on-lan-ubuntu/#References "References")

What Is Wake-on-LAN?
--------------------

Wake-on-LAN is a feature that allows you to turn on your computer from another device over the network. It does this by sending a data packet. The receiving computer's network driver processes this data packet and wakes the computer.

In this article we will tell you how to enable Wake-on-LAN in Ubuntu and example usage.

Does you Network Card support Wake on Lan?
------------------------------------------

Your ethernet card must support this feature for Wake-on-Lan to work. To find out if your Ethernet card supports this feature, you must first learn the name of your Ethernet interface. In the following sections, it is recommended to register the mac address for packet sending.

In Terminal, the ip address and other information are displayed with the following command:

```bash

foc@ubuntu22:~$ ip a
```
```bash

1: lo: <LOOPBACK,UP,LOWER_UP> mtu 65536 qdisc noqueue state UNKNOWN group default qlen 1000
    link/loopback 00:00:00:00:00:00 brd 00:00:00:00:00:00
    inet 127.0.0.1/8 scope host lo
       valid_lft forever preferred_lft forever
    inet6 ::1/128 scope host
       valid_lft forever preferred_lft forever
2: enp2s0: <BROADCAST,MULTICAST,UP,LOWER_UP> mtu 1500 qdisc fq_codel state UP group default qlen 1000
    link/ether 30:5a:3a:0d:ac:0d brd ff:ff:ff:ff:ff:ff
    inet ************/24 brd ************* scope global dynamic noprefixroute enp2s0
       valid_lft 67502sec preferred_lft 67502sec
    inet6 fe80::40ab:d219:c23e:abfb/64 scope link noprefixroute
       valid_lft forever preferred_lft forever
```
Mac address: 30:5a:3a:0d:ac:0d and enp2s0 is the computer's network interface name. To view and change the Wake-On-Lan settings, the "ethtool" package must be installed:

```bash

foc@ubuntu22:~$ sudo apt install ethtool -y
```
Next, find out if the network card supports wake-on-LAN:

```bash

foc@ubuntu22:~$ sudo ethtool enp2s0
```
```bash

Settings for enp2s0:
...
 Duplex: Full
 Auto-negotiation: on
 master-slave cfg: preferred slave
 master-slave status: slave
 Port: Twisted Pair
 PHYAD: 0
 Transceiver: external
 MDI-X: Unknown
 Supports Wake-on: pumbg
 Wake-on: d
 Link detected: yes
```
The expression "`Wake-on:d`" indicates that the wake-on-lan feature of the network card is supported but deactivated.

[ALSO READ:Simple TRICKs to check size of hidden files in Linux](https://www.golinuxcloud.com/check-size-of-hidden-files-linux/)

Enable Wake on Lan Feature
--------------------------

Run the following commands to enable wake-on-lan on your network card:

```bash

foc@ubuntu22:~$ sudo ethtool -s enp2s0 wol g
```
```bash

Settings for enp2s0:
...
 Speed: 1000Mb/s
 Duplex: Full
 Auto-negotiation: on
 master-slave cfg: preferred slave
 master-slave status: slave
 Port: Twisted Pair
 PHYAD: 0
 Transceiver: external
 MDI-X: Unknown
 Supports Wake-on: pumbg
 Wake-on: g
 Link detected: yes
```
Some motherboard manufacturers require you to change the settings in the BIOS to enable this feature. If there is no change when you check after entering the command, it is recommended to look at the BIOS settings.

Auto Wake-On-Lan Activation at Startup
--------------------------------------

If the Wake-on-Lan settings are deactivated when the server is restarted; you should solve this problem with systemd.

Create systemd service:

```bash

foc@ubuntu22:~$ sudo --preserve-env systemctl edit --force --full wol-enable.service
```
```bash

[Unit]
Description=Enable Wake-up on LAN

[Service]
Type=oneshot
ExecStart=/sbin/ethtool -s enp2s0   wol g

[Install]
WantedBy=basic.target
```
Replace the enp2s0 value with your own network interface value. Then reload and enable the service:

```bash

foc@ubuntu22:~$ sudo systemctl daemon-reload

foc@ubuntu22:~$ sudo systemctl enable wol-enable.service
Created symlink /etc/systemd/system/basic.target.wants/wol-enable.service → /etc/systemd/system/wol-enable.service.
```
Remote Ubuntu Server Wake Up
----------------------------

The above steps were the steps to do for Ubuntu to wake up. Now, let's talk about the steps to be done on the server that will do the wake-up task.

[ALSO READ:How to create, open, find, remove dashed filename in Linux](https://www.golinuxcloud.com/overview-```bash-dashed-filename-directory-linux/)

One of the following package must be installed on the server in question:

```bash

foc@ubuntu22:~$ sudo apt search wakeonlan
```
wakeonlan/jammy 0.41-12.1 all
  Sends 'magic packets' to wake-on-LAN enabled ethernet adapters

Install wakeonlan package:

```bash

foc@ubuntu22:~$ sudo apt  install wakeonlan -y
```
Wake up remote server using this package:

```bash

foc@ubuntu22:~$ sudo wakeonlan 30:5a:3a:0d:ac:0d
Sending magic packet to ***************:9 with 30:5a:3a:0d:ac:0d
```
Remote server mac address should be written after wakeonlan command.

Summary
-------

In this article we have explained the steps for remote wake up Ubuntu server remote. The manual page about what you can do with wakeonlan will help:

```bash

foc@ubuntu22:~$ man wakeonlan
```
```bash

NAME
       wakeonlan - Perl script to wake up computers
SYNOPSIS
       wakeonlan [-h] [-v] [-i IP_address] [-p port] [-f file]
       [[hardware_address] ...]
...
EXAMPLES
       Using the limited broadcast address (***************):

           $ wakeonlan 01:02:03:04:05:06
           $ wakeonlan 01:02:03:04:05:06 01:02:03:04:05:07

       Using a subnet broadcast address:

           $ wakeonlan -i ************* 01:02:03:04:05:06

       Using another destination port:

           $ wakeonlan -i ************* -p 1234 01:02:03:04:05:06

       Using a file as source of hardware and IP addresses:

           $ wakeonlan -f examples/lab001.wol
           $ wakeonlan -f examples/lab001.wol 01:02:03:04:05:06
```
References
----------

ubuntu-mate.community - [Wake on LAN Persistance Issues 22.04](https://ubuntu-mate.community/t/wake-on-lan-persistance-issues-22-04/25600)